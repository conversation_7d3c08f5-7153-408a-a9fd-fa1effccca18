# HRV Detector 本地开发文档

## 项目概述

HRV Detector 是一个心率变异分析系统的采集端本地服务，主要用于采集和分析心率变异数据。

## 项目架构

项目采用模块化架构设计，包含以下核心模块：

### 模块说明

1. **hrvdetector**
   - 主模块，包含系统的启动入口
   - 负责系统的整体配置和运行时环境
   - 集成Spring Boot Web框架，提供RESTful API接口

2. **hrvdetector-common**
   - 通用工具模块
   - 提供各个模块共用的工具类、常量、异常定义等
   - 不依赖于其他业务模块，被其他模块依赖

3. **hrvdetector-service**
   - 服务实现模块
   - 包含核心业务逻辑的实现
   - 集成JPA进行数据持久化
   - 依赖common模块

4. **hrvdetector-comm**
   - 模块通信模块
   - 负责与外部系统的通信
   - 处理数据传输和协议转换

## 技术栈

- **Java版本**：JDK 17
- **构建工具**：Maven
- **主框架**：Spring Boot 3.4.3
- **数据库**：PostgreSQL
- **ORM框架**：Spring Data JPA
- **数据库版本管理**：Liquibase
- **测试框架**：JUnit 5.11.0

## 开发环境配置

### 1. 基础环境要求

- JDK 17或以上版本
- Maven 3.6或以上版本
- PostgreSQL 12或以上版本

### 2. 数据库配置

```properties
spring.datasource.url=*********************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
```

### 3. 项目构建

```bash
# 克隆项目后，在项目根目录执行
mvn clean install
```

### 4. 运行应用

```bash
# 在hrvdetector模块目录下执行
mvn spring-boot:run
```

## 模块依赖关系

```
hrvdetector
├── hrvdetector-service
│   └── hrvdetector-common
├── hrvdetector-comm
│   └── hrvdetector-common
└── hrvdetector-common
```

## 开发规范

1. **代码风格**
   - 遵循Java标准编码规范
   - 使用4空格缩进
   - 类名使用UpperCamelCase风格
   - 方法名使用lowerCamelCase风格

2. **提交规范**
   - 提交信息需要清晰描述改动内容
   - 每次提交保持功能的单一性

## 数据库管理

1. **生成变更日志**
```bash
mvn liquibase:generateChangeLog
```

2. **应用变更**
```bash
# 应用所有未执行的变更
mvn liquibase:update

# 应用指定数量的变更
mvn liquibase:update -Dliquibase.count=1
```

3. **回滚变更**
```bash
# 回滚到指定版本
mvn liquibase:rollback -Dliquibase.rollbackTag=version_1.0

# 回滚指定数量的变更
mvn liquibase:rollback -Dliquibase.rollbackCount=1
```

4. **变更状态**
```bash
# 查看待执行的变更列表
mvn liquibase:status

# 查看已执行的变更历史
mvn liquibase:history
```

## 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务是否启动
   - 验证数据库用户名和密码是否正确
   - 确认数据库hrv_detector是否已创建

2. **应用启动失败**
   - 检查JDK版本是否符合要求
   - 确认所有依赖是否正确安装
   - 查看日志确认具体错误信息

## 参考文档

- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [Spring Data JPA文档](https://spring.io/projects/spring-data-jpa)
- [Liquibase文档](https://docs.liquibase.com/)