# 心率变异分析系统
## 需求分析
```https://sl228djn0q.feishu.cn/docx/QyoUdw7X7oX0OuxNklVc2NEinIV```
## HRV心率变异功能【设计】
```https://lanhuapp.com/link/#/invite?sid=qXyjImx7```
## 研发会议纪要
```https://sl228djn0q.feishu.cn/docx/VSXqdagjjo76mQxFWx7cmGmsnLd```
## HRV平台端【PRD】
```https://sl228djn0q.feishu.cn/docx/Tkfzd0sPRoxXiJxTlDMcUiAknsd```
## HRV采集端【PRD】
```https://sl228djn0q.feishu.cn/docx/ORnPd9W9NoOs4dx5pJ8cBu1un2g```
## HRV测评端【PRD】
```https://sl228djn0q.feishu.cn/docx/OL8idVm2ooiiFlxFUj8c4ET6nne```
## HRV平台端【UI】
```https://mastergo.com/file/153214522562086?fileOpenFrom=home&page_id=M```
## HRV采集端【UI】
```https://mastergo.com/file/152680229153018?fileOpenFrom=home&page_id=M```
## HRV测评端【UI】
```https://mastergo.com/file/151415945864502?fileOpenFrom=project&page_id=M&shareId=151415945864502```

## Ubuntu环境下打包
- 在微软商店安装Ubuntu
- 通过wsl命令打开Ubuntu
- 安装nodejs（通过nvm安装-避免权限问题）
```javascript
// 安装 nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
source ~/.bashrc
// 安装 nodejs
nvm install --lts
nvm use --lts
// 若存在以下windows环境下下载的依赖，需要删除（可选）
rm -rf node_modules package-lock.json
npm cache clean --force
// 安装npm依赖
npm install
// 安装打包依赖
npm install electron-installer-debian --save-dev
npm install electron-packager --save-dev
// 打包资源文件
npm run dist
// 打包deb文件
npm run deb64
```

## electron-forge 打包报错 
- This may be due to a lack of SYSV IPC support.fakeroot: error while starting the `faked' daemon.——解决办法
```javascript
// WSL1 不支持完整的 SYSV IPC，而 WSL2 支持。
wsl --set-version Ubuntu 2  // 将 Ubuntu 转换为 WSL2
wsl --shutdown             // 重启 WSL
```

- make打包报错Error: Could not find any make targets configured for the "win32" platform.——解决办法
```javascript
npm install --save-dev @electron-forge/cli
npx electron-forge import
```

- The current umask, 0, is not supported. You should use 0022 or 0002.——解决办法
```javascript
// 更改umask权限
sudo umask 0002
```
- 打包后运行报错：fakeroot, while creating message channels: Function not implemented
This may be due to a lack of SYSV IPC support.
fakeroot: error while starting the `faked' daemon.
```javascript
// 1. 设置默认使用 fakeroot-tcp
sudo update-alternatives --set fakeroot /usr/bin/fakeroot-tcp

// 2. 确保 IPC 可用
echo "tmpfs /dev/shm tmpfs defaults,size=512M 0 0" | sudo tee -a /etc/fstab
sudo mount -a

// 3. 添加环境变量到 ~/.bashrc
echo "export FAKEROOTDONTTRYCHOWN=1" >> ~/.bashrc
source ~/.bashrc

// 测试 fakeroot 基础功能
fakeroot /bin/bash -c 'whoami; touch testfile; ls -l testfile'

// 检查 IPC 状态
ipcs -m
```

- 在使用 electron-packager 打包 Electron 应用时，可以通过 --arch 参数指定目标 CPU 架构

```javascript
ia32: 32位 x86 架构
x64: 64位 x86 架构（默认值）
armv7l: ARM 32位架构（如 Raspberry Pi 2/3）
arm64: ARM 64位架构（如 Raspberry Pi 4）
universal: macOS 通用二进制（x64 + arm64）（同时支持 Intel 和 Apple Silicon）
```

- electron-installer-debian 是一个用于为 Electron 应用创建 Debian 安装包(.deb)的工具。其中的 --arch 参数用于指定目标架构

```javascript
i386: 32位 x86 架构
amd64: 64位 x86 架构（默认值）
armv7l: ARM 32位架构（如 Raspberry Pi 2/3）
arm64: ARM 64位架构（如 Raspberry Pi 4）
all: 通用架构（适用于纯文本或架构无关的包）
```

## npm加载报错
- unable to resolve dependency tree——解决办法
```javascript
npm install --legacy-peer-deps
```

- error while loading shared libraries: libnss3.so: cannot open shared object file
```javascript
// 这个错误表明 Electron 运行时缺少依赖的共享库 libnss3.so，这是 Linux 系统上的一个基础加密/安全库
sudo apt update
sudo apt install libnss3 libgtk-3-0 libxss1 libasound2
```
-  FATAL:setuid_sandbox_host.cc(163)] The SUID sandbox helper binary was found, but is not configured correctly. Rather than run without sandboxing I'm aborting now
```javascript
// 这个错误是由于 Electron 的 SUID sandbox（沙盒） 配置不正确导致的。沙盒是 Chromium（Electron 底层使用的浏览器引擎）的安全机制，用于隔离进程权限
npm run start --no-sandbox
// 或者
const { app } = require('electron');
app.commandLine.appendSwitch('no-sandbox');

// ⚠️ 注意：禁用沙盒会降低安全性，仅限开发调试使用。在生产环境中，应保持沙盒启用。故建议如下操作：
cd ./node_modules/electron/dist // 进入 Electron 的安装目录
sudo chown root chrome-sandbox
sudo chmod 4755 chrome-sandbox
// 或者 禁用 SUID sandbox（但仍启用普通沙盒）
npm run start --disable-setuid-sandbox
```

