const packager = require('electron-packager');
// 配置选项
const options = {
    dir: '.', // 项目目录
    out: 'dist', // 输出目录
    overwrite: true, // 覆盖输出目录
    asar: true, // 不使用 asar 打包
    platform: 'linux', // 打包平台
    arch: 'x64', // 打包架构
    icon: './favicon.ico', // 应用图标路径
};

// 调用 packager
packager(options).then(appPaths => {
    console.log(`应用已打包到：${appPaths}`);
})
.catch(err => {
    console.error('打包时出错：', err);
});