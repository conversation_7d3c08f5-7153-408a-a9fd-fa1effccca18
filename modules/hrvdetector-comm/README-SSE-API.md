# SSE API 文档

## 概述

本文档描述了HRV检测系统中的SSE（Server-Sent Events）接口。系统提供两个SSE接口用于实时推送波形数据和导联状态数据。

## 连接机制

### 连接超时和重连
- SSE连接默认超时时间为60秒
- 系统支持自动重连机制，最大重试次数为3次，重试间隔为1秒
- 连接可以通过客户端主动关闭或服务端超时自动关闭

### 错误处理
- 连接错误：当连接断开时，客户端会收到error事件
- 数据解析错误：客户端应该处理JSON解析异常
- 网络错误：在网络不稳定时自动尝试重连

### 最佳实践
- 在组件卸载时主动关闭SSE连接以释放资源
- 实现指数退避重连策略，避免频繁重连
- 添加适当的错误日志记录
- 在重连前检查网络状态
- 维护连接状态计数器，超过最大重试次数后通知用户
- 实现数据缓冲机制，避免数据处理过载

## 接口详情

### 1. 波形数据订阅接口

**请求URL：** `/sse/waveform`

**请求方法：** GET

**Content-Type：** text/event-stream

**响应格式：**
```javascript
data: {
    // 波形数据结构
    "timestamp": "2024-01-01T12:00:00.000Z",  // 数据时间戳
    "ecgLeadI": 123,       // 心电通道I波形数据
    "ecgLeadII": 456,      // 心电通道II波形数据
    "ecgLeadV1": 789,      // 心电通道V1波形数据
    "respWave": 321,       // 呼吸波形数据
    "rWavePosition": 100,   // R波位置
    "tWavePosition": 150,   // T波位置
    "paceDetected": false,  // 是否检测到起搏
    "rWaveDetected": true,  // 是否检测到R波
    "tWaveDetected": true   // 是否检测到T波
}
```

### 2. 导联状态订阅接口

**请求URL：** `/sse/ecg-lead-status`

**请求方法：** GET

**Content-Type：** text/event-stream

**响应格式：**
```javascript
data: {
    // 导联状态数据结构
    "timestamp": "2024-01-01T12:00:00.000Z",
    "fiveLeadMode": true,      // 是否为5导联模式
    "twelveLeadMode": false,   // 是否为12导联模式
    // 电极连接状态
    "raDisconnected": false,   // RA电极是否断开
    "laDisconnected": false,   // LA电极是否断开
    "llDisconnected": false,   // LL电极是否断开
    "rlDisconnected": false,   // RL电极是否断开
    "v1Disconnected": false,   // V1电极是否断开
    "v2Disconnected": false,   // V2电极是否断开
    "v3Disconnected": false,   // V3电极是否断开
    "v4Disconnected": false,   // V4电极是否断开
    "v5Disconnected": false,   // V5电极是否断开
    "v6Disconnected": false,   // V6电极是否断开
    // 通道信号状态
    "channelINoSignal": false,  // 通道I信号是否不存在
    "channelIINoSignal": false, // 通道II信号是否不存在
    "channelV1NoSignal": false, // 通道V1信号是否不存在
    "channelV2NoSignal": false, // 通道V2信号是否不存在
    "channelV3NoSignal": false, // 通道V3信号是否不存在
    "channelV4NoSignal": false, // 通道V4信号是否不存在
    "channelV5NoSignal": false, // 通道V5信号是否不存在
    "channelV6NoSignal": false  // 通道V6信号是否不存在
}
```

## 客户端示例代码

```javascript
// 连接波形数据SSE
const waveformSource = new EventSource('/sse/waveform');
waveformSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    // 处理波形数据
    console.log('Received waveform data:', data);
};

// 连接导联状态SSE
const leadStatusSource = new EventSource('/sse/ecg-lead-status');
leadStatusSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    // 处理导联状态数据
    console.log('Received lead status:', data);
};

// 错误处理
waveformSource.onerror = function(error) {
    console.error('Waveform SSE error:', error);
    // 可以在这里实现重连逻辑
};

// 关闭连接
function closeConnections() {
    waveformSource.close();
    leadStatusSource.close();
}
```

## 注意事项

1. 建议在组件卸载时关闭SSE连接以释放资源
2. 客户端应实现适当的错误处理和重连机制
3. 由于SSE是单向通信机制，如需双向通信请考虑使用WebSocket
4. 数据推送频率取决于服务端的实现，客户端应做好相应的性能优化