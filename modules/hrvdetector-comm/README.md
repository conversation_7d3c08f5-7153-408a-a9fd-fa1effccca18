# 通信模块包结构说明

## 包结构设计

```
com.cqqy.hrvas.comm
├── config          # 配置相关
│   └── CommConfiguration.java
├── port           # 串口管理
│   └── SerialPortManager.java
├── protocol       # 协议相关
│   ├── Frame.java
│   ├── FrameHandler.java
│   └── ...
├── service        # 业务服务
│   └── ...
└── model          # 数据模型
    └── ...
```

## 包说明

### config包
- 存放所有配置相关的类
- 包括串口配置、协议配置等

### port包
- 负责串口的管理
- 包括串口的打开、关闭、数据收发等基础功能

### protocol包
- 存放协议相关的类
- 包括帧定义、帧处理、帧解析等

### service包
- 存放具体业务服务实现
- 负责业务逻辑的处理

### model包
- 存放数据模型类
- 包括请求响应对象等