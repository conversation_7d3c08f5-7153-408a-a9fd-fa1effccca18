package com.cqqy.hrvas.comm.config;

import java.lang.reflect.Constructor;
import java.util.Arrays;
import java.util.Comparator;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.type.filter.AssignableTypeFilter;
import org.springframework.beans.factory.config.BeanDefinition;

import lombok.extern.slf4j.Slf4j;

import com.cqqy.hrvas.comm.m101.FrameContext;
import com.cqqy.hrvas.comm.m101.FrameDispatcher;
import com.cqqy.hrvas.comm.m101.FrameHandler;
import com.cqqy.hrvas.comm.m101.FrameSender;
import com.cqqy.hrvas.comm.JSerialCommPortManager;
import com.cqqy.hrvas.comm.SerialPortManager;

@Slf4j
@Configuration
public class M101Configuration implements ApplicationContextAware {
    private ApplicationContext applicationContext;

    @Bean
    public FrameContext frameContext() {
        return new FrameContext();
    }
    
    @Bean
    public SerialPortManager serialPortManager() {
        return new JSerialCommPortManager();
    }

    @Bean
    public FrameSender frameSender() {
        return new FrameSender(serialPortManager(), frameContext());
    }

    @DependsOn("sseController")
    @Bean
    public FrameDispatcher frameDispatcher() {
        FrameDispatcher dispatcher = new FrameDispatcher(serialPortManager(), frameContext());
        registerFrameHandlers(dispatcher);
        return dispatcher;
    }

    /**
     * 自动扫描并注册所有FrameHandler实现类
     * @param dispatcher 帧分发器
     */
    private void registerFrameHandlers(FrameDispatcher dispatcher) {
        ClassPathScanningCandidateComponentProvider scanner =
            new ClassPathScanningCandidateComponentProvider(false);
        scanner.addIncludeFilter(new AssignableTypeFilter(FrameHandler.class));

        for (BeanDefinition bd : scanner.findCandidateComponents("com.cqqy.hrvas.comm.m101.handler")) {
            try {
                Class<?> clazz = Class.forName(bd.getBeanClassName());
                // 获取所有构造函数并按参数数量排序
                Constructor<?>[] constructors = clazz.getConstructors();
                Constructor<?> maxParamsConstructor = Arrays.stream(constructors)
                    .max(Comparator.comparingInt(Constructor::getParameterCount))
                    .orElseThrow(() -> new NoSuchMethodException("No public constructors found"));

                // 获取构造函数参数类型
                Class<?>[] paramTypes = maxParamsConstructor.getParameterTypes();
                Object[] params = new Object[paramTypes.length];

                // 根据参数类型提供依赖
                for (int i = 0; i < paramTypes.length; i++) {
                    if (paramTypes[i].equals(FrameContext.class)) {
                        params[i] = frameContext();
                    // } else if (paramTypes[i].equals(SerialPortManager.class)) {
                    //     params[i] = serialPortManager();
                    } else if (paramTypes[i].equals(FrameDispatcher.class)) {
                        params[i] = dispatcher;
                    } else {
                        // 其他依赖类型
                        params[i] = applicationContext.getBean(paramTypes[i]);
                        System.out.println(paramTypes[i]);
                    }
                }

                FrameHandler handler = (FrameHandler) maxParamsConstructor.newInstance(params);
                dispatcher.registerHandler(handler);
                log.debug("已注册FrameHandler: {} (使用{}个参数的构造函数)",
                    bd.getBeanClassName(), paramTypes.length);
            } catch (Exception e) {
                log.error("注册FrameHandler失败: " + bd.getBeanClassName(), e);
            }
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}