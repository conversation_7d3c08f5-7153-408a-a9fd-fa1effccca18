package com.cqqy.hrvas.comm.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.cqqy.hrvas.comm.JSerialCommPortManager;
import com.cqqy.hrvas.comm.SerialPortManager;
import com.cqqy.hrvas.comm.m101.FrameDataListener;
import com.cqqy.hrvas.comm.m101.FrameDispatcher;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class M101Initializer {
    @Value("${hrvdetector.comm.m10x.port:/dev/cu.usbserial}")
    private String portName;

    @Value("${hrvdetector.comm.m10x.maxRetries:3}")
    private int maxRetries;

    @Value("${hrvdetector.comm.m10x.retryInterval:2000}")
    private int retryInterval;

    private final int baudRate;
    private final int dataBits;
    private final int stopBits;
    private final int parity;

    private final SerialPortManager serialPortManager;
    private final FrameDispatcher frameDispatcher;

    public M101Initializer(SerialPortManager serialPortManager, FrameDispatcher frameDispatcher) {
        this.serialPortManager = serialPortManager;
        this.frameDispatcher = frameDispatcher;
        this.baudRate = 115200;
        this.dataBits = 8;
        this.stopBits = 1;
        this.parity = 0;

    }

    @PostConstruct
    public void init() {
        JSerialCommPortManager portManager = (JSerialCommPortManager) serialPortManager;
        portManager.addDataListener(new FrameDataListener(frameDispatcher));

        boolean opened = false;
        for (int i = 0; i < maxRetries; i++) {
            opened = portManager.openPort(portName, baudRate, dataBits, stopBits, parity);
            if (opened) {
                log.info("串口 {} 已成功打开，波特率 {}", portName, baudRate);
                break;
            }

            log.warn("无法打开串口 {} (尝试 {}/{})", portName, i+1, maxRetries);
            try {
                Thread.sleep(retryInterval);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("串口打开重试被中断");
                break;
            }
        }

        if (!opened) {
            log.error("无法打开串口 {}，已达到最大重试次数 {}", portName, maxRetries);
        }
    }
}