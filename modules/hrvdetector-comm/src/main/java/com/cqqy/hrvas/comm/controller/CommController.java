package com.cqqy.hrvas.comm.controller;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import com.cqqy.hrvas.comm.service.CommService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.cqqy.hrvas.comm.m101.FrameContext;
import com.cqqy.hrvas.comm.m101.FrameSender;
import com.cqqy.hrvas.comm.m101.frame.CommandId;
import com.cqqy.hrvas.comm.m101.frame.Frame;
import com.cqqy.hrvas.comm.m101.frame.FrameBuilder;
import com.cqqy.hrvas.comm.m101.model.ModuleInfoResponse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 模块信息控制器
 * 用于发送模块信息查询命令
 */
@Tag(name = "comm-通信")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/comm")
public class CommController {

    private final FrameSender frameSender;

    private final FrameContext frameContext;

    private final CommService commService;

    /**
     * 查询模块信息
     * @return 包含模块信息的响应实体
     */
    @GetMapping("/moduleInfo")
    public ResponseEntity<?> getModuleInfo() {
        try {
            Frame frame = FrameBuilder.dcFrameBuilder(frameContext)
                .withFrameId(CommandId.QUERY_MODULE_INFO)
                .build();

            CompletableFuture<Frame> future = frameSender.sendFrameWithRetries(frame, 3);
            Frame responseFrame = future.get();

            ModuleInfoResponse response = ModuleInfoResponse.fromBytes(responseFrame.getData());
            return ResponseEntity.ok(response);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return ResponseEntity.status(503).body("Service unavailable");
        } catch (ExecutionException e) {
            return ResponseEntity.internalServerError().body("Failed to query module info");
        }
    }

    /**
     * 启动/停止心电校准
     * @param calibration 0-启动 1-停止
     * @return boolean
     */
    @Operation(summary = "启动/停止心电校准 0-启动 1-停止")
    @GetMapping("/calibration")
    public boolean setCalibration(@RequestParam(name = "calibration") int calibration) throws ExecutionException, InterruptedException {
        return commService.ecgCalibration(calibration);
    }

    /**
     * 在线升级
     * @return boolean
     */
    @Operation(summary = "心电模块在线升级")
    @GetMapping("/onlineUpgrade")
    public boolean onlineUpgrade() throws ExecutionException, InterruptedException {
        return commService.onlineUpgrade();
    }
}