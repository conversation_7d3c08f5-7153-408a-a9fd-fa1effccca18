package com.cqqy.hrvas.comm.controller;

import com.cqqy.hrvas.annotation.Permission;
import com.cqqy.hrvas.comm.service.sse.SseEmitterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@Tag(name = "comm-SSE")
@Slf4j
@RestController
@RequestMapping("/api/sse")
public class SseController {
    private final SseEmitterService sseEmitterService;

    public SseController(SseEmitterService sseEmitterService) {
        this.sseEmitterService = sseEmitterService;
    }

    /**
     * 创建SSE连接用于接收波形数据
     * @return SseEmitter对象用于持续推送数据
     */
    @Permission("detector:sse:waveform")
    @Operation(summary = "创建SSE连接用于接收波形数据")
    @GetMapping(value = "/waveform", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamWaveformData() {
        String clientId = UUID.randomUUID().toString();
        SseEmitter emitter = sseEmitterService.createEmitter(clientId, SseEmitterService.WAVEFORM_SUBSCRIPTION);
        log.debug("Created new SSE connection for client {} with subscription {}, active connections: {}", 
            clientId, SseEmitterService.WAVEFORM_SUBSCRIPTION, sseEmitterService.getActiveEmitterCount());
        return emitter;
    }

    /**
     * 创建SSE连接用于接收导联状态数据
     * @return SseEmitter对象用于持续推送数据
     */
    @Permission("detector:sse:ecg-lead-status")
    @Operation(summary = "创建SSE连接用于接收导联状态数据")
    @GetMapping(value = "/ecg-lead-status", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamEcgLeadStatusData() {
        String clientId = UUID.randomUUID().toString();
        SseEmitter emitter = sseEmitterService.createEmitter(clientId, SseEmitterService.ECG_LEAD_STATUS_SUBSCRIPTION);
        log.debug("Created new SSE connection for client {} with subscription {}, active connections: {}", 
            clientId, SseEmitterService.ECG_LEAD_STATUS_SUBSCRIPTION, sseEmitterService.getActiveEmitterCount());
        return emitter;
    }

    /**
     * 创建SSE连接用于接收报警提示
     * @return SseEmitter对象用于持续推送数据
     */
    @Permission("detector:sse:ecg-early-warning")
    @Operation(summary = "创建SSE连接用于接收报警提示")
    @GetMapping(value = "/ecg-early-warning", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter ecgEarlyWarning() {
        String clientId = UUID.randomUUID().toString();
        SseEmitter emitter = sseEmitterService.createEmitter(clientId, SseEmitterService.ECG_EARLY_WARNING_SUBSCRIPTION);
        log.debug("Created new SSE connection for client {} with subscription {}, active connections: {}",
                clientId, SseEmitterService.ECG_EARLY_WARNING_SUBSCRIPTION, sseEmitterService.getActiveEmitterCount());
        return emitter;
    }

    /**
     * 创建SSE连接用于接收电量信息以及低电报警提示
     * @return SseEmitter对象用于持续推送数据
     */
    @Permission("detector:sse:power-supply-early-warning")
    @Operation(summary = "创建SSE连接用于接收电量信息以及低电报警提示")
    @GetMapping(value = "/power-supply-early-warning", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter powerSupplyEarlyWarning() {
        String clientId = UUID.randomUUID().toString();
        SseEmitter emitter = sseEmitterService.createEmitter(clientId, SseEmitterService.POWER_SUPPLY_EARLY_WARNING_SUBSCRIPTION);
        log.debug("Created new SSE connection for client {} with subscription {}, active connections: {}",
                clientId, SseEmitterService.POWER_SUPPLY_EARLY_WARNING_SUBSCRIPTION, sseEmitterService.getActiveEmitterCount());
        return emitter;
    }

    /**
     * 创建SSE连接用于接收心率/呼吸率数据
     * @return SseEmitter对象用于持续推送数据
     */
    @Permission("detector:sse:heart-rate")
    @Operation(summary = "创建SSE连接用于接收心率/呼吸率数据")
    @GetMapping(value = "/heart-rate", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamHeartRateData() {
        String clientId = UUID.randomUUID().toString();
        SseEmitter emitter = sseEmitterService.createEmitter(clientId, SseEmitterService.HEART_RATE_SUBSCRIPTION);
        log.debug("Created new SSE connection for client {} with subscription {}, active connections: {}",
                clientId, SseEmitterService.HEART_RATE_SUBSCRIPTION, sseEmitterService.getActiveEmitterCount());
        return emitter;
    }
}
