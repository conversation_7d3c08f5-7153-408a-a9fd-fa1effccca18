package com.cqqy.hrvas.comm.event;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
public class Transfer {

    private final ApplicationEventPublisher eventPublisher;

    public Transfer(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    public void setEventPublisher(Object data, String msgType) {
        eventPublisher.publishEvent(new CommEvent(this, data, msgType));
    }
}
