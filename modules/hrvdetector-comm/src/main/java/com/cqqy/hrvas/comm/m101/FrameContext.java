package com.cqqy.hrvas.comm.m101;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;

import com.cqqy.hrvas.comm.m101.frame.Frame;
import com.cqqy.hrvas.comm.m101.handler.IgnoreCommandHandler;
import com.cqqy.hrvas.comm.m101.utils.RetryManager;
import com.cqqy.hrvas.comm.m101.utils.SequenceManager;
import com.cqqy.hrvas.comm.m101.utils.TimeoutManager;

import java.util.concurrent.Executors;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 帧上下文管理器
 * 负责管理帧发送器的共享资源，包括超时管理、重试管理和响应等待
 */
@Slf4j
public class FrameContext {
    @Getter
    private final Map<Integer, CompletableFuture<Frame>> pendingResponses;
    @Getter
    private final ScheduledExecutorService scheduler;
    @Getter
    private final TimeoutManager timeoutManager;
    @Getter
    private final RetryManager retryManager;
    /** 序列号管理器 */
    @Getter
    private final SequenceManager sequenceManager;
    
    /** 帧处理器映射表 */
    private final Map<Byte, FrameHandler> handlers = new ConcurrentHashMap<>();

    public FrameContext() {
        this.pendingResponses = new ConcurrentHashMap<>();
        this.scheduler = Executors.newScheduledThreadPool(1);
        this.timeoutManager = new TimeoutManager(scheduler, 3000); // 默认3秒超时
        this.retryManager = new RetryManager(scheduler, 3, 1000); // 默认3次重试，1秒间隔
        this.sequenceManager = new SequenceManager();
    }

    /**
     * 注册等待响应
     * @param sequenceNumber 帧序列号
     * @param future 等待的Future
     */
    public void registerPendingResponse(int sequenceNumber, CompletableFuture<Frame> future) {
        pendingResponses.put(sequenceNumber, future);
    }
    
    /**
     * 移除等待响应
     * @param sequenceNumber 帧序列号
     * @return 被移除的Future
     */
    public CompletableFuture<Frame> removePendingResponse(int sequenceNumber) {
        return pendingResponses.remove(sequenceNumber);
    }
    
    /**
     * 取消等待响应
     * @param sequenceNumber 帧序列号
     * @param reason 取消原因
     */
    public void cancelResponse(int sequenceNumber, String reason) {
        CompletableFuture<Frame> future = pendingResponses.remove(sequenceNumber);
        
        if (future != null && !future.isDone()) {
            timeoutManager.cancelTimeout(sequenceNumber);
            retryManager.cancelRetry(sequenceNumber);
            future.completeExceptionally(new RuntimeException(reason));
            log.debug("Cancelled response for sequence: {}, reason: {}", sequenceNumber, reason);
        }
    }
    
    /**
     * 设置响应超时时间
     * @param timeout 超时时间（毫秒）
     */
    public void setResponseTimeout(long timeout) {
        timeoutManager.setDefaultTimeout(timeout);
    }
    
    /**
     * 设置最大重试次数
     * @param maxRetries 最大重试次数
     */
    public void setMaxRetries(int maxRetries) {
        retryManager.setDefaultMaxRetries(maxRetries);
    }
    
    /**
     * 注册帧处理器
     * @param handler 帧处理器
     */
    public void registerHandler(FrameHandler handler) {
        if (handler != null) {
            handlers.put(handler.getCommandId(), handler);
            log.debug("Registered handler for command ID: {}", handler.getCommandId());
        }
    }
    
    /**
     * 获取帧处理器
     * @param commandId 命令ID
     * @return 帧处理器
     */
    public FrameHandler getHandler(byte commandId) {
        for (byte ignoreId : IgnoreCommandHandler.IGNORE_COMMAND_IDS) {
            if (commandId == ignoreId) {
                commandId = (byte) Integer.MAX_VALUE;
                break;
            }
        }
        return handlers.get(commandId);
    }
}