package com.cqqy.hrvas.comm.m101;

import java.util.List;
import java.util.concurrent.*;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;

import com.cqqy.hrvas.comm.m101.frame.Frame;
import com.cqqy.hrvas.comm.m101.frame.FrameParser;
import com.cqqy.hrvas.comm.m101.utils.FrameBuffer;
import com.fazecast.jSerialComm.SerialPort;
import com.fazecast.jSerialComm.SerialPortDataListener;
import com.fazecast.jSerialComm.SerialPortEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * 帧数据监听器
 */
@Slf4j
public class FrameDataListener implements SerialPortDataListener {

    private final FrameBuffer frameBuffer;
    private final FrameDispatcher frameDispatcher;
    private final ExecutorService handlerThreadPool;
    private final BlockingQueue<Frame> frameQueue;
    private final ExecutorService dispatchExecutor;
    private volatile boolean running;

    public FrameDataListener(FrameDispatcher frameDispatcher) {
        int threadCount = Runtime.getRuntime().availableProcessors();
        this.frameBuffer = new FrameBuffer();
        this.frameDispatcher = frameDispatcher;
        this.frameQueue = new LinkedBlockingQueue<>(1000);
        this.running = true;
        
        // 创建分发线程池（单线程）
         this.dispatchExecutor = new ThreadPoolExecutor(
                 1, 1,
                 0L, TimeUnit.MILLISECONDS,
                 new SynchronousQueue<>(),
                 new BasicThreadFactory.Builder().namingPattern("frame-dispatch-pool-%d").daemon(true).build()
         );

        // 创建处理器线程池
        this.handlerThreadPool = new ThreadPoolExecutor(
                threadCount, // 核心线程数
                threadCount*2, // 最大线程数
                60L, TimeUnit.SECONDS, // 空闲线程存活时间
                new LinkedBlockingQueue<>(1000), // 工作队列
                new BasicThreadFactory.Builder().namingPattern("frame-handler-pool-%d").daemon(true).build() // 自定义线程工厂
        );

        // 启动分发线程（线程池方式）
        startDispatchThread();
    }

    @Override
    public int getListeningEvents() {
        return SerialPort.LISTENING_EVENT_DATA_AVAILABLE;
    }

    @Override
    public void serialEvent(SerialPortEvent event) {
        SerialPort serialPort = event.getSerialPort();
        if (event.getEventType() != SerialPort.LISTENING_EVENT_DATA_AVAILABLE) {
            return;
        }
        try {
            // 读取可用数据
            byte[] newData = new byte[serialPort.bytesAvailable()];
            int numRead = serialPort.readBytes(newData, newData.length);

            if (numRead <= 0) {
                return;
            }

            // 添加数据到帧缓冲区
            frameBuffer.append(newData);
            // 计算已处理的字节数
            int consumedBytes = 0;
            // 解析完整的帧并添加到帧队列
            List<Frame> frames = FrameParser.parse(frameBuffer.getBuffer());
            for (Frame frame : frames) {
                // 将帧添加到队列中，由分发线程处理
                if (!frameQueue.offer(frame)) {
                    log.warn("Frame queue is full, dropping frame with sequence: {}", frame.getSequenceNumber());
                }
                consumedBytes += frame.getLength();
            }
            
            // 从缓冲区移除已处理的数据
            if (consumedBytes > 0) {
                frameBuffer.consume(consumedBytes);
            }
        } catch (Exception e) {
            log.error("Error reading from serial port", e);
            notifyError(e);
        }
    }

    private void notifyError(Exception e) {
        log.error("An error occurred in FrameDataListener", e);
        // 在这里可以添加更具体的错误处理逻辑，例如：
        // 1. 通知上层应用错误发生
        // 2. 尝试恢复或关闭串口
        // 3. 记录到特定的错误监控系统
    }
    
    /**
     * 启动分发线程
     */
    private void startDispatchThread() {
        dispatchExecutor.submit(() -> {
            log.info("Frame dispatch thread started");
            while (running) {
                try {
                    // 从队列中获取帧，如果队列为空则阻塞等待
                    Frame frame = frameQueue.poll(100, TimeUnit.MILLISECONDS);
                    if (frame != null) {
                        // 将帧提交到线程池中处理
                        handlerThreadPool.submit(() -> frameDispatcher.handleFrame(frame));
                    }
                } catch (InterruptedException e) {
                    log.warn("Frame dispatch thread interrupted", e);
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    log.error("Error in frame dispatch thread", e);
                }
            }
            log.info("Frame dispatch thread stopped");
        });
    }
    
    /**
     * 停止分发线程
     */
    public void shutdown() {
        running = false;
        dispatchExecutor.shutdownNow();
        handlerThreadPool.shutdown();
    }
}