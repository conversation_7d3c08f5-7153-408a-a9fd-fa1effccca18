package com.cqqy.hrvas.comm.m101;

import com.cqqy.hrvas.comm.SerialPortManager;
import com.cqqy.hrvas.comm.m101.frame.Frame;
import com.cqqy.hrvas.comm.m101.frame.FrameType;

import lombok.extern.slf4j.Slf4j;

/**
 * 帧分发器
 * 用于根据commandId分发Frame到对应的处理器
 */
@Slf4j
public class FrameDispatcher {
    /** 帧上下文 */
    private final FrameContext frameContext;
    private final FrameSender frameSender;
    
    public FrameDispatcher(SerialPortManager portManager,FrameContext frameContext) {
        this.frameContext = frameContext;
        this.frameSender = new FrameSender(portManager, frameContext);
    }
    
    /**
     * 注册帧处理器
     * @param handler 帧处理器
     */
    public void registerHandler(FrameHandler handler) {
        frameContext.registerHandler(handler);
    }
    
    /**
     * 验证帧类型是否有效
     * @param frameType 帧类型
     * @return 是否为有效的帧类型
     */
    private boolean isValidFrameType(byte frameType) {
        return frameType == FrameType.DD.getCode() || frameType == FrameType.DA.getCode();
    }

    /**
     * 预处理接收到的帧
     * @param frame 接收到的帧
     * @return 是否处理成功
     */
    private boolean preprocessFrame(Frame frame) {
        return true;
    }

    /**
     * 处理接收到的帧
     * @param frame 接收到的帧
     */
    public void handleFrame(Frame frame) {
        // 帧为空验证
        if (frame == null) {
            log.warn("Received null frame");
            return;
        }
        
        // 帧类型验证
        byte frameType = frame.getFrameType();
        if (!isValidFrameType(frameType)) {
            log.warn("Received unknown frame type: {}", frameType);
            return;
        }
        
        // 预处理帧
        if (!preprocessFrame(frame)) {
            log.warn("Frame preprocessing failed for sequence: {}", frame.getSequenceNumber());
            return;
        }
        
        // 获取并调用对应的帧处理器
        FrameHandler handler = frameContext.getHandler(frame.getCommandId());
        if (handler == null) {
            log.warn("No handler found for command ID: {}", frame.getCommandIdHex());
            return;
        }

        try {
            if (!handler.preHandle(frame, frameSender)) {
                log.debug("PreHandle interrupted processing for command ID: {}", frame.getCommandIdHex());
                return;
            }
            handler.handle(frame, frameSender);
            handler.postHandle(frame, frameSender);

            // // 完成响应请求
            // new ResponseProcessor(frameContext).completeResponse(frame, frameSender);

            // 对0x90的高频数据包使用trace级别日志
            if (frame.getCommandId() == (byte)0x90) {
                log.trace("Frame processing completed for waveform command ID: {}", frame.getCommandIdHex());
            } else {
                log.debug("Processed frame for command ID: {}", frame.getCommandIdHex());
            }
        } catch (Exception e) {
            log.error("Error handling frame with command ID: {}, error: {}",
                    frame.getCommandIdHex(), e.getMessage(), e);
        }
    }
}