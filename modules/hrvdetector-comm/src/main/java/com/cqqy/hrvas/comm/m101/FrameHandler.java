package com.cqqy.hrvas.comm.m101;

import com.cqqy.hrvas.comm.m101.frame.Frame;

/**
 * 帧处理器接口
 * 用于处理不同类型的数据帧
 */
public interface FrameHandler {
    /**
     * 处理数据帧
     * @param frame 数据帧
     * @param frameSender 帧发送器，用于直接发送响应帧
     */
    void handle(Frame frame, FrameSender frameSender);
    
    /**
     * 获取该处理器支持的命令ID
     * @return 命令ID
     */
    byte getCommandId();

    /**
     * 预处理方法（默认实现）
     * @param frame 数据帧
     * @param sender 帧发送器
     * @return 是否继续处理流程
     */
    default boolean preHandle(Frame frame, FrameSender sender) {
        return true;
    }

    /**
     * 后处理方法（默认实现）
     * @param frame 数据帧
     * @param sender 帧发送器
     */
    default void postHandle(Frame frame, FrameSender sender) {}
}