package com.cqqy.hrvas.comm.m101;

import com.cqqy.hrvas.comm.SerialPortManager;
import com.cqqy.hrvas.comm.m101.frame.Frame;
import com.cqqy.hrvas.comm.m101.frame.FrameParser;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

/**
 * 帧发送器
 * 负责帧的发送、响应处理和重试机制
 */
@Slf4j
public class FrameSender {
    private final SerialPortManager serialPortManager;
    private final FrameContext frameContext;
    
    public FrameSender(SerialPortManager serialPortManager, FrameContext frameContext) {
        this.serialPortManager = serialPortManager;
        this.frameContext = frameContext;
    }
    
    /**
     * 发送帧
     * @param frame 要发送的帧
     * @return 是否发送成功
     */
    public boolean sendFrame(Frame frame) {

        if (frame == null) {
            log.warn("Attempt to send null frame");
            return false;
        }
        
        try {
            byte[] data = FrameParser.serialize(frame);
            boolean isSendSuccess = serialPortManager.sendRawData(data);
            frameContext.getSequenceManager().addPendingFrame(frame);
            return isSendSuccess;
        } catch (Exception e) {
            log.error("Failed to send frame", e);
            return false;
        }
    }
    
    /**
     * 发送帧并等待响应
     * @param frame 要发送的帧
     * @return 响应帧的Future
     */
    public CompletableFuture<Frame> sendFrameAndWaitResponse(Frame frame) {
        if (frame == null) {
            return CompletableFuture.failedFuture(new IllegalArgumentException("Frame cannot be null"));
        }

        CompletableFuture<Frame> responseFuture = new CompletableFuture<>();
        int sequenceNumber = frame.getSequenceNumber();
        
        // 注册响应等待
        frameContext.registerPendingResponse(sequenceNumber, responseFuture);

        // 发送帧
        if (!sendFrame(frame)) {
            frameContext.removePendingResponse(sequenceNumber);
            return CompletableFuture.failedFuture(new RuntimeException("Failed to send frame"));
        }

        // 设置超时处理
        frameContext.getTimeoutManager().setTimeout(sequenceNumber, this::handleTimeout);
        
        return responseFuture;
    }
    
    /**
     * 发送帧并设置重试次数（使用默认重试次数）
     * @param frame 要发送的帧
     * @param maxRetries 最大重试次数，-1表示无限重试
     * @return 响应帧的Future
     */
    public CompletableFuture<Frame> sendFrameWithRetries(Frame frame, int maxRetries) {
        if (frame == null) {
            return CompletableFuture.failedFuture(new IllegalArgumentException("Frame cannot be null"));
        }
        
        int sequenceNumber = frame.getSequenceNumber();
        frameContext.getRetryManager().initRetry(sequenceNumber, maxRetries, frameContext.getTimeoutManager().getDefaultTimeout(), 
            seq -> handleRetry(frame));
        
        return sendFrameAndWaitResponse(frame);
    }
    
    /**
     * 发送帧直到收到响应（无重试次数）
     * @param frame 要发送的帧
     * @return 响应帧的Future
     */
    public CompletableFuture<Frame> sendFrameUntilResponse(Frame frame) {
        if (frame == null) {
            return CompletableFuture.failedFuture(new IllegalArgumentException("Frame cannot be null"));
        }
        
        int sequenceNumber = frame.getSequenceNumber();
        frameContext.getRetryManager().initRetry(sequenceNumber, -1, frameContext.getTimeoutManager().getDefaultTimeout(), 
            seq -> handleRetry(frame));
        
        return sendFrameAndWaitResponse(frame);
    }
        
    /**
     * 取消等待响应
     * @param sequenceNumber 帧序列号
     * @param reason 取消原因
     */
    public void cancelResponse(int sequenceNumber, String reason) {
        frameContext.cancelResponse(sequenceNumber, reason);
    }
    
    /**
     * 处理超时事件
     * @param sequenceNumber 帧序列号
     */
    private void handleTimeout(int sequenceNumber) {
        CompletableFuture<Frame> future = frameContext.getPendingResponses().get(sequenceNumber);
        
        if (future != null && !future.isDone()) {
            if (!frameContext.getRetryManager().handleRetry(sequenceNumber)) {
                frameContext.removePendingResponse(sequenceNumber);
                future.completeExceptionally(new TimeoutException("Response timeout"));
                log.warn("Response timeout for sequence: {}, no more retries", sequenceNumber);
            } else {
                log.debug("Response timeout for sequence: {}, will retry", sequenceNumber);
            }
        }
    }
    
    /**
     * 处理重试
     * @param frame 要重试的帧
     */
    private void handleRetry(Frame frame) {
        if (frame == null) return;
        
        int sequenceNumber = frame.getSequenceNumber();
        if (sendFrame(frame)) {
            frameContext.getTimeoutManager().setTimeout(sequenceNumber, this::handleTimeout);
        } else {
            cancelResponse(sequenceNumber, "Failed to send frame during retry");
        }
    }
    
    public void setResponseTimeout(long timeout) {
        frameContext.setResponseTimeout(timeout);
    }
    
    public void setMaxRetries(int maxRetries) {
        frameContext.setMaxRetries(maxRetries);
    }
    
    /**
     * 根据序列号获取应答Future
     * @param sequenceNumber 序列号
     * @return 响应帧的Future，如果不存在返回null
     */
    public CompletableFuture<Frame> getResponseFuture(int sequenceNumber) {
        return frameContext.getPendingResponses().get(sequenceNumber);
    }

    /**
     * 根据序列号等待并获取应答
     * @param sequenceNumber 序列号
     * @return 响应帧
     * @throws TimeoutException 如果超时未收到响应
     * @throws InterruptedException 如果线程被中断
     * @throws ExecutionException 如果在等待过程中发生执行异常
     */
    public Frame waitForResponse(int sequenceNumber) 
        throws TimeoutException, InterruptedException, ExecutionException {
        CompletableFuture<Frame> future = frameContext.getPendingResponses().get(sequenceNumber);
        if (future == null) {
            throw new IllegalArgumentException("No pending response for sequence: " + sequenceNumber);
        }
        return future.get();
    }
}
