package com.cqqy.hrvas.comm.m101.frame;

import java.util.Map;

/**
 * 命令ID定义
 */
public class CommandId {
    private CommandId() {}

    /** 下行命令 */
    private static final Map<Byte, Byte[]> DOWN_COMMAND_MAP;

    /** 上电握手命令 */
    public static final byte POWER_ON_HANDSHAKE = (byte) 0x01;

    /** 查询模块信息 */
    public static final byte QUERY_MODULE_INFO = (byte) 0x02;

    /** 设置病人类型 */
    public static final byte SET_PATIENT_INFO = (byte) 0x10;

    /** 设置心电导联模式 */
    public static final byte SET_ECG_LEAD_MODE = (byte) 0x20;

    /** 设置心电通道导联 */
    public static final byte SET_ECG_CHANNEL_LEADS = (byte) 0x21;

    /** 设置心电滤波方式 */
    public static final byte SET_ECG_FILTERING_METHOD = (byte) 0x22;

    /** 设置心电50/60hz 陷波 */
    public static final byte SET_ECG_NOTCH = (byte) 0x23;

    /** 设置心电增益 */
    public static final byte SET_ECG_GAIN = (byte) 0x24;

    /** 设置心电 ST 模板 */
    public static final byte SET_ECG_ST_TEMPLATE = (byte) 0x25;

    /** 设置心率计算/心律失常分析通道 */
    public static final byte SET_HR_OR_ECG = (byte) 0x26;

    /** 启动/停止 PACE 检测 */
    public static final byte SET_PACE = (byte) 0x27;

    /** 启动/停止心电校准 */
    public static final byte SET_ECG_CALIBRATION = (byte) 0x28;

    /** 设置呼吸窒息报警时间 */
    public static final byte SET_ASPHYXIA_ALARM_TIME = (byte) 0x30;

    /** 设置呼吸导联 */
    public static final byte SET_BREATHING_LEADS = (byte) 0x31;

    /** 设置呼吸敏感度 */
    public static final byte SET_RESPIRATORY_SENSITIVITY = (byte) 0x32;

    /** 设置R，T 波位置算法类型 */
    public static final byte SET_RT_WAVE = (byte) 0x33;

    /** 在线升级 **/
    public static final byte ONLINE_UPGRADE = (byte) 0x7F;


    /** 上行命令 */

    /** 通用命令应答数据包 DA */
    public static final byte COMMON_COMMAND_RESPONSE = (byte) 0x80;

    /** 上电握手请求数据包 DD */
    public static final byte POWER_ON_HANDSHAKE_DATA = (byte) 0x81;

    /** 模块信息应答 DA */
    public static final byte MODULE_INFO_RESPONSE = (byte) 0x82;
    
    /** 波形数据 DD */
    public static final byte WAVEFORM_DATA = (byte) 0x90;

    /** 心率/呼吸率数据 DD */
    public static final byte HEART_RESPIRATORY_DATA = (byte) 0x91;

    /** 心电导联状态 DD */
    public static final byte ECG_LEAD_STATUS = (byte) 0x92;

    /** 心律失常分析结果数据包 DD */
    public static final byte ARRHYTHMIA_DATA = (byte) 0x96;

    static {
        DOWN_COMMAND_MAP = Map.of(
            POWER_ON_HANDSHAKE, new Byte[]{ParamType.ECG.getCode(), FrameType.DC.getCode()},
            QUERY_MODULE_INFO, new Byte[]{ParamType.ECG.getCode(), FrameType.DC.getCode()}
        );
    }

    public static Byte[] getDownCommand(byte commandId) {
        return DOWN_COMMAND_MAP.get(commandId);
    }
}