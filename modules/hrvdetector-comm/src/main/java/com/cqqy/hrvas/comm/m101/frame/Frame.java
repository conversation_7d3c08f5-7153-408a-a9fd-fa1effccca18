package com.cqqy.hrvas.comm.m101.frame;

import lombok.Data;

/**
 * 串口通信帧
 * 用于封装和解析串口通信的数据帧
 */
@Data
public class Frame {
    /** 数据包起始符 */ 
    public static final byte FRAME_START_MARK = (byte) 0xFA;

    /** 数据包起始符 */
    private final byte startMark = FRAME_START_MARK;
    
    /** 数据包长度 */
    private byte length;
    
    /** 参数类型: 0x01-ECG, 0x02-NiBP, 0x03-SpO2 */
    private byte paramType;
    
    /** 
     * 数据包类型
     * 0x01-控制命令包(DC)
     * 0x02-请求命令包(DR)
     * 0x03-命令应答包(DA)
     * 0x04-通用数据包(DD)
     */
    private byte frameType;
    
    /** 数据包ID */
    private byte commandId;
    
    /** 数据包序列号 */
    private int sequenceNumber;
    
    /** 数据段 */
    private byte[] data;
    
    /** 校验和 */
    private byte checksum;
    
    /**
     * 计算校验和
     * @return 校验和值
     */
    public byte calculateChecksum() {
        int sum = 0;
        
        // 按顺序累加所有字节（除了起始标记和校验和本身）
        sum += length;
        sum += paramType;
        sum += frameType;
        sum += commandId;
        sum += (sequenceNumber & 0xFF);
        sum += ((sequenceNumber >> 8) & 0xFF);
        sum += ((sequenceNumber >> 16) & 0xFF);
        sum += ((sequenceNumber >> 24) & 0xFF);
        
        // 添加数据段的所有字节
        if (data != null) {
            for (byte b : data) {
                sum += (b & 0xFF);
            }
        }
        
        // 取低8位
        return (byte) (sum & 0xFF);
    }
    
    /**
     * 验证校验和是否正确
     * @return 校验和是否正确
     */
    public boolean validateChecksum() {
        return calculateChecksum() == checksum;
    }

    public String getCommandIdHex() {
        return String.format("0X%02X", commandId);
    }
}