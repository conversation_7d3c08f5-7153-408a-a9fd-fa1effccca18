package com.cqqy.hrvas.comm.m101.frame;

import java.util.Objects;

import com.cqqy.hrvas.comm.m101.FrameContext;

public class FrameBuilder {
    private final FrameContext frameContext;
    private byte paramType;
    private byte frameType;
    private byte frameId;
    private byte[] data;

    public FrameBuilder(FrameContext frameContext) {
        this.frameContext = frameContext;
    }

    // 链式设置方法
    public FrameBuilder withParamType(byte paramType) {
        this.paramType = paramType;
        return this;
    }

    public FrameBuilder withFrameType(byte frameType) {
        this.frameType = frameType;
        return this;
    }

    public FrameBuilder withFrameId(byte frameId) {
        this.frameId = frameId;
        return this;
    }

    public FrameBuilder withData(byte[] data) {
        this.data = data;
        return this;
    }

    /**
     * 构建最终Frame对象
     */
    public Frame build() {
        validateParameters(paramType, frameType, data);
        
        Frame frame = new Frame();
        frame.setParamType(paramType);
        frame.setFrameType(frameType);
        frame.setCommandId(frameId);
        frame.setSequenceNumber(getNextSequenceNumber());
        frame.setData(Objects.requireNonNullElse(data, new byte[0]));
        
        calculateAndSetLength(frame);
        frame.setChecksum(frame.calculateChecksum());
        return frame;
    }

    private int getNextSequenceNumber() {
        return frameContext.getSequenceManager().nextSequence();
    }

    /**
     * 参数校验方法
     */
    private void validateParameters(byte paramType, byte frameType, byte[] data) {
        if (paramType < ParamType.ECG.getCode() ||
                paramType > ParamType.SPO2.getCode() ||
                paramType == (byte) 0x00) {
            throw new IllegalArgumentException("无效参数类型: " + paramType);
        }
        if (frameType != FrameType.DC.getCode() && frameType!= FrameType.DD.getCode()) {
            throw new IllegalArgumentException("无效帧类型: " + frameType);
        }
    }

    /**
     * 自动计算帧长度（含协议头长度）
     */
    private void calculateAndSetLength(Frame frame) {
        int baseLength = 10; // 固定头长度
        int totalLength = baseLength + frame.getData().length;
        frame.setLength((byte) Math.min(totalLength, Byte.MAX_VALUE));
    }

    public static FrameBuilder frameBuilder(FrameContext context) {
        return new FrameBuilder(context);
    }

    public static FrameBuilder frameBuilder(FrameContext context, byte commandId) {
        Byte[] cmdBytes = CommandId.getDownCommand(commandId);
        return new FrameBuilder(context)
           .withParamType(cmdBytes[0])
           .withFrameType(cmdBytes[1])
           .withFrameId(commandId);
    }

    public static FrameBuilder frameBuilder(FrameContext context, byte paramType, byte frameType) {
        return new FrameBuilder(context)
            .withParamType(paramType)
            .withFrameType(frameType);
    }

    /**
     * 创建预设的DC帧构建器
     * @return 配置好帧类型的FrameBuilder实例
     */
    public static FrameBuilder dcFrameBuilder(FrameContext context) {
        return new FrameBuilder(context)
                .withParamType(ParamType.ECG.getCode())
                .withFrameType(FrameType.DC.getCode());
    }

    /**
     * 创建预设的DD帧构建器
     * @return 配置好帧类型的FrameBuilder实例
     */
    public static FrameBuilder ddFrameBuilder(FrameContext context) {
        return new FrameBuilder(context)
            .withFrameType(FrameType.DD.getCode());
    }
}
