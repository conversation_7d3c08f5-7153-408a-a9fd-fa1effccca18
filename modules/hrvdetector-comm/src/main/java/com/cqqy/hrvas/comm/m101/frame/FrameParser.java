package com.cqqy.hrvas.comm.m101.frame;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * 帧解析器，负责从字节流中解析出完整的帧
 */
@Slf4j
public class FrameParser {

    /**
     * 将帧对象序列化为字节数组
     * @param frame 帧对象
     * @return 字节数组
     */
    public static byte[] serialize(Frame frame) {
        if (frame == null) {
            return new byte[0];
        }
        
        // 计算总长度
        int totalLength = 10; // 帧头(1) + 长度(1) + 参数类型(1) + 帧类型(1) + 帧ID(1) + 序列号(4) + 校验和(1)
        if (frame.getData() != null) {
            totalLength += frame.getData().length;
        }
        
        ByteBuffer buffer = ByteBuffer.allocate(totalLength);
        
        // 写入帧头
        buffer.put(frame.getStartMark());
        
        // 写入长度
        buffer.put(frame.getLength());
        
        // 写入参数类型
        buffer.put(frame.getParamType());
        
        // 写入帧类型
        buffer.put(frame.getFrameType());
        
        // 写入帧ID
        buffer.put(frame.getCommandId());
        
        // 写入序列号
        buffer.putInt(frame.getSequenceNumber());
        
        // 写入数据段
        if (frame.getData() != null) {
            buffer.put(frame.getData());
        }
        
        // 写入校验和
        buffer.put(frame.getChecksum());
        
        return buffer.array();
    }


    /**
     * 解析字节数组为帧对象
     * @param data 字节数组
     * @return 帧对象列表
     */
    public static List<Frame> parse(byte[] data) {
        List<Frame> frames = new ArrayList<>();
        if (data == null || data.length < 10) { // 最小帧长度为10字节
            return frames;
        }
        
        int index = 0;
        while (index < data.length) {
            // 查找帧头
            while (index < data.length && data[index] != Frame.FRAME_START_MARK) {
                index++;
            }
            
            // 如果没有足够的字节，则退出
            if (index + 9 >= data.length) {
                break;
            }
            
            // 读取长度
            byte length = data[index + 1];
            
            // 检查长度是否合法
            if (length < 9 || index + length > data.length) {
                index++;
                continue;
            }
            
            try {
                // 创建帧对象
                Frame frame = new Frame();
                // frame.setStartMark(data[index]); // 帧头不需要设置，因为它已经在Frame类中定义为常量
                frame.setLength(length);
                frame.setParamType(data[index + 2]);
                frame.setFrameType(data[index + 3]);
                frame.setCommandId(data[index + 4]);
                
                // 读取序列号
                ByteBuffer buffer = ByteBuffer.wrap(data, index + 5, 4);
                frame.setSequenceNumber(buffer.getInt());
                
                // 读取数据段
                int dataLength = length - 10;
                if (dataLength > 0) {
                    byte[] frameData = new byte[dataLength];
                    System.arraycopy(data, index + 9, frameData, 0, dataLength);
                    frame.setData(frameData);
                }
                
                // 读取校验和
                frame.setChecksum(data[index + length - 1]);
                
                // 验证校验和
                if (frame.validateChecksum()) {
                    frames.add(frame);
                } else {
                    log.warn("Invalid checksum for frame at index {}", index);
                }
                
                // 移动到下一帧
                index += length;
            } catch (Exception e) {
                log.error("Error parsing frame at index {}", index, e);
                index++;
            }
        }
        
        return frames;
    }
}
