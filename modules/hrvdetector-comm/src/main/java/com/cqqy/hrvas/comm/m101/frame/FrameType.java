package com.cqqy.hrvas.comm.m101.frame;

/**
 * 数据包类型枚举
 * 用于标识不同的数据包类型，包括四种类型：控制命令包（DC）、请求命令包（DR）、命令应答包（DA）和通用数据包（DD）
 */
public enum FrameType {
    /**
     * 控制命令包(DC)
     * 对接收方而言，必须发送通用命令应答包进行应答，表示接收到的命令格式是否正确以及命令是否正常执行
     * 对于发送方而言，若在规定时限内未接收到应答包，则需重发
     */
    DC((byte) 0x01),
    
    /**
     * 请求命令包(DR)
     * 对接收方而言，必须进行应答，如果接收到的命令格式不符合要求，则发送通用命令应答包进行应答
     * 如果命令格式符合要求，则根据解析内容进行相应的处理或数据反馈应答
     * 对于发送方而言，若在规定时限内未接收到应答包，则需重发
     */
    DR((byte) 0x02),
    
    /**
     * 命令应答包(DA)
     * 用于响应控制命令包和请求命令包。
     * 无需应答和重发
     */
    DA((byte) 0x03),
    
    /**
     * 通用数据包(DD)
     * 用于周期性主动发送的波形包、参数包、状态信息包等。
     * 无需应答和重发
     */
    DD((byte) 0x04);
    
    private final byte code;
    
    FrameType(byte code) {
        this.code = code;
    }
    
    public byte getCode() {
        return code;
    }
    
    /**
     * 根据类型码获取帧类型
     * @param code 类型码
     * @return 帧类型
     */
    public static FrameType fromCode(byte code) {
        for (FrameType type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown frame type code: " + code);
    }
    
    /**
     * 判断是否需要应答
     * @return 是否需要应答
     */
    public boolean requiresResponse() {
        return this == DC || this == DR;
    }
    
    /**
     * 判断是否需要重发
     * @return 是否需要重发
     */
    public boolean requiresRetransmission() {
        return this == DC || this == DR;
    }
}