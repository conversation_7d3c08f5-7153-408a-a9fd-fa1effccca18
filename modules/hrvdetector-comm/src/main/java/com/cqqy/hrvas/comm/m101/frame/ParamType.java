package com.cqqy.hrvas.comm.m101.frame;

/**
 * 参数类型枚举
 * 用于标识不同的参数模块的数据包，此处 0x01，代表 ECG,0x02，代表 NiBP， 0x03，代表 SpO2
 */
public enum ParamType {
    /**
     * ECG - 心电图
     */
    ECG((byte) 0x01),
    
    /**
     * NiBP - 无创血压
     */
    NIBP((byte) 0x02),
    
    /**
     * SpO2 - 血氧饱和度
     */
    SPO2((byte) 0x03);
    
    private final byte code;
    
    ParamType(byte code) {
        this.code = code;
    }
    
    public byte getCode() {
        return code;
    }
    
    /**
     * 根据类型码获取参数类型
     * @param code 类型码
     * @return 参数类型
     */
    public static ParamType fromCode(byte code) {
        for (ParamType type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown parameter type code: " + code);
    }
}