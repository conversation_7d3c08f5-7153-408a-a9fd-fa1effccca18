package com.cqqy.hrvas.comm.m101.handler;

import com.cqqy.hrvas.comm.m101.FrameHandler;
import com.cqqy.hrvas.comm.m101.FrameSender;
import com.cqqy.hrvas.comm.m101.frame.CommandId;
import com.cqqy.hrvas.comm.m101.frame.Frame;
import com.cqqy.hrvas.comm.service.EarlyWarningService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ArrhythmiaHandler implements FrameHandler {

    private final EarlyWarningService earlyWarningService;

    public ArrhythmiaHandler(EarlyWarningService earlyWarningService) {
        this.earlyWarningService = earlyWarningService;
    }

    @Override
    public void handle(Frame frame, FrameSender frameSender) {
        if (frame == null || frame.getData() == null) {
            log.warn("Received null frame or frame data");
            return;
        }
        byte[] data = frame.getData();
        int code = data[0];
        if (code == 1) {
            int lastTime = ((data[2] & 0xFF) << 8) | (data[1] & 0xFF);
            int thisTime = ((data[4] & 0xFF) << 8) | (data[3] & 0xFF);
            earlyWarningService.arrhythmiaReceive(lastTime, thisTime);
        }
    }

    @Override
    public byte getCommandId() {
        return CommandId.ARRHYTHMIA_DATA;
    }
}
