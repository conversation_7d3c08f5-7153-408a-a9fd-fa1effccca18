package com.cqqy.hrvas.comm.m101.handler;

import com.cqqy.hrvas.comm.m101.FrameContext;
import com.cqqy.hrvas.comm.m101.FrameHandler;
import com.cqqy.hrvas.comm.m101.FrameSender;
import com.cqqy.hrvas.comm.m101.frame.CommandId;
import com.cqqy.hrvas.comm.m101.frame.Frame;
import com.cqqy.hrvas.comm.m101.utils.M10xSession;
import com.cqqy.hrvas.comm.powersupply.service.PsEarlyWarningService;
import lombok.extern.slf4j.Slf4j;

/**
 * 通用命令应答处理器 (ECG, DA，0x80)
 * 
 * 下位机通用命令应答
 * 当收到控制命令（DC）时进行应答，包括命令是否合理和执行是否成功。
 * 当收到请求命令（DR）时，只在命令格式不正确或者请求信息无法返回时进行应答，包括命令格式不正确或者命令不合理。
 */
@Slf4j
public class CommonResponseHandler implements FrameHandler {
    private final FrameContext frameContext;

    private final ResponseProcessor responseProcessor;

    private final PsEarlyWarningService psEarlyWarningService;

    public CommonResponseHandler(FrameContext frameContext, PsEarlyWarningService psEarlyWarningService) {
        this.frameContext = frameContext;
        this.responseProcessor = new ResponseProcessor(frameContext);
        this.psEarlyWarningService = psEarlyWarningService;
    }
    
    @Override
    public void handle(Frame frame, FrameSender frameSender) {
        try {
            int sequenceNumber = frame.getSequenceNumber();
            Frame originalFrame = frameContext.getSequenceManager().getPendingFrame(sequenceNumber);
            if (originalFrame == null) {
                log.warn("No pending frame for sequence: {}", sequenceNumber);
                return;
            }

            // 解析通用命令应答数据
            if (!processCommonResponse(originalFrame, frame)) {
                return;
            }

            responseProcessor.completeResponse(frame, frameSender);
        } catch (Exception e) {
            log.error("Failed to handle common response", e);
        }
    }
    
    private String parseErrorCode(byte code) {
        switch (code) {
            case 0x01: return "参数类型错误";
            case 0x02: return "数据类型错误"; 
            case 0x03: return "数据ID错误";
            case 0x04: return "数据段错误";
            case 0x05: return "序列号段错误";
            case 0x06: return "校验和错误";
            case 0x07: return "命令执行成功";
            case 0x08: return "命令执行失败";
            case 0x09: return "系统状态忙";
            default: return "未知错误码: " + code;
        }
    }

    /**
     * 处理通用命令应答
     *
     * @param originalFrame 原始请求帧
     * @param responseFrame 响应帧
     * @return 是否处理成功 成功则继续处理，失败则停止处理
     */
    public boolean processCommonResponse(Frame originalFrame, Frame responseFrame) {
        byte[] data = responseFrame.getData();
        byte errorCode = data[0]; // 错误码
        int commandId = originalFrame.getCommandId(); // 命令ID

        String errorMessage = parseErrorCode(errorCode);
        log.info("Received common response - Sequence: {}, Command: {}, Status: {}", 
            originalFrame.getSequenceNumber(), originalFrame.getCommandId(), errorMessage);

        if (commandId == CommandId.POWER_ON_HANDSHAKE) {
            M10xSession m10xSession = M10xSession.getInstance();
            if (errorCode == 0x07) {
                m10xSession.setModuleStatus(0);
                log.info("Received power on handshake response - Sequence: {}, Command: {}, Status: {}",
                    originalFrame.getSequenceNumber(), originalFrame.getCommandId(), "握手成功");
                psEarlyWarningService.handshakeResponse("handshake success");
                return true;
            } else if (errorCode == 0x09) {
                m10xSession.setModuleStatus(1);
                log.info("Received power on handshake response - Sequence: {}, Command: {}, Status: {}",
                    originalFrame.getSequenceNumber(), originalFrame.getCommandId(), "握手成功 (模块忙)");
                psEarlyWarningService.handshakeResponse("handshake success(module busy)");
                return true;
            } else {
                // 其他错误
                log.error("Received power on handshake response - Sequence: {}, Command: {}, Status: {}",
                    originalFrame.getSequenceNumber(), originalFrame.getCommandId(), "握手失败");
                psEarlyWarningService.handshakeResponse("handshake fail");
                return false;
            }
        }
        return true;
    }
    
    @Override
    public byte getCommandId() {
        // 通用命令应答ID
        return CommandId.COMMON_COMMAND_RESPONSE;
    }
}