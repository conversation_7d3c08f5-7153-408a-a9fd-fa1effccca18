package com.cqqy.hrvas.comm.m101.handler;

import com.cqqy.hrvas.comm.m101.FrameHandler;
import com.cqqy.hrvas.comm.m101.FrameSender;
import com.cqqy.hrvas.comm.m101.frame.CommandId;
import com.cqqy.hrvas.comm.m101.frame.Frame;
import com.cqqy.hrvas.comm.m101.model.EcgLeadStatus;
import com.cqqy.hrvas.comm.service.sse.SseEmitterService;

import lombok.extern.slf4j.Slf4j;

/**
 * 心电导联状态数据包处理器
 */
@Slf4j
public class EcgLeadStatusHandler implements FrameHandler {

    private final EcgLeadStatus leadStatus = EcgLeadStatus.getInstance();
    private final SseEmitterService sseEmitterService;

    public EcgLeadStatusHandler(SseEmitterService sseEmitterService) {
        this.sseEmitterService = sseEmitterService;
    }

    @Override
    public void handle(Frame frame, FrameSender frameSender) {
        try {
            byte[] data = frame.getData();
            if (data == null || data.length < 3) {
                log.warn("Invalid lead status data length, expected 3 bytes but got {}", data == null ? 0 : data.length);
                return;
            }

            // 解析第1字节数据
            byte byte1 = data[0];
            leadStatus.setFiveLeadMode((byte1 & 0x01) != 0);     // Bit0: 5导联模式
            leadStatus.setRlDisconnected((byte1 & 0x02) != 0);   // Bit1: RL电极断开
            leadStatus.setV1Disconnected((byte1 & 0x04) != 0);   // Bit2: V1电极断开
            leadStatus.setLlDisconnected((byte1 & 0x08) != 0);   // Bit3: LL电极断开
            leadStatus.setLaDisconnected((byte1 & 0x10) != 0);   // Bit4: LA电极断开
            leadStatus.setRaDisconnected((byte1 & 0x20) != 0);   // Bit5: RA电极断开

            // 解析第2字节数据
            byte byte2 = data[1];
            leadStatus.setTwelveLeadMode((byte2 & 0x01) != 0);   // Bit0: 12导联模式
            leadStatus.setV2Disconnected((byte2 & 0x02) != 0);   // Bit1: V2电极断开
            leadStatus.setV3Disconnected((byte2 & 0x04) != 0);   // Bit2: V3电极断开
            leadStatus.setV4Disconnected((byte2 & 0x08) != 0);   // Bit3: V4电极断开
            leadStatus.setV5Disconnected((byte2 & 0x10) != 0);   // Bit4: V5电极断开
            leadStatus.setV6Disconnected((byte2 & 0x20) != 0);   // Bit5: V6电极断开

            // 解析第3字节数据
            byte byte3 = data[2];
            leadStatus.setChannelINoSignal((byte3 & 0x01) != 0);  // Bit0: 通道I信号不存在
            leadStatus.setChannelIINoSignal((byte3 & 0x02) != 0); // Bit1: 通道II信号不存在
            leadStatus.setChannelV1NoSignal((byte3 & 0x04) != 0); // Bit2: 通道V1信号不存在
            leadStatus.setChannelV2NoSignal((byte3 & 0x08) != 0); // Bit3: 通道V2信号不存在
            leadStatus.setChannelV3NoSignal((byte3 & 0x10) != 0); // Bit4: 通道V3信号不存在
            leadStatus.setChannelV4NoSignal((byte3 & 0x20) != 0); // Bit5: 通道V4信号不存在
            leadStatus.setChannelV5NoSignal((byte3 & 0x40) != 0); // Bit6: 通道V5信号不存在
            leadStatus.setChannelV6NoSignal((byte3 & 0x80) != 0); // Bit7: 通道V6信号不存在

            log.debug("Lead status updated - byte1: {}, byte2: {}, byte3: {}", byte1, byte2, byte3);
            
            // 通过SSE广播导联状态数据给订阅了导联状态的客户端
            sseEmitterService.broadcast(SseEmitterService.ECG_LEAD_STATUS_SUBSCRIPTION, leadStatus);
            
        } catch (Exception e) {
            log.error("Failed to process lead status data", e);
        }
    }

    @Override
    public byte getCommandId() {
        return CommandId.ECG_LEAD_STATUS;
    }
}