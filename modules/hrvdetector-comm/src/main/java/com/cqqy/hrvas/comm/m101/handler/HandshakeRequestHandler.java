package com.cqqy.hrvas.comm.m101.handler;

import com.cqqy.hrvas.comm.m101.FrameContext;
import com.cqqy.hrvas.comm.m101.FrameHandler;
import com.cqqy.hrvas.comm.m101.FrameSender;
import com.cqqy.hrvas.comm.m101.frame.CommandId;
import com.cqqy.hrvas.comm.m101.frame.Frame;
import com.cqqy.hrvas.comm.m101.frame.FrameBuilder;

import lombok.extern.slf4j.Slf4j;

/**
 * 上电握手请求处理器(ECG, DD, 0x81)
 * 
 * 握手请求^ --> 握手_ --> 通用应答(成功或繁忙)^
 *                   --> 握手(重试)_ --> ... --> 通用应答(成功或繁忙)^
 */
@Slf4j
public class HandshakeRequestHandler implements FrameHandler {

    private final FrameContext frameContext;

    public HandshakeRequestHandler(FrameContext frameContext) {
        this.frameContext = frameContext;
    }
    
    @Override
    public void handle(Frame frame, FrameSender frameSender) {
        log.info("Received handshake request: {}", frame.getData());
        frameSender.sendFrameWithRetries(
            FrameBuilder.frameBuilder(frameContext, CommandId.POWER_ON_HANDSHAKE)
            .build(), -1);
    }
    
    @Override
    public byte getCommandId() {
        return CommandId.POWER_ON_HANDSHAKE_DATA;
    }
}