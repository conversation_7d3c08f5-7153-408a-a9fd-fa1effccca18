package com.cqqy.hrvas.comm.m101.handler;

import com.cqqy.hrvas.comm.event.Transfer;
import com.cqqy.hrvas.comm.m101.FrameHandler;
import com.cqqy.hrvas.comm.m101.FrameSender;
import com.cqqy.hrvas.comm.m101.frame.CommandId;
import com.cqqy.hrvas.comm.m101.frame.Frame;
import com.cqqy.hrvas.comm.m101.model.HeartRateData;
import com.cqqy.hrvas.comm.service.EarlyWarningService;
import com.cqqy.hrvas.comm.service.sse.SseEmitterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.cqqy.hrvas.constant.WebSocketMsgType.DE_HEART_RATE;

@Slf4j
@Component
public class HeartRateHandler implements FrameHandler {

    private final EarlyWarningService earlyWarningService;

    private final SseEmitterService sseEmitterService;

    private final Transfer transfer;

    public HeartRateHandler(EarlyWarningService earlyWarningService, SseEmitterService sseEmitterService, Transfer transfer) {
        this.earlyWarningService = earlyWarningService;
        this.sseEmitterService = sseEmitterService;
        this.transfer = transfer;
    }

    @Override
    public void handle(Frame frame, FrameSender frameSender) {
        if (frame == null || frame.getData() == null) {
            log.warn("Received null frame or frame data");
            return;
        }
        byte[] data = frame.getData();

        HeartRateData heartRateData = new HeartRateData();
        if (data[0] == -100) { // -100表示未得到结果，返回0
            heartRateData.setHeartRate(0);
            heartRateData.setRespiratoryRate(0);
        } else {
            heartRateData = parseWaveformData(data);
        }

        earlyWarningService.heartRateReceive(heartRateData.getHeartRate());

        // 通过SSE广播波形数据给订阅了波形数据的客户端
        sseEmitterService.broadcast(SseEmitterService.HEART_RATE_SUBSCRIPTION, heartRateData);

        // 推送到服务器
        transfer.setEventPublisher(heartRateData, DE_HEART_RATE);
    }

    @Override
    public byte getCommandId() {
        return CommandId.HEART_RESPIRATORY_DATA;
    }

    private HeartRateData parseWaveformData(byte[] data) {
        HeartRateData heartRateData = new HeartRateData();
        int heartRate = ((data[1] & 0xFF) << 8) | (data[0] & 0xFF);
        int respiratoryRate = ((data[3] & 0xFF) << 8) | (data[2] & 0xFF);
        heartRateData.setHeartRate(heartRate);
        heartRateData.setRespiratoryRate(respiratoryRate);
        return heartRateData;
    }
}
