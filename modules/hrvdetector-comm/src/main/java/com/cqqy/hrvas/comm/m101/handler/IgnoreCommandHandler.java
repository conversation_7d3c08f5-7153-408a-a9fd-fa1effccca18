package com.cqqy.hrvas.comm.m101.handler;

import com.cqqy.hrvas.comm.m101.FrameHandler;
import com.cqqy.hrvas.comm.m101.FrameSender;
import com.cqqy.hrvas.comm.m101.frame.Frame;

import lombok.extern.slf4j.Slf4j;

/**
 * 忽略命令帧处理器
 * 用于处理不需要特殊处理的命令帧
 */
@Slf4j
public class IgnoreCommandHandler implements FrameHandler {
    public static final byte[] IGNORE_COMMAND_IDS = {};

    @Override
    public void handle(Frame frame, FrameSender frameSender) {
        log.debug("Ignoring frame with command ID: {}", frame.getCommandIdHex());
    }
    
    @Override
    public byte getCommandId() {
        return (byte) Integer.MAX_VALUE;
    }
}