package com.cqqy.hrvas.comm.m101.handler;

import com.cqqy.hrvas.comm.m101.FrameContext;
import com.cqqy.hrvas.comm.m101.FrameHandler;
import com.cqqy.hrvas.comm.m101.FrameSender;
import com.cqqy.hrvas.comm.m101.frame.CommandId;
import com.cqqy.hrvas.comm.m101.frame.Frame;
import com.cqqy.hrvas.comm.m101.model.ModuleInfoResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * 模块信息应答处理器
 */
@Slf4j
public class ModuleInfoHandler implements FrameHandler {

    private final ResponseProcessor responseProcessor;

    public ModuleInfoHandler(FrameContext frameContext) {
        this.responseProcessor = new ResponseProcessor(frameContext);
    }

    @Override
    public void handle(Frame frame, FrameSender frameSender) {
        try {
            ModuleInfoResponse response = ModuleInfoResponse.fromBytes(frame.getData());
            responseProcessor.completeResponse(frame, frameSender);
            log.info("Received module info: {}", response);
        } catch (Exception e) {
            log.error("Failed to parse module info response", e);
        }
    }
    
    @Override
    public byte getCommandId() {
        return CommandId.MODULE_INFO_RESPONSE;
    }
}