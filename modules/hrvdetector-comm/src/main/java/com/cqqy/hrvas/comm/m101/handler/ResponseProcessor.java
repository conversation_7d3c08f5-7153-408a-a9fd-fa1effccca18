package com.cqqy.hrvas.comm.m101.handler;

import com.cqqy.hrvas.comm.m101.FrameContext;
import com.cqqy.hrvas.comm.m101.FrameSender;
import com.cqqy.hrvas.comm.m101.frame.Frame;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;

/**
 * 响应处理器
 * 提供通用的响应处理功能
 */
@Slf4j
public class ResponseProcessor {
    private final FrameContext frameContext;

    public ResponseProcessor(FrameContext frameContext) {
        this.frameContext = frameContext;
    }

    /**
     * 完成响应请求
     */
    public void completeResponse(Frame frame, FrameSender frameSender) {
        int sequenceNumber = frame.getSequenceNumber();
        CompletableFuture<Frame> future = frameContext.removePendingResponse(sequenceNumber);
        if (future != null && !future.isDone()) {
            frameContext.getTimeoutManager().cancelTimeout(sequenceNumber);
            frameContext.getRetryManager().cancelRetry(sequenceNumber);
            future.complete(frame);
            log.debug("Completed future for sequence: {}", sequenceNumber);
        }
    }
}