package com.cqqy.hrvas.comm.m101.handler;

import com.cqqy.hrvas.comm.event.Transfer;
import com.cqqy.hrvas.comm.m101.FrameHandler;
import com.cqqy.hrvas.comm.m101.FrameSender;
import com.cqqy.hrvas.comm.m101.frame.CommandId;
import com.cqqy.hrvas.comm.m101.frame.Frame;
import com.cqqy.hrvas.comm.m101.model.WaveformData;
import com.cqqy.hrvas.comm.service.sse.SseEmitterService;
import com.cqqy.hrvas.comm.service.waveform.WaveformService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.cqqy.hrvas.constant.WebSocketMsgType.DE_WAVEFORM;

/**
 * 波形数据处理器
 * 用于处理0x90命令的波形数据帧
 */
@Slf4j
@Component
public class WaveformHandler implements FrameHandler {
    private final SseEmitterService sseEmitterService;

    private final WaveformService waveformService;

    private final Transfer transfer;
    
    public WaveformHandler(SseEmitterService sseEmitterService, WaveformService waveformService, Transfer transfer) {
        this.sseEmitterService = sseEmitterService;
        this.waveformService = waveformService;
        this.transfer = transfer;
    }
    
    @Override
    public void handle(Frame frame, FrameSender frameSender) {
        if (frame == null || frame.getData() == null) {
            log.warn("Received null frame or frame data");
            return;
        }
        
        try {
            // 验证数据长度是否符合预期(1字节标志位 + 8字节波形数据)
            if (frame.getData().length != 9) {
                log.warn("Invalid waveform data length: {}", frame.getData().length);
                return;
            }

            // 解析波形数据
            WaveformData waveformData = parseWaveformData(frame.getSequenceNumber(), frame.getData());
            // 保存文件
            waveformService.receiveData(waveformData);
            // 推送到服务器
            transfer.setEventPublisher(waveformData, DE_WAVEFORM);

            // 通过SSE广播波形数据给订阅了波形数据的客户端
            sseEmitterService.broadcast(SseEmitterService.WAVEFORM_SUBSCRIPTION, waveformData);
            
           log.trace("Processed waveform data and broadcasted to {} clients",
                   sseEmitterService.getActiveEmitterCount());
        } catch (Exception e) {
            log.error("Error processing waveform data for sequence {}: {}", 
                    frame.getSequenceNumber(), e.getMessage(), e);
        }
    }
    
    @Override
    public byte getCommandId() {
        return CommandId.WAVEFORM_DATA;
    }
    
    /**
     * 解析波形数据
     * @param data 原始数据
     * @return 波形数据传输对象
     */
    private WaveformData parseWaveformData(int sequence, byte[] data) {
        WaveformData waveformData = new WaveformData();
        
        // 设置时间戳(毫秒级精度)
        waveformData.setTimestamp(System.currentTimeMillis());
        
        // 从序列号中解析R波和T波位置（按字节顺序）
        int rWavePosition = ((sequence >> 8) & 0xFF) | ((sequence & 0xFF) << 8);
        int tWavePosition = ((sequence >> 24) & 0xFF) | ((sequence >> 16) & 0xFF) << 8;
        waveformData.setRWavePosition(rWavePosition);
        waveformData.setTWavePosition(tWavePosition);
        
        // 解析标志位
        byte flags = data[0];
        boolean hasPace = (flags & 0x01) != 0;    // Bit0: PACE标志
        boolean hasRWave = (flags & 0x10) != 0;   // Bit4: R波标志 
        boolean hasTWave = (flags & 0x80) != 0;   // Bit7: T波标志
        
        waveformData.setPaceDetected(hasPace);
        waveformData.setRWaveDetected(hasRWave);
        waveformData.setTWaveDetected(hasTWave);
        
        // 解析各通道波形数据
        int ecgI = ((data[2] & 0xFF) << 8) | (data[1] & 0xFF);  // 字节2:低8位, 字节3:高8位
        int ecgII = ((data[4] & 0xFF) << 8) | (data[3] & 0xFF);  // 字节4:低8位, 字节5:高8位
        int ecgV1 = ((data[6] & 0xFF) << 8) | (data[5] & 0xFF);  // 字节6:低8位, 字节7:高8位
        int resp = ((data[8] & 0xFF) << 8) | (data[7] & 0xFF);   // 字节8:低8位, 字节9:高8位
        
        waveformData.setEcgChannelI(ecgI);
        waveformData.setEcgChannelII(ecgII);
        waveformData.setEcgChannelVI(ecgV1);
        waveformData.setRespWave(resp);
        
        return waveformData;
    }
}