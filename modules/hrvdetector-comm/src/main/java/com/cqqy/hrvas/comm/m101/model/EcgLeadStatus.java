package com.cqqy.hrvas.comm.m101.model;

import lombok.Getter;
import lombok.Setter;

/**
 * ECG导联状态类
 * 用于封装ECG导联的连接状态和信号状态
 */
public class EcgLeadStatus {
    /** 是否为5导联模式 */
    @Getter
    @Setter
    private boolean fiveLeadMode = false;

    /** 是否为12导联模式 */
    @Getter
    @Setter
    private boolean twelveLeadMode = false;

    /** 电极状态 (true表示断开) */
    @Getter
    @Setter
    private boolean rlDisconnected = false;
    @Getter
    @Setter
    private boolean v1Disconnected = false;
    @Getter
    @Setter
    private boolean llDisconnected = false;
    @Getter
    @Setter
    private boolean laDisconnected = false;
    @Getter
    @Setter
    private boolean raDisconnected = false;
    @Getter
    @Setter
    private boolean v2Disconnected = false;
    @Getter
    @Setter
    private boolean v3Disconnected = false;
    @Getter
    @Setter
    private boolean v4Disconnected = false;
    @Getter
    @Setter
    private boolean v5Disconnected = false;
    @Getter
    @Setter
    private boolean v6Disconnected = false;

    /** 通道信号状态 (true表示信号不存在) */
    @Getter
    @Setter
    private boolean channelINoSignal = false;
    @Getter
    @Setter
    private boolean channelIINoSignal = false;
    @Getter
    @Setter
    private boolean channelV1NoSignal = false;
    @Getter
    @Setter
    private boolean channelV2NoSignal = false;
    @Getter
    @Setter
    private boolean channelV3NoSignal = false;
    @Getter
    @Setter
    private boolean channelV4NoSignal = false;
    @Getter
    @Setter
    private boolean channelV5NoSignal = false;
    @Getter
    @Setter
    private boolean channelV6NoSignal = false;

    private static volatile EcgLeadStatus instance;

    private EcgLeadStatus() {
        // 私有构造函数
    }

    public static EcgLeadStatus getInstance() {
        if (instance == null) {
            synchronized (EcgLeadStatus.class) {
                if (instance == null) {
                    instance = new EcgLeadStatus();
                }
            }
        }
        return instance;
    }
}