package com.cqqy.hrvas.comm.m101.model;

import lombok.Data;

/**
 * 握手请求
 */
@Data
public class HandshakeRequest {
    /** 设备类型 */
    private byte deviceType;
    /** 设备版本 */
    private byte deviceVersion;
    /** 协议版本 */
    private byte protocolVersion;
    
    /**
     * 从字节数组解析握手请求
     * @param data 字节数组
     * @return 握手请求对象
     */
    public static HandshakeRequest fromBytes(byte[] data) {
        if (data == null || data.length < 3) {
            throw new IllegalArgumentException("Invalid handshake request data");
        }
        
        HandshakeRequest request = new HandshakeRequest();
        request.setDeviceType(data[0]);
        request.setDeviceVersion(data[1]);
        request.setProtocolVersion(data[2]);
        return request;
    }
}