package com.cqqy.hrvas.comm.m101.model;

import lombok.Data;

/**
 * 模块信息响应类
 * 用于封装模块状态查询的结果
 */
@Data
public class ModuleInfoResponse {
    /** 版本信息 */
    private String version;
    
    /** 算法类型 */
    private int algorithmType;
    
    /** 算法版本 */
    private int algorithmVersion;
    
    /** 通讯协议类型 */
    private int protocolType;
    
    /** 通讯协议版本 */
    private int protocolVersion;
    
    /** 自检结果 */
    private boolean selfTestPassed;
    
    /**
     * 从字节数组解析模块信息
     * @param data 字节数组
     * @return 模块信息对象
     */
    public static ModuleInfoResponse fromBytes(byte[] data) {
        if (data == null || data.length < 9) {
            throw new IllegalArgumentException("Invalid module info data");
        }
        
        ModuleInfoResponse response = new ModuleInfoResponse();
        
        // 解析版本信息
        response.version = String.format("%d.%d.%d.%d", 
            data[0] & 0xFF, data[1] & 0xFF, data[2] & 0xFF, data[3] & 0xFF);
        
        // 解析算法信息
        response.algorithmType = data[4] & 0xFF;
        response.algorithmVersion = data[5] & 0xFF;
        
        // 解析通讯协议信息
        response.protocolType = data[6] & 0xFF;
        response.protocolVersion = data[7] & 0xFF;
        
        // 解析自检结果
        response.selfTestPassed = data[8] == 0x00;
        
        return response;
    }
}