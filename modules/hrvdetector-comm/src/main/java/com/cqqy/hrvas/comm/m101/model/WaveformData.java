package com.cqqy.hrvas.comm.m101.model;

import lombok.Data;

/**
 * 心电呼吸波形数据传输对象
 * 
 * 序列号结构:
 * 字节1-4: 包含R波和T波位置信息
 *   字节1: R波位置低8位
 *   字节2: R波位置高8位
 *   字节3: T波位置低8位  
 *   字节4: T波位置高8位
 * 
 * 数据段结构(9字节):
 *   字节1: 状态标志位
 *     Bit0: PACE检测标志(1=检测到,0=未检测到)
 *     Bit4: R波检测标志(1=检测到,0=未检测到)
 *     Bit7: T波检测标志(1=检测到,0=未检测到)
 *   字节2-3: 心电通道I波形数据(低8位+高8位)
 *   字节4-5: 心电通道II波形数据(低8位+高8位)
 *   字节6-7: 心电通道V1波形数据(低8位+高8位)
 *   字节8-9: 呼吸波形数据(低8位+高8位)
 * 
 * 心电呼吸数据范围在 0-4096，心电呼吸上传数据全部加上 2048 的基准值。心电 1mV 的信号对应采样幅值
 * 在 800 左右（不同模式滤波下会有一些差别）。R 波，T 波位置是相对于当前波形的位置信息。
 * 未识别到 R，T 波标志时，R，T 波位置默认为 0。
 */
@Data
public class WaveformData {
    // 时间戳(毫秒)
    private long timestamp;
    
    // 波位置信息
    private int rWavePosition; // R波位置
    private int tWavePosition; // T波位置
    
    // 标志位
    private boolean paceDetected;  // 是否检测到PACE信号
    private boolean rWaveDetected; // 是否检测到R波
    private boolean tWaveDetected; // 是否检测到T波
    
    private int ecgChannelI;  // 心电通道1波形数据
    private int ecgChannelII; // 心电通道2波形数据
    private int ecgChannelVI; // 心电通道V1波形数据
    private int respWave;  // 呼吸波形数据
}