package com.cqqy.hrvas.comm.m101.utils;

import java.util.concurrent.locks.ReentrantLock;

import com.cqqy.hrvas.comm.m101.frame.Frame;

import lombok.extern.slf4j.Slf4j;

/**
 * 线程安全的帧缓冲区管理器
 * 用于在多线程环境下处理跨包数据和粘包情况
 */
@Slf4j
public class FrameBuffer {
    private static final int MAX_BUFFER_SIZE = 8192 * 1024; // 最大缓冲区大小
    private final byte[] buffer; // 数据缓冲区
    private int position; // 当前位置
    private final ReentrantLock bufferLock = new ReentrantLock();

    public FrameBuffer() {
        this.buffer = new byte[MAX_BUFFER_SIZE];
        this.position = 0;
    }

    /**
     * 添加数据到缓冲区 (线程安全)
     * @param data 新接收的数据
     * @return 是否添加成功
     */
    public boolean append(byte[] data) {
        if (data == null || data.length == 0) {
            return true;
        }

        bufferLock.lock();
        try {
            // 检查缓冲区是否有足够空间
            if (position + data.length > MAX_BUFFER_SIZE) {
                // 如果缓冲区已满，尝试清理无效数据
                compact();

                // 再次检查空间
                if (position + data.length > MAX_BUFFER_SIZE) {
                    log.warn("Buffer overflow, discarding {} bytes", position);
                    reset(); // 缓冲区已满，清空所有数据
                }
            }

            // 复制数据到缓冲区
            System.arraycopy(data, 0, buffer, position, data.length);
            position += data.length;
            return true;
        } finally {
            bufferLock.unlock();
        }
    }

    /**
     * 获取当前缓冲区中的所有数据 (线程安全)
     * @return 缓冲区数据
     */
    public byte[] getBuffer() {
        bufferLock.lock();
        try {
            byte[] data = new byte[position];
            System.arraycopy(buffer, 0, data, 0, position);
            return data;
        } finally {
            bufferLock.unlock();
        }
    }

    /**
     * 移除已处理的数据 (线程安全)
     * @param length 要移除的字节数
     */
    public void consume(int length) {
        if (length <= 0) {
            return;
        }

        bufferLock.lock();
        try {
            if (length > position) {
                log.warn("Attempt to consume more bytes than available: {} > {}", length, position);
                return;
            }

            // 移动剩余数据到缓冲区开始位置
            int remaining = position - length;
            if (remaining > 0) {
                System.arraycopy(buffer, length, buffer, 0, remaining);
            }
            position = remaining;
        } finally {
            bufferLock.unlock();
        }
    }

    /**
     * 压缩缓冲区，清理无效数据 (内部方法，已在锁内调用)
     */
    private void compact() {
        // 查找第一个帧头位置
        int start = 0;
        while (start < position && buffer[start] != Frame.FRAME_START_MARK) {
            start++;
        }

        if (start > 0 && start < position) {
            // 移动有效数据到缓冲区开始位置
            int length = position - start;
            System.arraycopy(buffer, start, buffer, 0, length);
            position = length;
            log.debug("Compacted buffer, removed {} bytes of invalid data", start);
        } else if (start == position) {
            // 没有找到有效的帧头，清空缓冲区
            reset();
            log.debug("No valid frame start mark found, buffer cleared");
        }
    }

    /**
     * 重置缓冲区 (内部方法，已在锁内调用)
     */
    private void reset() {
        position = 0;
    }

    /**
     * 获取当前缓冲区使用量 (线程安全)
     * @return 当前使用的字节数
     */
    public int getSize() {
        bufferLock.lock();
        try {
            return position;
        } finally {
            bufferLock.unlock();
        }
    }

    /**
     * 检查缓冲区是否为空 (线程安全)
     * @return 是否为空
     */
    public boolean isEmpty() {
        bufferLock.lock();
        try {
            return position == 0;
        } finally {
            bufferLock.unlock();
        }
    }
}