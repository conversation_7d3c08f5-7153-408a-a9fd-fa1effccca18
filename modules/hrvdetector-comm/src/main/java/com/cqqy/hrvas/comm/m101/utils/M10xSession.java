package com.cqqy.hrvas.comm.m101.utils;

import com.cqqy.hrvas.comm.m101.model.EcgLeadStatus;

import lombok.Getter;
import lombok.Setter;

/**
 * M10x 会话状态
 */
public class M10xSession {
    
    /** 模块状态 -1 未知 0 正常 1 系统忙 */
    @Getter
    @Setter
    private int moduleStatus = -1;

    /** ECG导联状态 */
    @Getter
    private final EcgLeadStatus ecgLeadStatus = EcgLeadStatus.getInstance();

    private static volatile M10xSession instance;

    private M10xSession() {
        // 私有构造函数
    }

    public static M10xSession getInstance() {
        if (instance == null) {
            synchronized (M10xSession.class) {
                if (instance == null) {
                    instance = new M10xSession();
                }
            }
        }
        return instance;
    }
}
