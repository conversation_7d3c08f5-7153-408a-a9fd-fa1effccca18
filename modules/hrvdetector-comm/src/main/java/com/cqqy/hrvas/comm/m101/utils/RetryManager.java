package com.cqqy.hrvas.comm.m101.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * 重试管理器
 * 负责管理帧的重试逻辑和策略配置
 */
@Slf4j
public class RetryManager {
    private final ScheduledExecutorService scheduler;
    private final Map<Integer, RetryContext> retryContexts;
    private int defaultMaxRetries;
    private long defaultRetryDelay;
    
    public RetryManager(ScheduledExecutorService scheduler, int defaultMaxRetries, long defaultRetryDelay) {
        this.scheduler = scheduler;
        this.defaultMaxRetries = defaultMaxRetries;
        this.defaultRetryDelay = defaultRetryDelay;
        this.retryContexts = new ConcurrentHashMap<>();
    }
    
    /**
     * 重试上下文
     */
    private static class RetryContext {
        private int retryCount;
        private final int maxRetries;
        private final long retryDelay;
        private ScheduledFuture<?> retryTask;
        private final Consumer<Integer> retryHandler;
        
        public RetryContext(int maxRetries, long retryDelay, Consumer<Integer> retryHandler) {
            this.maxRetries = maxRetries;
            this.retryDelay = retryDelay;
            this.retryHandler = retryHandler;
            this.retryCount = 0;
        }
        
        public boolean canRetry() {
            return maxRetries == -1 || retryCount < maxRetries;
        }
        
        public void incrementRetryCount() {
            retryCount++;
        }
        
        public void setRetryTask(ScheduledFuture<?> task) {
            cancelRetryTask();
            this.retryTask = task;
        }
        
        public void cancelRetryTask() {
            if (retryTask != null) {
                retryTask.cancel(false);
                retryTask = null;
            }
        }
        
        public void handleRetry(int sequenceNumber) {
            if (retryHandler != null) {
                retryHandler.accept(sequenceNumber);
            }
        }
    }
    
    /**
     * 初始化重试上下文
     * @param sequenceNumber 帧序列号
     * @param maxRetries 最大重试次数
     * @param retryDelay 重试延迟（毫秒）
     * @param retryHandler 重试处理器
     */
    public void initRetry(int sequenceNumber, int maxRetries, long retryDelay, Consumer<Integer> retryHandler) {
        RetryContext context = new RetryContext(maxRetries, retryDelay, retryHandler);
        retryContexts.put(sequenceNumber, context);
        log.debug("Initialized retry context for sequence: {}, maxRetries: {}", sequenceNumber, maxRetries);
    }
    
    /**
     * 使用默认参数初始化重试上下文
     * @param sequenceNumber 帧序列号
     * @param retryHandler 重试处理器
     */
    public void initRetry(int sequenceNumber, Consumer<Integer> retryHandler) {
        initRetry(sequenceNumber, defaultMaxRetries, defaultRetryDelay, retryHandler);
    }
    
    /**
     * 处理重试
     * @param sequenceNumber 帧序列号
     * @return 是否可以继续重试
     */
    public boolean handleRetry(int sequenceNumber) {
        RetryContext context = retryContexts.get(sequenceNumber);
        if (context == null) {
            return false;
        }
        
        if (!context.canRetry()) {
            retryContexts.remove(sequenceNumber);
            log.warn("Max retries reached for sequence: {}", sequenceNumber);
            return false;
        }
        
        context.incrementRetryCount();
        ScheduledFuture<?> retryTask = scheduler.schedule(
            () -> context.handleRetry(sequenceNumber),
            context.retryDelay,
            TimeUnit.MILLISECONDS
        );
        
        context.setRetryTask(retryTask);
        log.debug("Scheduled retry {} for sequence: {}", context.retryCount, sequenceNumber);
        return true;
    }
    
    /**
     * 取消重试
     * @param sequenceNumber 帧序列号
     */
    public void cancelRetry(int sequenceNumber) {
        RetryContext context = retryContexts.remove(sequenceNumber);
        if (context != null) {
            context.cancelRetryTask();
            log.debug("Cancelled retry for sequence: {}", sequenceNumber);
        }
    }
    
    /**
     * 设置默认最大重试次数
     * @param maxRetries 最大重试次数
     */
    public void setDefaultMaxRetries(int maxRetries) {
        this.defaultMaxRetries = maxRetries;
    }
    
    /**
     * 设置默认重试延迟
     * @param retryDelay 重试延迟（毫秒）
     */
    public void setDefaultRetryDelay(long retryDelay) {
        this.defaultRetryDelay = retryDelay;
    }
}