package com.cqqy.hrvas.comm.m101.utils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import com.cqqy.hrvas.comm.m101.frame.Frame;
import com.cqqy.hrvas.comm.m101.frame.FrameType;

import lombok.extern.slf4j.Slf4j;

/**
 * 序列号管理器
 * 用于管理上位机序列号和处理应答包匹配
 */
@Slf4j
public class SequenceManager {
    /** 序列号生成器 */
    private final AtomicInteger sequenceGenerator = new AtomicInteger(0);
    
    /** 等待应答的帧映射表 */
    private final Map<Integer, Frame> pendingFrames = new ConcurrentHashMap<>();
    
    /**
     * 获取下一个序列号
     * @return 序列号
     */
    public int nextSequence() {
        return sequenceGenerator.incrementAndGet();
    }
    
    /**
     * 添加等待应答的帧
     * @param frame 需要等待应答的帧
     */
    public void addPendingFrame(Frame frame) {
        if (frame != null && frame.getFrameType() != FrameType.DA.getCode() 
            && frame.getFrameType() != FrameType.DD.getCode()) {
            pendingFrames.put(frame.getSequenceNumber(), frame);
            log.debug("Added pending frame with sequence {}", frame.getSequenceNumber());
        }
    }
    
    /**
     * 移除等待应答的帧
     * @param sequenceNumber 序列号
     * @return 被移除的帧
     */
    public Frame removePendingFrame(int sequenceNumber) {
        Frame frame = pendingFrames.remove(sequenceNumber);
        if (frame != null) {
            log.debug("Removed pending frame with sequence {}", sequenceNumber);
        }
        return frame;
    }
    
    /**
     * 获取等待应答的帧
     * @param sequenceNumber 序列号
     * @return 等待应答的帧
     */
    public Frame getPendingFrame(int sequenceNumber) {
        return pendingFrames.get(sequenceNumber);
    }
    
    /**
     * 检查是否有等待应答的帧
     * @param sequenceNumber 序列号
     * @return 是否存在等待应答的帧
     */
    public boolean hasPendingFrame(int sequenceNumber) {
        return pendingFrames.containsKey(sequenceNumber);
    }
    
    /**
     * 清空所有等待应答的帧
     */
    public void clearPendingFrames() {
        pendingFrames.clear();
        log.debug("Cleared all pending frames");
    }
}