package com.cqqy.hrvas.comm.m101.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * 超时管理器
 * 负责管理帧的超时检测和通知机制
 */
@Slf4j
public class TimeoutManager {
    private final ScheduledExecutorService scheduler;
    private final Map<Integer, TimeoutContext> timeoutContexts;
    private long defaultTimeout;
    
    public TimeoutManager(ScheduledExecutorService scheduler, long defaultTimeout) {
        this.scheduler = scheduler;
        this.defaultTimeout = defaultTimeout;
        this.timeoutContexts = new ConcurrentHashMap<>();
    }
    
    /**
     * 超时上下文
     */
    private static class TimeoutContext {
        private final ScheduledFuture<?> timeoutTask;
        private final Consumer<Integer> timeoutHandler;
        
        public TimeoutContext(ScheduledFuture<?> timeoutTask, Consumer<Integer> timeoutHandler) {
            this.timeoutTask = timeoutTask;
            this.timeoutHandler = timeoutHandler;
        }
        
        public void cancel() {
            if (timeoutTask != null) {
                timeoutTask.cancel(false);
            }
        }
        
        public void handleTimeout(int sequenceNumber) {
            if (timeoutHandler != null) {
                timeoutHandler.accept(sequenceNumber);
            }
        }
    }
    
    /**
     * 设置帧的超时检测
     * @param sequenceNumber 帧序列号
     * @param timeout 超时时间（毫秒）
     * @param timeoutHandler 超时处理器
     */
    public void setTimeout(int sequenceNumber, long timeout, Consumer<Integer> timeoutHandler) {
        // 取消已存在的超时检测
        cancelTimeout(sequenceNumber);
        
        // 创建新的超时任务
        ScheduledFuture<?> timeoutTask = scheduler.schedule(
            () -> handleTimeout(sequenceNumber),
            timeout,
            TimeUnit.MILLISECONDS
        );
        
        // 保存超时上下文
        timeoutContexts.put(sequenceNumber, new TimeoutContext(timeoutTask, timeoutHandler));
        log.debug("Set timeout {} ms for sequence: {}", timeout, sequenceNumber);
    }
    
    /**
     * 使用默认超时时间设置帧的超时检测
     * @param sequenceNumber 帧序列号
     * @param timeoutHandler 超时处理器
     */
    public void setTimeout(int sequenceNumber, Consumer<Integer> timeoutHandler) {
        setTimeout(sequenceNumber, defaultTimeout, timeoutHandler);
    }
    
    /**
     * 取消帧的超时检测
     * @param sequenceNumber 帧序列号
     */
    public void cancelTimeout(int sequenceNumber) {
        TimeoutContext context = timeoutContexts.remove(sequenceNumber);
        if (context != null) {
            context.cancel();
            log.debug("Cancelled timeout for sequence: {}", sequenceNumber);
        }
    }
    
    /**
     * 处理超时事件
     * @param sequenceNumber 帧序列号
     */
    private void handleTimeout(int sequenceNumber) {
        TimeoutContext context = timeoutContexts.remove(sequenceNumber);
        if (context != null) {
            context.handleTimeout(sequenceNumber);
            log.debug("Timeout occurred for sequence: {}", sequenceNumber);
        }
    }
    
    /**
     * 设置默认超时时间
     * @param timeout 超时时间（毫秒）
     */
    public void setDefaultTimeout(long timeout) {
        this.defaultTimeout = timeout;
    }
    
    /**
     * 获取默认超时时间
     * @return 默认超时时间（毫秒）
     */
    public long getDefaultTimeout() {
        return defaultTimeout;
    }
}