package com.cqqy.hrvas.comm.powersupply;

import com.fazecast.jSerialComm.SerialPort;
import com.fazecast.jSerialComm.SerialPortDataListener;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class JPsSerialCommPortManager implements PsSerialPortManager {
    private final List<SerialPortDataListener> listeners = new ArrayList<>();
    private SerialPort serialPort;

    @Override
    public boolean openPort(String portName, int baudRate, int dataBits, int stopBits, int parity) {
        // 如果已经打开，先关闭
        if (serialPort != null && serialPort.isOpen()) {
            closePort();
        }
        
        try {
            // 查找指定名称的串口
            SerialPort[] ports = SerialPort.getCommPorts();
            for (SerialPort port : ports) {
                if (port.getSystemPortName().equals(portName) || port.getDescriptivePortName().contains(portName)) {
                    serialPort = port;
                    break;
                }
            }
            
            if (serialPort == null) {
                log.error("Port {} not found", portName);
                return false;
            }
            
            // 配置串口参数
            serialPort.setBaudRate(baudRate);
            serialPort.setNumDataBits(dataBits);
            serialPort.setNumStopBits(stopBits);
            serialPort.setParity(parity);
            
            // 设置读取超时
            serialPort.setComPortTimeouts(SerialPort.TIMEOUT_READ_SEMI_BLOCKING, 1000, 0);
            
            // 添加数据监听器
            for (SerialPortDataListener listener : listeners) {
                serialPort.addDataListener(listener);
            }
            
            // 打开串口
            boolean opened = serialPort.openPort();
            if (opened) {
                log.info("Port {} opened successfully", portName);
            } else {
                log.error("Failed to open port {}", portName);
            }
            
            return opened;
        } catch (Exception e) {
            log.error("Error opening port {}", portName, e);
            notifyError(e);
            return false;
        }
    }


    
    @Override
    public void closePort() {
        if (serialPort != null && serialPort.isOpen()) {
            try {
                serialPort.removeDataListener();
                serialPort.closePort();
                log.info("Port closed");
            } catch (Exception e) {
                log.error("Error closing port", e);
                notifyError(e);
            } finally {
                serialPort = null;
            }
        }
    }

    @Override
    public boolean sendRawData(byte[] data) {
        if (serialPort == null || !serialPort.isOpen() || data == null || data.length == 0) {
            return false;
        }
        
        try {
            int bytesWritten = serialPort.writeBytes(data, data.length);
            boolean success = bytesWritten == data.length;
            
            if (success) {
                log.debug("Sent {} bytes successfully", data.length);
            } else {
                log.error("Failed to send all data, sent {}/{} bytes", bytesWritten, data.length);
            }
            
            return success;
        } catch (Exception e) {
            log.error("Error sending data", e);
            notifyError(e);
            return false;
        }
    }

    @Override
    public String[] getAvailablePorts() {
        SerialPort[] ports = SerialPort.getCommPorts();
        String[] portNames = new String[ports.length];
        
        for (int i = 0; i < ports.length; i++) {
            portNames[i] = ports[i].getSystemPortName();
        }
        
        return portNames;
    }

    @Override
    public void addDataListener(SerialPortDataListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    @Override
    public void removeDataListener(SerialPortDataListener listener) {
        listeners.remove(listener);
    }
    
    @Override
    public boolean isPortOpen() {
        return serialPort != null && serialPort.isOpen();
    }

    /**
     * 通知所有监听器发生错误
     * @param e 异常信息
     */
    private void notifyError(Exception e) {
        // TODO: 实现错误通知逻辑
    }
}
