package com.cqqy.hrvas.comm.powersupply;

import com.fazecast.jSerialComm.SerialPortDataListener;

/**
 * 串口管理器接口
 */
public interface PsSerialPortManager {
    
    /**
     * 打开串口
     * @param portName 串口名称
     * @param baudRate 波特率
     * @param dataBits 数据位
     * @param stopBits 停止位
     * @param parity 校验位
     * @return 是否成功打开
     */
    boolean openPort(String portName, int baudRate, int dataBits, int stopBits, int parity);
    
    /**
     * 关闭串口
     */
    void closePort();
    
    /**
     * 发送原始数据
     * @param data 要发送的数据
     * @return 是否发送成功
     */
    boolean sendRawData(byte[] data);
    
    /**
     * 获取可用串口列表
     * @return 可用串口列表
     */
    String[] getAvailablePorts();
    
    /**
     * 判断串口是否已打开
     * @return 是否已打开
     */
    boolean isPortOpen();

    /**
     * 添加串口数据监听器
     * @param listener 监听器
     */
    void addDataListener(SerialPortDataListener listener);

    /**
     * 移除串口数据监听器
     * @param listener 监听器
     */
    void removeDataListener(SerialPortDataListener listener);
}