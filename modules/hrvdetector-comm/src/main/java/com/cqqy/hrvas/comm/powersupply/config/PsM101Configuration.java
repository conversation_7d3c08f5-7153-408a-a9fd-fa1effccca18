package com.cqqy.hrvas.comm.powersupply.config;

import com.cqqy.hrvas.comm.powersupply.JPsSerialCommPortManager;
import com.cqqy.hrvas.comm.powersupply.PsSerialPortManager;
import com.cqqy.hrvas.comm.powersupply.m101.PsCommonHandler;
import com.cqqy.hrvas.comm.powersupply.service.PsEarlyWarningService;
import com.cqqy.hrvas.comm.service.sse.SseEmitterService;
import com.cqqy.hrvas.util.CacheUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.cache.CacheManager;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@RequiredArgsConstructor
@Configuration
public class PsM101Configuration implements ApplicationContextAware {

    private final SseEmitterService sseEmitterService;

    private final CacheUtil cacheUtil;

    private final CacheManager cacheManager;

    @Bean
    public PsSerialPortManager psSerialPortManager() {
        return new JPsSerialCommPortManager();
    }

    @Bean
    public PsCommonHandler psCommonHandler() {
        return new PsCommonHandler(psEarlyWarningService());
    }

    @Bean
    public PsEarlyWarningService psEarlyWarningService() {
        return new PsEarlyWarningService(sseEmitterService, cacheUtil, cacheManager);
    }
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    }
}