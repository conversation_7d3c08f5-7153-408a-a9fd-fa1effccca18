package com.cqqy.hrvas.comm.powersupply.config;

import com.cqqy.hrvas.comm.powersupply.JPsSerialCommPortManager;
import com.cqqy.hrvas.comm.powersupply.PsSerialPortManager;
import com.cqqy.hrvas.comm.powersupply.m101.PsCommonHandler;
import com.cqqy.hrvas.comm.powersupply.m101.PsFrameDataListener;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PsM101Initializer {
    @Value("${hrvdetector.comm.power.port:/dev/cu.usbserial}")
    private String portName;

    @Value("${hrvdetector.comm.power.maxRetries:3}")
    private int maxRetries;

    @Value("${hrvdetector.comm.power.retryInterval:2000}")
    private int retryInterval;

    private final int baudRate;
    private final int dataBits;
    private final int stopBits;
    private final int parity;

    private final PsSerialPortManager psSerialPortManager;
    private final PsCommonHandler psCommonHandler;

    public PsM101Initializer(PsSerialPortManager psSerialPortManager, PsCommonHandler psCommonHandler) {
        this.psSerialPortManager = psSerialPortManager;
        this.psCommonHandler = psCommonHandler;
        this.baudRate = 115200;
        this.dataBits = 8;
        this.stopBits = 1;
        this.parity = 0;

    }

    @PostConstruct
    public void init() {
        JPsSerialCommPortManager portManager = (JPsSerialCommPortManager) psSerialPortManager;
        portManager.addDataListener(new PsFrameDataListener(psCommonHandler));

        boolean opened = false;
        for (int i = 0; i < maxRetries; i++) {
            opened = portManager.openPort(portName, baudRate, dataBits, stopBits, parity);
            if (opened) {
                log.info("串口 {} 已成功打开，波特率 {}", portName, baudRate);
                break;
            }

            log.warn("无法打开串口 {} (尝试 {}/{})", portName, i+1, maxRetries);
            try {
                Thread.sleep(retryInterval);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("串口打开重试被中断");
                break;
            }
        }

        if (!opened) {
            log.error("无法打开串口 {}，已达到最大重试次数 {}", portName, maxRetries);
        }
    }
}