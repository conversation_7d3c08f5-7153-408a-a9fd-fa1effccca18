package com.cqqy.hrvas.comm.powersupply.m101;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@AllArgsConstructor
@Getter
public enum PsBatStateEnum {

    /** Error Code **/
    OK(0x0000, "正常"),
    Busy(0x0001, "繁忙"),
    ReservedCommand(0x0002, "保留命令"),
    UnsupportedCommand(0x0003, "不支持的命令"),
    AccessDenied(0x0004, "访问被拒绝"),
    Overflow_Underflow(0x0005, "溢流/底流"),
    BadSize(0x0006, "错误大小"),
    UnknownError(0x0007, "未知错误"),
    /** Status Bits **/
    FULLY_DISCHARGED(0x0010, "全放电"),
    FULLY_CHARGED(0x0020, "满充电"),
    DISCHARGING(0x0040, "放电"),
    INITIALIZED(0x080, "初始化"),
    /** Alarm Code **/
    REMAINING_TIME_ALARM(0x0100, "剩余时间报警"), //RTA
    REMAINING_CAPACITY_ALARM(0x0200, "剩余容量报警"), //RCA
    Reserved1(0x0400, "保留命令"),
    TERMINATE_DISCHARGE_ALARM(0x0800, "终止放电"),
    OVER_TEMP_ALARM(0x1000, "温度过高"),
    Reserved2(0x2000, "保留命令"),
    TERMINATE_CHARGE_ALARM(0x4000, "终止充电"),
    OVER_CHARGED_ALARM(0x8000, "过度充电"),
    ;

    private final Integer code;

    private final String desc;

    public static String getDesc(Integer code) {
        PsBatStateEnum[] values = PsBatStateEnum.values();
        for (PsBatStateEnum value : values) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
