package com.cqqy.hrvas.comm.powersupply.m101;

import com.cqqy.hrvas.comm.m101.frame.Frame;
import com.cqqy.hrvas.comm.powersupply.service.PsEarlyWarningService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class PsCommonHandler {

    private final List<byte[]> buffer = new ArrayList<>();

    private final Object lock = new Object();

    private final PsEarlyWarningService psEarlyWarningService;

    public PsCommonHandler(PsEarlyWarningService psEarlyWarningService) {
        this.psEarlyWarningService = psEarlyWarningService;
    }

    public void handle(Frame frame) {
        if (frame == null || frame.getData() == null) {
            log.warn("Received null frame or frame data");
            return;
        }

        byte[] data = frame.getData();
        synchronized (lock) {
            buffer.add(data.clone());
            if (data[3] == 0x00) {
                processCompleteWave(buffer);
                buffer.clear();
            }
        }
    }

    private void processCompleteWave(List<byte[]> dataList) {
        double charge = 0;
        int chargerStatus = 0;
        for (byte[] data : dataList) {
            if (data[3] == 0x00) {
                charge = uartDataProcess(data);
            } else if (data[3] == 0x02) {
                int status = uartDataProcess(data);
                if (status != -1) {
                    chargerStatus = status;
                }
            }
        }
        psEarlyWarningService.handleBatteryLevel(charge / 100, chargerStatus);
    }

    private int uartDataProcess(byte[] data) {
        int devType = data[3] & 0xFF;
        int cmd = data[5] & 0xFF;

        if (devType == 0) { // bat
            if (cmd == 0x81) {
                return (data[17]*256+data[16]);
            }
        } else if (devType == 0x01) {
        } else if (devType == 2) {
            if (cmd == 0x88) {
                int chager_is_connect = data[6] & 0xFF;
                if (chager_is_connect > 0) { // 充电
                    return 1;
                } else {
                    return 0;
                }
            }
        } else {
        }
        return -1;
    }
}
