package com.cqqy.hrvas.comm.powersupply.m101;

import com.cqqy.hrvas.comm.m101.frame.Frame;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 帧解析器，负责从字节流中解析出完整的帧
 */
@Slf4j
public class PsFrameParser {

    public static String byteToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString();
    }

    /**
     * 解析字节数组为帧对象
     * @param data 字节数组
     * @return 帧对象列表
     */
    public static List<Frame> parse(byte[] data) {
        List<Frame> frames = new ArrayList<>();
        if (data == null) {
            return frames;
        }

        String s = byteToHex(data);
        System.out.println(s);
        int index = 0;
        while (index < data.length - 1) {
            // 查找帧头
            while (index < data.length && (data[index] != (byte) 0xAA || data[index+1] != (byte) 0x55)) {
                index++;
            }
            
            // 读取长度
            byte length = data[index + 2];
            
            // 检查长度是否合法
            if (index + length + 3 > data.length) {
                index++;
                continue;
            }
            
            try {
                // 创建帧对象
                Frame frame = new Frame();
                
                // 读取数据段
                int dataLength = length + 3;
                byte crc = 0x00;
                byte[] frameData = new byte[dataLength];
                System.arraycopy(data, index, frameData, 0, dataLength);
                frame.setData(frameData);
                byte POLYNOMIAL=0x07;
                for (int i = 0; i < frameData.length - 1 ; i++) {
                    crc ^= frameData[i];
                    for (byte bit = 0; bit < 8; bit++)
                    {
                        if ((crc & 0x80)!=0)
                        {  // 检查最高位
                            crc = (byte)((crc << 1) ^ POLYNOMIAL);
                        }
                        else
                        {
                            crc <<= 1;
                        }
                    }
                }

                // 验证校验和
                if (crc == data[index + dataLength - 1]) {
                    frames.add(frame);
                } else {
                    log.warn("powersupply ：Invalid checksum for frame at index {}", index);
                }
                
                // 移动到下一帧
                index += dataLength;
            } catch (Exception e) {
                log.error("powersupply ：Error parsing frame at index {}", index, e);
                index++;
            }
        }
        
        return frames;
    }
}
