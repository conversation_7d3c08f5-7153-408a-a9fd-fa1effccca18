package com.cqqy.hrvas.comm.powersupply.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.cqqy.hrvas.cache.DetectorConfigCache;
import com.cqqy.hrvas.comm.service.sse.SseEmitterService;
import com.cqqy.hrvas.exception.BizException;
import com.cqqy.hrvas.util.CacheUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import static com.cqqy.hrvas.constant.CacheConstant.CACHE_NAME;
import static com.cqqy.hrvas.constant.CacheConstant.CONFIG_KEY;

@Data
@RequiredArgsConstructor
@Slf4j
@Service
public class PsEarlyWarningService {

    private final SseEmitterService sseEmitterService;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    private final CacheUtil cacheUtil;

    private final CacheManager cacheManager;

    @Async
    public void handshakeResponse(String warning) {
        try {
            Thread.sleep(10000); // 等待10秒
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        sseEmitterService.broadcast(SseEmitterService.POWER_SUPPLY_EARLY_WARNING_SUBSCRIPTION, warning);
    }

    public void handleBatteryLevel(double charge, int chargerStatus) {
        DetectorConfigCache detectorConfigCache = cacheUtil.getFromCache(CACHE_NAME, CONFIG_KEY);
        if (ObjectUtil.isEmpty(detectorConfigCache)) {
            log.error("缓存获取失败：detectorConfig={}", detectorConfigCache);
            return;
        }

        Double alarmBatteryLow = detectorConfigCache.getAlarmBatteryLow();

        JSONObject warning = new JSONObject();
        warning.put("charge", charge);
        warning.put("warn", charge < alarmBatteryLow ? 1 : 0);
        warning.put("status", chargerStatus);
        System.out.println("电量："+charge+", 充电状态："+chargerStatus);
        sseEmitterService.broadcast(SseEmitterService.POWER_SUPPLY_EARLY_WARNING_SUBSCRIPTION, warning);
    }

    @Scheduled(fixedDelay = 30_000)
    public void networkConnections() {
        DetectorConfigCache detectorConfigCache = cacheUtil.getFromCache(CACHE_NAME, CONFIG_KEY);
        if (ObjectUtil.isEmpty(detectorConfigCache)) {
            log.error("缓存获取失败：detectorConfig={}", detectorConfigCache);
            return;
        }

        String netServerUrl = detectorConfigCache.getNetServerUrl();
        if (!isReachable(netServerUrl, 3_000)) {
            String warning =  StrUtil.format("连接服务器失败,地址：{}", netServerUrl);
            sseEmitterService.broadcast(SseEmitterService.POWER_SUPPLY_EARLY_WARNING_SUBSCRIPTION, warning);
        }
    }

    public static boolean isReachable(String ipAddress, int timeout) {
        try {
            URL url = new URL(ipAddress);
            InetAddress address = InetAddress.getByName(url.getHost());
            return address.isReachable(timeout);
        } catch (MalformedURLException e) {
            throw new BizException("获取服务器IP失败:" + e.getMessage());
        } catch (IOException e) {
            return false;
        }
    }
}
