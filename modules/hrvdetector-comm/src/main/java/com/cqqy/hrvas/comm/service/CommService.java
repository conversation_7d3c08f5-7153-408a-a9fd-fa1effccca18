package com.cqqy.hrvas.comm.service;

import cn.hutool.core.collection.CollUtil;
import com.cqqy.hrvas.comm.m101.FrameContext;
import com.cqqy.hrvas.comm.m101.FrameSender;
import com.cqqy.hrvas.comm.m101.frame.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@SuppressWarnings({"DuplicatedCode", "JavadocDeclaration", "unused"})
@RequiredArgsConstructor
@Slf4j
@Service
public class CommService {

    private final FrameSender frameSender;

    private final FrameContext frameContext;

    /**
     * 设置心电 ST 模板
     * @param iso ISO 位置
     * @param st ST 位置
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public boolean setEcgStTemplate(int iso, int st) throws ExecutionException, InterruptedException {
        if (iso < -50 || iso > -20 || st < 20 || st > 50) {
            throw new RuntimeException("ST模版值不在指定范围");
        }
        byte lowIso = (byte) (iso & 0xFF);
        byte highIso = (byte) ((iso >> 8) & 0xFF);
        byte lowSt = (byte) (st & 0xFF);
        //noinspection ConstantValue
        byte highSt = (byte) ((st >> 8) & 0xFF);
        byte[] bytes = new byte[4];
        bytes[0] = lowIso;
        bytes[1] = highIso;
        bytes[2] = lowSt;
        bytes[3] = highSt;

        Frame frame = FrameBuilder.dcFrameBuilder(frameContext)
                .withParamType(ParamType.ECG.getCode())     // 参数类型
                .withFrameType(FrameType.DC.getCode())      // 数据包类型
                .withFrameId(CommandId.SET_ECG_ST_TEMPLATE) // 数据包ID
                .withData(bytes)                            // 数据段
                .build();

        CompletableFuture<Frame> future = frameSender.sendFrameWithRetries(frame, 3);
        Frame responseFrame = future.get();

        return checkResponse(responseFrame, frame);
    }

    private boolean checkResponse(Frame responseFrame, Frame frame) {
        // 校验发送帧和回复帧序列号、长度、参数类型、帧类型、帧Id、和数据段
        return responseFrame.getSequenceNumber() == frame.getSequenceNumber() &&
                responseFrame.getLength() == (byte) 0x0B &&
                responseFrame.getParamType() == ParamType.ECG.getCode() &&
                responseFrame.getFrameType() == FrameType.DA.getCode() &&
                responseFrame.getCommandId() == CommandId.COMMON_COMMAND_RESPONSE &&
                responseFrame.getData()[0] == (byte) 0x07;
    }

    /**设置心电陷波
     * @param sink
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public boolean setEcgSinkWave(int sink) throws ExecutionException, InterruptedException {
        List<Integer> sinks = CollUtil.newArrayList(0, 1, 2, 16);
        if (!sinks.contains(sink)) {
            throw new RuntimeException("心电陷波不在指定范围");
        }
        byte[] bytes = new byte[1];
        bytes[0] = (byte) sink;

        Frame frame = FrameBuilder.dcFrameBuilder(frameContext)
                .withParamType(ParamType.ECG.getCode())     // 参数类型
                .withFrameType(FrameType.DC.getCode())      // 数据包类型
                .withFrameId(CommandId.SET_ECG_NOTCH)       // 数据包ID
                .withData(bytes)                            // 数据段
                .build();

        CompletableFuture<Frame> future = frameSender.sendFrameWithRetries(frame, 3);
        Frame responseFrame = future.get();
        return checkResponse(responseFrame, frame);
    }

    /**设置心率分析通道
     * @param Arrhy
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public boolean setEcgArrhythmia(int Arrhy) throws ExecutionException, InterruptedException {
        List<Integer> sinks = CollUtil.newArrayList(0, 1, 2, 16);
        if (!sinks.contains(Arrhy)) {
            throw new RuntimeException("心率分析通道不在指定范围");
        }
        byte[] bytes = new byte[1];
        bytes[0] = (byte) Arrhy;

        Frame frame = FrameBuilder.dcFrameBuilder(frameContext)
                .withParamType(ParamType.ECG.getCode())     // 参数类型
                .withFrameType(FrameType.DC.getCode())      // 数据包类型
                .withFrameId(CommandId.SET_HR_OR_ECG)       // 数据包ID
                .withData(bytes)                            // 数据段
                .build();

        CompletableFuture<Frame> future = frameSender.sendFrameWithRetries(frame, 3);
        Frame responseFrame = future.get();
        return checkResponse(responseFrame, frame);
    }

    /**设置心电滤波
     * @param cardiogram
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public boolean setEcgElectrocardiogram(int cardiogram) throws ExecutionException, InterruptedException {
        List<Integer> sinks = CollUtil.newArrayList(0, 1, 2, 16);
        if (!sinks.contains(cardiogram)) {
            throw new RuntimeException("心电滤波不在指定范围");
        }
        byte[] bytes = new byte[1];
        bytes[0] = (byte) cardiogram;

        Frame frame = FrameBuilder.dcFrameBuilder(frameContext)
                .withParamType(ParamType.ECG.getCode())     // 参数类型
                .withFrameType(FrameType.DC.getCode())      // 数据包类型
                .withFrameId(CommandId.SET_ECG_FILTERING_METHOD) // 数据包ID
                .withData(bytes)                            // 数据段
                .build();

        CompletableFuture<Frame> future = frameSender.sendFrameWithRetries(frame, 3);
        Frame responseFrame = future.get();
        return checkResponse(responseFrame, frame);
    }

    /**设置心电增益
     * @param buff
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public boolean setEcgBuff(int buff) throws ExecutionException, InterruptedException {
        List<Integer> sinks = CollUtil.newArrayList(0, 1, 2, 3);
        if (!sinks.contains(buff)) {
            throw new RuntimeException("心电增益不在指定范围");
        }
        String hexString = Integer.toHexString(buff).toUpperCase();
        hexString+="1111";
        int intValue = Integer.parseInt(hexString, 16);
        byte[] bytes = new byte[1];
        bytes[0] = (byte) intValue;
        Frame frame = FrameBuilder.dcFrameBuilder(frameContext)
                .withParamType(ParamType.ECG.getCode())     // 参数类型
                .withFrameType(FrameType.DC.getCode())      // 数据包类型
                .withFrameId(CommandId.SET_ECG_GAIN)        // 数据包ID
                .withData(bytes)                            // 数据段
                .build();

        CompletableFuture<Frame> future = frameSender.sendFrameWithRetries(frame, 3);
        Frame responseFrame = future.get();
        return checkResponse(responseFrame, frame);
    }

    /**设置PACE 检测值
     * @param pace
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public boolean setEcgPace(int pace) throws ExecutionException, InterruptedException {
        List<Integer> sinks = CollUtil.newArrayList(0, 1);
        if (!sinks.contains(pace)) {
            throw new RuntimeException("PACE 检测值不在指定范围");
        }
        byte[] bytes = new byte[1];
        bytes[0] = (byte) pace;

        Frame frame = FrameBuilder.dcFrameBuilder(frameContext)
                .withParamType(ParamType.ECG.getCode())     // 参数类型
                .withFrameType(FrameType.DC.getCode())      // 数据包类型
                .withFrameId(CommandId.SET_PACE)            // 数据包ID
                .withData(bytes)                            // 数据段
                .build();

        CompletableFuture<Frame> future = frameSender.sendFrameWithRetries(frame, 3);
        Frame responseFrame = future.get();
        return checkResponse(responseFrame, frame);
    }

    /**设置R，T 波位置
     * @param wave
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public boolean setEcgRtWave(int wave) throws ExecutionException, InterruptedException {
        List<Integer> sinks = CollUtil.newArrayList(0, 1);
        if (!sinks.contains(wave)) {
            throw new RuntimeException("R，T 波位置值不在指定范围");
        }
        byte[] bytes = new byte[1];
        bytes[0] = (byte) wave;

        Frame frame = FrameBuilder.dcFrameBuilder(frameContext)
                .withParamType(ParamType.ECG.getCode())     // 参数类型
                .withFrameType(FrameType.DC.getCode())      // 数据包类型
                .withFrameId(CommandId.SET_RT_WAVE)         // 数据包ID
                .withData(bytes)                            // 数据段
                .build();

        CompletableFuture<Frame> future = frameSender.sendFrameWithRetries(frame, 3);
        Frame responseFrame = future.get();
        return checkResponse(responseFrame, frame);
    }
    //病人模式
    public boolean setEcgPatientModel(int model) throws ExecutionException, InterruptedException {
        List<Integer> sinks = CollUtil.newArrayList(0, 1, 2);
        if (!sinks.contains(model)) {
            throw new RuntimeException("病人模式值不在指定范围");
        }
        byte[] bytes = new byte[1];
        bytes[0] = (byte) model;

        Frame frame = FrameBuilder.dcFrameBuilder(frameContext)
                .withParamType(ParamType.ECG.getCode())     // 参数类型
                .withFrameType(FrameType.DC.getCode())      // 数据包类型
                .withFrameId(CommandId.SET_PATIENT_INFO)    // 数据包ID
                .withData(bytes)                            // 数据段
                .build();

        CompletableFuture<Frame> future = frameSender.sendFrameWithRetries(frame, 3);
        Frame responseFrame = future.get();
        return checkResponse(responseFrame, frame);
    }
    //导联模式
    public boolean setEcgLeadModel(int model) throws ExecutionException, InterruptedException {
        List<Integer> sinks = CollUtil.newArrayList(0, 1, 2);
        if (!sinks.contains(model)) {
            throw new RuntimeException("导联模式值不在指定范围");
        }
        byte[] bytes = new byte[1];
        bytes[0] = (byte) model;

        Frame frame = FrameBuilder.dcFrameBuilder(frameContext)
                .withParamType(ParamType.ECG.getCode())     // 参数类型
                .withFrameType(FrameType.DC.getCode())      // 数据包类型
                .withFrameId(CommandId.SET_ECG_LEAD_MODE)   // 数据包ID
                .withData(bytes)                            // 数据段
                .build();

        CompletableFuture<Frame> future = frameSender.sendFrameWithRetries(frame, 3);
        Frame responseFrame = future.get();
        return checkResponse(responseFrame, frame);
    }
    //导联通道
    public boolean setEcgLeadChannel(int channel) throws ExecutionException, InterruptedException {
        List<Integer> sinks = CollUtil.newArrayList(0, 1, 2, 3, 4, 5, 6);
        if (!sinks.contains(channel)) {
            throw new RuntimeException("导联通道不在指定范围");
        }
        String hexString = Integer.toHexString(channel).toUpperCase();
        hexString+="0000";
        int intValue = Integer.parseInt(hexString, 16);
        byte[] bytes = new byte[1];
        bytes[0] = (byte) intValue;
        Frame frame = FrameBuilder.dcFrameBuilder(frameContext)
                .withParamType(ParamType.ECG.getCode())     // 参数类型
                .withFrameType(FrameType.DC.getCode())      // 数据包类型
                .withFrameId(CommandId.SET_ECG_CHANNEL_LEADS)// 数据包ID
                .withData(bytes)                            // 数据段
                .build();

        CompletableFuture<Frame> future = frameSender.sendFrameWithRetries(frame, 3);
        Frame responseFrame = future.get();
        return checkResponse(responseFrame, frame);
    }

    // 在线升级
    public boolean onlineUpgrade() throws ExecutionException, InterruptedException {
        Frame frame = FrameBuilder.dcFrameBuilder(frameContext)
                .withParamType(ParamType.SPO2.getCode())     // 参数类型
                .withFrameType(FrameType.DC.getCode())      // 数据包类型
                .withFrameId(CommandId.ONLINE_UPGRADE)// 数据包ID
                .build();

        CompletableFuture<Frame> future = frameSender.sendFrameWithRetries(frame, 3);
        Frame responseFrame = future.get();
        return checkResponse(responseFrame, frame);
    }

    // 启动/停止心电校准
    public boolean ecgCalibration(int calibration) throws ExecutionException, InterruptedException {
        List<Integer> sinks = CollUtil.newArrayList(0, 1);
        if (!sinks.contains(calibration)) {
            throw new RuntimeException("PACE 检测值不在指定范围");
        }
        byte[] bytes = new byte[1];
        bytes[0] = (byte) calibration;

        Frame frame = FrameBuilder.dcFrameBuilder(frameContext)
                .withParamType(ParamType.ECG.getCode())     // 参数类型
                .withFrameType(FrameType.DC.getCode())      // 数据包类型
                .withFrameId(CommandId.SET_ECG_CALIBRATION)            // 数据包ID
                .withData(bytes)                            // 数据段
                .build();

        CompletableFuture<Frame> future = frameSender.sendFrameWithRetries(frame, 3);
        Frame responseFrame = future.get();
        return checkResponse(responseFrame, frame);
    }


    @Async
    public void executeCommand(Integer ecgStTemplateIso, Integer ecgStTemplateSt, Integer ecgNotchMode,
                               Integer ecgArrhythmiaChannel, Integer ecgPowerFiltering, Integer ecgGain,
                               Integer ecgPatientModel, Integer ecgLeadModel, Integer ecgLeadChannel) {
        try {
            printLog("心电ST模板", setEcgStTemplate(ecgStTemplateIso, ecgStTemplateSt));
            printLog("心电陷波", setEcgSinkWave(convert(ecgNotchMode)));
            printLog("心率分析通道", setEcgArrhythmia(convert(ecgArrhythmiaChannel)));
            printLog("心电滤波", setEcgElectrocardiogram(convert(ecgPowerFiltering)));
            printLog("心电增益", setEcgBuff(ecgGain));
            printLog("病人模式", setEcgPatientModel(ecgPatientModel));
            printLog("导联模式", setEcgLeadModel(ecgLeadModel));
            printLog("导联通道", setEcgLeadChannel(ecgLeadChannel));

        } catch (ExecutionException | InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    private int convert(int origin) {
        return switch (origin) {
            case 0 -> 0x00;
            case 1 -> 0x01;
            case 2 -> 0x02;
            case 3 -> 0x10;
            default -> throw new IllegalStateException("Unexpected value: " + origin);
        };
    }

    private void printLog(String operation, boolean result) {
        if (result) {
            log.info("{}设置{}", operation, "成功");
        } else {
            log.error("{}设置{}", operation, "失败");
        }
    }
}
