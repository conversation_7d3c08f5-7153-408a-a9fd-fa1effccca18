package com.cqqy.hrvas.comm.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.cqqy.hrvas.cache.DetectorConfigCache;
import com.cqqy.hrvas.comm.service.sse.SseEmitterService;
import com.cqqy.hrvas.util.CacheUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.cqqy.hrvas.constant.CacheConstant.*;

@Data
@RequiredArgsConstructor
@Slf4j
@Service
public class EarlyWarningService {

    private final SseEmitterService sseEmitterService;

    private final AtomicBoolean isWriting = new AtomicBoolean(false);

    private ScheduledFuture<?> scheduledStop;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    private final CacheUtil cacheUtil;

    private final CacheManager cacheManager;

    public synchronized void start(int durationMinutes) {
        isWriting.set(true);

        // 定时停止
        scheduledStop = scheduler.schedule(this::stop,
                durationMinutes, TimeUnit.MINUTES);
    }

    public synchronized void stop() {
        isWriting.set(false);
    }

    public void heartRateReceive(int heartRate) {
        if (!isWriting.get()) {
            return;
        }
        DetectorConfigCache detectorConfigCache = cacheUtil.getFromCache(CACHE_NAME, CONFIG_KEY);
        Integer patientType = cacheUtil.getFromCache(CACHE_NAME, PATIENT_TYPE_KEY);
        if (ObjectUtil.isEmpty(detectorConfigCache) || ObjectUtil.isEmpty(patientType)) {
            log.error("缓存获取失败：detectorConfig={}，patientType={}", detectorConfigCache, patientType);
            return;
        }

        String warning = "";
        Integer heartRateUpper;
        Integer heartRateLower;
        // 成人
        if (patientType == 0) {
            heartRateUpper = detectorConfigCache.getAlarmEcgHeartRateAdultUpper();
            heartRateLower = detectorConfigCache.getAlarmEcgHeartRateAdultLower();
        } else {
            // 儿童
            heartRateUpper = detectorConfigCache.getAlarmEcgHeartRateChildUpper();
            heartRateLower = detectorConfigCache.getAlarmEcgHeartRateChildLower();
        }
        if (heartRate >= heartRateUpper) {
            warning = StrUtil.format("心电过速警告，当前心率：{}", heartRate);
        }
        if (heartRate <= heartRateLower) {
            warning = StrUtil.format("心电过缓警告，当前心率：{}", heartRate);
        }
        sseEmitterService.broadcast(SseEmitterService.ECG_EARLY_WARNING_SUBSCRIPTION, warning);
    }

    public void arrhythmiaReceive(int lastTime, int thisTime) {
        if (!isWriting.get()) {
            return;
        }
        DetectorConfigCache detectorConfigCache = cacheUtil.getFromCache(CACHE_NAME, CONFIG_KEY);
        Integer patientType = cacheUtil.getFromCache(CACHE_NAME, PATIENT_TYPE_KEY);
        if (ObjectUtil.isEmpty(detectorConfigCache) || ObjectUtil.isEmpty(patientType)) {
            log.error("缓存获取失败：detectorConfig={}，patientType={}", detectorConfigCache, patientType);
            return;
        }

        String warning;
        // 成人
        Integer fibroticTremor;
        if (patientType == 0) {
            fibroticTremor = detectorConfigCache.getAlarmFibroticTremorAdult();
        } else {
            // 儿童
            fibroticTremor = detectorConfigCache.getAlarmFibroticTremorChild();
        }
        if (fibroticTremor == 0) {
            warning = StrUtil.format("纤维性颤动警告，上次心率失常位置：{}，当次心率失常位置:{}", lastTime, thisTime);
            sseEmitterService.broadcast(SseEmitterService.ECG_EARLY_WARNING_SUBSCRIPTION, warning);
        }
    }
}
