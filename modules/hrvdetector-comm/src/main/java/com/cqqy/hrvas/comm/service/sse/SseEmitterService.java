package com.cqqy.hrvas.comm.service.sse;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SSE服务，用于管理SSE连接和数据推送
 */
@Slf4j
@Service
public class SseEmitterService {
    public static final String WAVEFORM_SUBSCRIPTION = "waveform";
    public static final String ECG_LEAD_STATUS_SUBSCRIPTION = "ecg_lead_status";
    public static final String ECG_EARLY_WARNING_SUBSCRIPTION = "ecg_early_warning";
    public static final String POWER_SUPPLY_EARLY_WARNING_SUBSCRIPTION = "power_supply_early_warning";
    public static final String HEART_RATE_SUBSCRIPTION = "heart-rate";

    // 默认超时时间（毫秒）
    private static final long DEFAULT_TIMEOUT = 60000L;
    
    // 重试次数
    private static final int MAX_RETRY_ATTEMPTS = 3;
    
    // 重试间隔（毫秒）
    private static final long RETRY_INTERVAL = 1000L;
    
    private final Map<String, Map<String, SseEmitter>> emitters = new ConcurrentHashMap<>();
    
    /**
     * 创建新的SSE连接
     * @param clientId 客户端ID
     * @param subscription 订阅类型
     * @return SseEmitter实例
     */
    public SseEmitter createEmitter(String clientId, String subscription) {
        SseEmitter emitter = new SseEmitter(DEFAULT_TIMEOUT);
        
        emitter.onCompletion(() -> {
            log.info("Client {} connection completed for subscription {}", clientId, subscription);
            removeEmitter(clientId, subscription);
        });
        
        emitter.onTimeout(() -> {
            log.info("Client {} connection timeout for subscription {}", clientId, subscription);
            emitter.complete();
            removeEmitter(clientId, subscription);
        });
        
        emitters.computeIfAbsent(clientId, k -> new ConcurrentHashMap<>())
               .put(subscription, emitter);
        return emitter;
    }
    
    private void removeEmitter(String clientId, String subscription) {
        Map<String, SseEmitter> clientEmitters = emitters.get(clientId);
        if (clientEmitters != null) {
            clientEmitters.remove(subscription);
            if (clientEmitters.isEmpty()) {
                emitters.remove(clientId);
            }
        }
    }
    
    /**
     * 向指定客户端推送数据
     * @param clientId 客户端ID
     * @param data 要推送的数据
     */
    public void sendToClient(String clientId, String subscription, Object data) {
        Map<String, SseEmitter> clientEmitters = emitters.get(clientId);
        if (clientEmitters != null) {
            SseEmitter emitter = clientEmitters.get(subscription);
            if (emitter != null) {
                sendWithRetry(clientId, subscription, emitter, data, MAX_RETRY_ATTEMPTS);
            }
        }
    }
    
    private void sendWithRetry(String clientId, String subscription, SseEmitter emitter, Object data, int attemptsLeft) {
        try {
            emitter.send(SseEmitter.event().data(data));
        } catch (IOException e) {
            if (attemptsLeft > 1) {
                log.warn("Failed to send data to client {}, retrying... ({} attempts left)", clientId, attemptsLeft - 1);
                try {
                    Thread.sleep(RETRY_INTERVAL);
                    sendWithRetry(clientId, subscription, emitter, data, attemptsLeft - 1);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    handleSendError(clientId, emitter, e);
                }
            } else {
                handleSendError(clientId, emitter, e);
            }
        }
    }
    
    private void handleSendError(String clientId, SseEmitter emitter, Exception e) {
        log.error("Failed to send data to client {} after all retry attempts", clientId, e);
        emitter.complete();
        emitters.remove(clientId);
    }
    
    /**
     * 向所有客户端广播数据
     * @param data 要广播的数据
     */
    public void broadcast(String subscription, Object data) {
        emitters.forEach((clientId, clientEmitters) -> {
            SseEmitter emitter = clientEmitters.get(subscription);
            if (emitter != null) {
                sendWithRetry(clientId, subscription, emitter, data, MAX_RETRY_ATTEMPTS);
            }
        });
    }
    
    /**
     * 获取当前活跃的SSE连接数
     * @return 活跃连接数
     */
    public int getActiveEmitterCount() {
        return emitters.size();
    }
}