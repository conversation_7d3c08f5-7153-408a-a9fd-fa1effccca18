package com.cqqy.hrvas.comm.service.waveform;

import com.cqqy.hrvas.comm.m101.model.WaveformData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Service
public class WaveformService {

    private BufferedWriter fileWriter;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    private ScheduledFuture<?> scheduledStop;

    private final AtomicBoolean isWriting = new AtomicBoolean(false);

    @Value("${hrvdetector.storage.filepath}")
    private String path;

    @Value("${hrvdetector.storage.filename.prefix}")
    private String filenamePrefix;

    @Value("${hrvdetector.storage.filename.suffix}")
    private String filenameSuffix;

    private Integer num;

    public void receiveData(WaveformData data) {
        if (isWriting.get()) {
            try {
                fileWriter.write(num + "," +
                        data.getRWavePosition() + "," +
                        data.getTWavePosition() + "," +
                        data.isPaceDetected() + "," +
                        data.isRWaveDetected() + "," +
                        data.isTWaveDetected() + "," +
                        data.getEcgChannelI() + "," +
                        data.getEcgChannelII() + "," +
                        data.getEcgChannelVI() + "," +
                        data.getRespWave() + "\n");
                fileWriter.flush();
                num++;
            } catch (IOException e) {
                log.error("写入失败: {}", e.getMessage());
                stopWriting(); // 出错时自动停止
            }
        }
    }

    /**
     * 启动写入（API触发）
     */
    public synchronized void startWriting(Long taskId, int durationMinutes) {
        if (isWriting.get()) {
            throw new IllegalStateException("已有写入任务进行中");
        }

        try {
            // 创建新文件
//        String filename = "waveform_data_" + Instant.now().getEpochSecond() + ".txt";
            String filename = filenamePrefix + "_" + taskId + "." + filenameSuffix;
            Path currentFilePath = Path.of(path, filename);
            Files.deleteIfExists(currentFilePath);
            Files.createDirectories(currentFilePath.getParent());
            fileWriter = Files.newBufferedWriter(currentFilePath,
                    StandardOpenOption.CREATE,
                    StandardOpenOption.APPEND
            );
            log.info("波形数据写入开始");

            isWriting.set(true);
            fileWriter.write(",r_pos,t_pos,pace_flag,r_flag,t_flag,channel_1,channel_2,channel_v1,resp" + "\n");
            fileWriter.flush();
            num = 0;

            // 定时停止
            scheduledStop = scheduler.schedule(this::stopWriting,
                    durationMinutes, TimeUnit.MINUTES);
        } catch (IOException e) {
            log.error("写入波形数据失败: {}", e.getMessage());
        }
    }

    /**
     * 停止写入
     */
    public synchronized void stopWriting() {
        if (isWriting.compareAndSet(true, false)) {
            try {
                if (fileWriter != null) {
                    fileWriter.close();
                    fileWriter = null;
                }

                scheduledStop.cancel(false);
                log.info("波形数据写入结束");
            } catch (IOException e) {
                log.error("关闭文件失败: {}", e.getMessage());
            }
        }
        num = 0;
    }

}
