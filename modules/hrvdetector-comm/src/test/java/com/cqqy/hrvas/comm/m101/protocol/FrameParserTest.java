package com.cqqy.hrvas.comm.m101.protocol;

import org.junit.jupiter.api.Test;

import com.cqqy.hrvas.comm.m101.frame.Frame;
import com.cqqy.hrvas.comm.m101.frame.FrameParser;

import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

class FrameParserTest {

    @Test
    void testSerializeAndParse() {
        // 创建一个测试帧
        Frame frame = new Frame();
        frame.setLength((byte) 15); // 总长度：帧头(1) + 长度(1) + 参数类型(1) + 帧类型(1) + 帧ID(1) + 序列号(4) + 数据(5) + 校验和(1)
        frame.setParamType((byte) 0x01); // ECG
        frame.setFrameType((byte) 0x04); // 通用数据包
        frame.setCommandId((byte) 0x01);
        frame.setSequenceNumber(0xffffffff);
        frame.setData(new byte[]{0x01, 0x02, 0x03, 0x04, 0x05});
        frame.setChecksum(frame.calculateChecksum());

        // 序列化
        byte[] serialized = FrameParser.serialize(frame);

        // 解析
        List<Frame> parsedFrames = FrameParser.parse(serialized);

        // 验证
       assertFalse(parsedFrames.isEmpty(), "解析后的帧列表不应为空");
        Frame parsedFrame = parsedFrames.get(0);
        
        // 验证各个字段
        assertEquals(frame.getLength(), parsedFrame.getLength(), "长度字段不匹配");
        assertEquals(frame.getParamType(), parsedFrame.getParamType(), "参数类型不匹配");
        assertEquals(frame.getFrameType(), parsedFrame.getFrameType(), "帧类型不匹配");
        assertEquals(frame.getCommandId(), parsedFrame.getCommandId(), "帧ID不匹配");
        assertEquals(frame.getSequenceNumber(), parsedFrame.getSequenceNumber(), "序列号不匹配");
        assertArrayEquals(frame.getData(), parsedFrame.getData(), "数据段不匹配");
        assertEquals(frame.getChecksum(), parsedFrame.getChecksum(), "校验和不匹配");
        assertTrue(parsedFrame.validateChecksum(), "校验和验证失败");
    }

    @Test
    void testParseInvalidData() {
        // 测试空数据
        List<Frame> frames1 = FrameParser.parse(null);
        assertTrue(frames1.isEmpty(), "空数据应返回空列表");

        // 测试过短的数据
        List<Frame> frames2 = FrameParser.parse(new byte[]{0x01, 0x02, 0x03});
        assertTrue(frames2.isEmpty(), "过短的数据应返回空列表");

        // 测试无效的帧头
        byte[] invalidStartMark = new byte[]{0x00, 0x0A, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};
        List<Frame> frames3 = FrameParser.parse(invalidStartMark);
        assertTrue(frames3.isEmpty(), "无效帧头的数据应返回空列表");

        // 测试无效的校验和
        Frame frame = new Frame();
        frame.setLength((byte) 10);
        frame.setParamType((byte) 0x01);
        frame.setFrameType((byte) 0x01);
        frame.setCommandId((byte) 0x01);
        frame.setSequenceNumber(1);
        frame.setChecksum((byte) 0xFF); // 设置错误的校验和
        
        byte[] invalidChecksum = FrameParser.serialize(frame);
        List<Frame> frames4 = FrameParser.parse(invalidChecksum);
        assertTrue(frames4.isEmpty(), "校验和错误的帧应被过滤");

        // 测试多个帧
        byte[] multipleFrames = new byte[]{
            (byte) 0xFA, 0x0A, 0x01, 0x01, 0x01, 0x00, 0x00, 0x00, 0x01, 0x0E,
            (byte) 0xFA, 0x0A, 0x01, 0x01, 0x02, 0x00, 0x00, 0x00, 0x02, 0x10,
            (byte) 0xFA, 0x0A, 0x01, 0x01, 0x03, 0x00, 0x00
        };
        List<Frame> frames5 = FrameParser.parse(multipleFrames);
        assertEquals(2, frames5.size(), "应解析出两个完整的帧");
    }
}