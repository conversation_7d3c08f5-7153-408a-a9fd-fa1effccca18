package com.cqqy.hrvas.cache;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class DetectorConfigCache {

    private Long id;

    private String netServerUrl;

    private String netPlatformUrl;

    private String netReportUrl;

    private Integer netOnlineUpgrade;

    private Integer ecgStTemplateIso;

    private Integer ecgStTemplateSt;

    private Integer ecgNotchMode;

    private Integer ecgArrhythmiaChannel;

    private Integer ecgPowerFiltering;

    private Integer ecgGain;

    private Integer alarmEcgHeartRateAdultUpper;

    private Integer alarmEcgHeartRateAdultLower;

    private Integer alarmEcgHeartRateChildUpper;

    private Integer alarmEcgHeartRateChildLower;

    private Integer alarmEcgHeartRateOldUpper;

    private Integer alarmEcgHeartRateOldLower;

    private Integer alarmFibroticTremorAdult;

    private Integer alarmFibroticTremorChild;

    private Double alarmBatteryLow;

    private Integer testPassedIntervalUpper;

    private Integer testPassedIntervalLower;

    private Integer ecgPatientModel;

    private Integer ecgLeadModel;

    private Integer ecgLeadChannel;
}
