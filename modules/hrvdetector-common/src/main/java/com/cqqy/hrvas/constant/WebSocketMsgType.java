package com.cqqy.hrvas.constant;

public interface WebSocketMsgType {

    // 测评端发送：开始推送
    String EV_PUSH_START = "push_start";
    // 测评端发送：开始任务
    String EV_TASK_START = "task_start";
    // 测评端发送：结束任务
    String EV_TASK_STOP = "task_stop";
    // 采集端发送：心电波形
    String DE_WAVEFORM = "waveform";
    // 采集端发送：呼吸心率
    String DE_HEART_RATE = "heart_rate";
    // 采集端发送：确认
    String DE_TASK_START_CONFIRM = "task_start_confirm";
}
