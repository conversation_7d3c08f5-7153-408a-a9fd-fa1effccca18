package com.cqqy.hrvas.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@AllArgsConstructor
@Getter
public enum HrvMode {

    HRV_MODE(0, "HRV"),

    SCWT_MODE(1, "SCWT"),

    MAT_MODE(2, "MAT"),

    ;

    private final Integer code;

    private final String desc;

    public static String getDesc(Integer code) {
        HrvMode[] values = HrvMode.values();
        for (HrvMode value : values) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
