package com.cqqy.hrvas.exception;

import cn.hutool.core.util.StrUtil;
import com.cqqy.hrvas.response.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Object> handleException(Exception e) {
        log.error("系统异常: ", e);
        return ApiResponse.fail(500, StrUtil.format("系统异常: {}", e.getMessage()));
    }

    @ExceptionHandler(BizException.class)
    public ApiResponse<Object> handleBusinessException(BizException e) {
        log.error("业务异常: {}", e.getMessage());
        return ApiResponse.fail(500, StrUtil.format("业务异常: {}", e.getMessage()));
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Object> handleValidationException(MethodArgumentNotValidException e) {
        List<String> errors = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .toList();

        return ApiResponse.fail(500, StrUtil.format("参数校验异常：{}", errors));
    }

    @ExceptionHandler(LoginException.class)
    public ApiResponse<Object> handleLoginException(LoginException e) {
        log.error("登录异常: {}", e.getMessage());
        return ApiResponse.fail(401, StrUtil.format("登录异常: {}", e.getMessage()));
    }
}
