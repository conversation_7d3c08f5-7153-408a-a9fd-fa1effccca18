package com.cqqy.hrvas.questionbank;

import com.cqqy.hrvas.questionbank.mat.TopicGenerator;
import com.cqqy.hrvas.questionbank.mat.TopicStrategy;
import com.cqqy.hrvas.questionbank.scwt.Consistency;
import com.cqqy.hrvas.questionbank.scwt.NonConsistency;
import com.cqqy.hrvas.questionbank.scwt.TopicScwtGenerator;

import java.util.List;

public class TopicUtil {

    //ES
    public static List<String> getESAdd(int minFactorX, int maxFactorX,
                                        int minFactorY, int maxFactorY,
                                        int minAnswer, int maxAnswer,
                                        int topicNum){
        return TopicGenerator.generate(
                TopicStrategy.ADD,
                minFactorX, maxFactorX, minFactorY, maxFactorY, minAnswer, maxAnswer, topicNum
        );
    }
    public static List<String> getESSub(int minFactorX, int maxFactorX,
                                        int minFactorY, int maxFactorY,
                                        int minAnswer, int maxAnswer,
                                        int topicNum){
        return TopicGenerator.generate(
                TopicStrategy.SUB,
                minFactorX, maxFactorX, minFactorY, maxFactorY, minAnswer, maxAnswer, topicNum
        );
    }
    public static List<String> getESMul(int minFactorX, int maxFactorX,
                                        int minFactorY, int maxFactorY,
                                        int minAnswer, int maxAnswer,
                                        int topicNum){
        return TopicGenerator.generate(
                TopicStrategy.MUL,
                minFactorX, maxFactorX, minFactorY, maxFactorY, minAnswer, maxAnswer, topicNum
        );
    }
    public static List<String> getESDiv(int minFactorX, int maxFactorX,
                                        int minFactorY, int maxFactorY,
                                        int minAnswer, int maxAnswer,
                                        int topicNum){
        return TopicGenerator.generate(
                TopicStrategy.DIV,
                minFactorX, maxFactorX, minFactorY, maxFactorY, minAnswer, maxAnswer, topicNum
        );
    }

    //DS
    public static List<String> getDSAdd(int minFactorX, int maxFactorX,
                                        int minFactorY, int maxFactorY,
                                        int minAnswer, int maxAnswer,
                                        int topicNum){
        return TopicGenerator.generate(
                TopicStrategy.ADD,
                minFactorX, maxFactorX, minFactorY, maxFactorY, minAnswer, maxAnswer, topicNum
        );
    }
    public static List<String> getDSSub(int minFactorX, int maxFactorX,
                                        int minFactorY, int maxFactorY,
                                        int minAnswer, int maxAnswer,
                                        int topicNum){
        return TopicGenerator.generate(
                TopicStrategy.SUB,
                minFactorX, maxFactorX, minFactorY, maxFactorY, minAnswer, maxAnswer, topicNum
        );
    }
    public static List<String> getDSMul(int minFactorX, int maxFactorX,
                                        int minFactorY, int maxFactorY,
                                        int minAnswer, int maxAnswer,
                                        int topicNum){
        return TopicGenerator.generate(
                TopicStrategy.MUL,
                minFactorX, maxFactorX, minFactorY, maxFactorY, minAnswer, maxAnswer, topicNum
        );
    }
    public static List<String> getDSDiv(int minFactorX, int maxFactorX,
                                        int minFactorY, int maxFactorY,
                                        int minAnswer, int maxAnswer,
                                        int topicNum){
        return TopicGenerator.generate(
                TopicStrategy.DIV,
                minFactorX, maxFactorX, minFactorY, maxFactorY, minAnswer, maxAnswer, topicNum
        );
    }

    // Consistency
    public static List<Consistency> getConsistency(int topicNum) {
        return TopicScwtGenerator.getInstance().getConsistency(topicNum);
    }

    // NotConsistency
    public static List<NonConsistency> getNonConsistency(int topicNum) {
        return TopicScwtGenerator.getInstance().getNonConsistency(topicNum);
    }
}
