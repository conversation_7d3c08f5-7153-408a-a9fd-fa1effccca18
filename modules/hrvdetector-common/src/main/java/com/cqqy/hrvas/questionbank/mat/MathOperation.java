package com.cqqy.hrvas.questionbank.mat;

/**
 * 策略接口（定义运算行为）
 */
public interface MathOperation {

    /**
     * 计算答案
     */
    int calculate(int a, int b);

    /**
     * 题目格式
     */
    String format(int a, int b, int result);

    /**
     * 题目唯一key
     */
    String generateKey(int a, int b);

    /**
     * 验证参数合法性
     */
    boolean validate(int a, int b);

    /**
     * 运算类型
     */
    String getName();
}
