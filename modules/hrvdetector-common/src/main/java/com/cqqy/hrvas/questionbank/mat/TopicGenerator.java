package com.cqqy.hrvas.questionbank.mat;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;

/**
 * 上下文控制器（工厂模式 + 享元模式）
 */
public class TopicGenerator {
    private static final Map<TopicStrategy, TopicTemplate> CACHE =
            new EnumMap<>(TopicStrategy.class);

    public static List<String> generate(TopicStrategy op,
                                        int minFactorX, int maxFactorX,
                                        int minFactorY, int maxFactorY,
                                        int minAnswer, int maxAnswer,
                                        int topicNum) {
        TopicTemplate generator = CACHE.computeIfAbsent(op,
                k -> new TopicTemplate(op) {});
        return generator.generate(minFactorX, maxFactorX, minFactorY, maxFactorY, minAnswer, maxAnswer, topicNum);
    }
}
