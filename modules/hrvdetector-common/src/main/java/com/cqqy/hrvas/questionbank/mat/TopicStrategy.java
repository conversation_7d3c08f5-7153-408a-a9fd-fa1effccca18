package com.cqqy.hrvas.questionbank.mat;

/**
 * 具体策略实现（单例模式）
 */
public enum TopicStrategy implements MathOperation {

    ADD {
        public int calculate(int a, int b) { return a + b; }
        public String format(int a, int b, int r) { return a + " + " + b + " = " + r; }
        public String generateKey(int a, int b) { return a <= b ? a+"+"+b : b+"+"+a; }
        public boolean validate(int a, int b) { return true; }
        public String getName() {
            return "加法";
        }
    },
    SUB {
        public int calculate(int a, int b) { return a - b; }
        public String format(int a, int b, int r) { return a + " - " + b + " = " + r; }
        public String generateKey(int a, int b) { return a + "-" + b; }
        public boolean validate(int a, int b) { return a >= b; } // 保证非负数结果
        public String getName() {
            return "减法";
        }
    },
    MUL {
        public int calculate(int a, int b) { return a * b; }
        public String format(int a, int b, int r) { return a + " × " + b + " = " + r; }
        public String generateKey(int a, int b) { return a <= b ? a+"×"+b : b+"×"+a; }
        public boolean validate(int a, int b) { return true; }
        public String getName() {
            return "乘法";
        }
    },
    DIV {
        public int calculate(int a, int b) { return a / b; }
        public String format(int a, int b, int r) { return a + " ÷ " + b + " = " + r; }
        public String generateKey(int a, int b) { return a + "÷" + b; }
        public boolean validate(int a, int b) {
            return b != 0 && a % b == 0;
        }
        public String getName() {
            return "除法";
        }
    };
}
