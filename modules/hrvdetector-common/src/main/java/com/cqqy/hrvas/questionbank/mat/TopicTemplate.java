package com.cqqy.hrvas.questionbank.mat;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 模板基类（封装公共算法）
 */
abstract class TopicTemplate {

    private final Random rand = new Random();

    private int divisorIndex = 0; // 除数索引计数器
    protected final MathOperation operation;

    protected TopicTemplate(MathOperation operation) {
        this.operation = operation;
    }

    // 模板方法（final防止子类修改流程）
    public final List<String> generate(int minFactorX, int maxFactorX,
                                       int minFactorY, int maxFactorY,
                                       int minAnswer, int maxAnswer,
                                       int topicNum) {
        // 参数验证
        validateParameters(minFactorX, maxFactorX, minFactorY, maxFactorY, minAnswer, maxAnswer, topicNum);

        List<String> result = new ArrayList<>();
        Set<String> keys = new HashSet<>();

        int attempts = 0;
        final int MAX_ATTEMPTS = topicNum * 10; // 最大尝试次数

        while (result.size() < topicNum) {
            attempts++;

            // 生成题目参数
            int[] factors;
            if (operation == TopicStrategy.DIV) {
                factors = generateFactorsInverseMul(minFactorX, maxFactorX, minFactorY, maxFactorY, minAnswer, maxAnswer);
            } else {
                factors = generateFactors(minFactorX, maxFactorX, minFactorY, maxFactorY);
            }

            int a = factors[0];
            int b = factors[1];

            // 验证参数合法性
            if (!operation.validate(a, b)) continue;

            // 排除重复题目
            String key = operation.generateKey(a, b);
            if (keys.contains(key)) continue;

            // 比较结果范围
            int answer = operation.calculate(a, b);
            if (answer < minAnswer || answer > maxAnswer) continue;

            keys.add(key);
            result.add(operation.format(a, b, answer));

            // 限制循环次数
            if (attempts > MAX_ATTEMPTS) {
                break;
            }
        }
        System.out.println("生成" +operation.getName()+ "题目共"+result.size()+"道！");
        return result;
    }

    private int[] generateFactors(int minA, int maxA,
                                  int minB, int maxB) {

        return new int[]{
                rand.nextInt(maxA - minA + 1) + minA,
                rand.nextInt(maxB - minB + 1) + minB
        };
    }

    private int[] generateFactorsInverseMul(int minDividend, int maxDividend,
                                            int minDivisor, int maxDivisor,
                                            int minQuotient, int maxQuotient) {
        List<Integer> validDivisors = IntStream.rangeClosed(minDivisor, maxDivisor)
                .filter(d -> {
                    int qMinDividend = (int) Math.ceil((double) minDividend / d);
                    int qMaxDividend = maxDividend / d;
                    int qMin = Math.max(qMinDividend, minQuotient);
                    int qMax = Math.min(qMaxDividend, maxQuotient);
                    return qMin <= qMax;
                })
                .boxed()
                .collect(Collectors.toList());

        if (validDivisors.isEmpty()) {
            throw new IllegalArgumentException("指定范围内无有效除数");
        }

        int divisor = validDivisors.get(divisorIndex % validDivisors.size());
        divisorIndex++;

        int qMinDividend = (int) Math.ceil((double) minDividend / divisor);
        int qMaxDividend = maxDividend / divisor;
        int qMin = Math.max(qMinDividend, minQuotient);
        int qMax = Math.min(qMaxDividend, maxQuotient);

        int quotient = qMin + rand.nextInt(qMax - qMin + 1);
        int dividend = divisor * quotient;

        return new int[]{dividend, divisor};
    }

    private void validateParameters(int minFactorX, int maxFactorX, int minFactorY, int maxFactorY, int minAnswer, int maxAnswer, int topicNum) {

        if (minFactorX < 0 || maxFactorX < 0 || minFactorY < 0 || maxFactorY < 0 || minAnswer < 0 || maxAnswer < 0 || topicNum < 0) {
            throw new IllegalArgumentException("所有参数必须为正数");
        }

        if (minFactorY > maxFactorY) {
            throw new IllegalArgumentException("无效范围");
        }
        if (minFactorX > maxFactorX) {
            throw new IllegalArgumentException("无效范围");
        }
        if (minAnswer > maxAnswer) {
            throw new IllegalArgumentException("无效范围");
        }
        if (topicNum <= 0) {
            throw new IllegalArgumentException("问题数量必须为正数");
        }
    }
}
