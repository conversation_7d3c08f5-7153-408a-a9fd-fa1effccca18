package com.cqqy.hrvas.questionbank.scwt;

import java.util.List;

public class Consistency {

    private Integer topicGraphic; // 随机一个红黄蓝绿四个颜色的圆形 0-红 1-黄 2-蓝 3-绿
    private List<Integer> topicCharacter; // 红黄蓝绿四个颜色与含义相同的字 0-红 1-黄 2-蓝 3-绿
    private Integer correctAnswer; // 正确的字的序号<=>graphic

    public Consistency(Integer topicGraphic, List<Integer> topicCharacter, Integer correctAnswer) {
        this.topicGraphic = topicGraphic;
        this.topicCharacter = topicCharacter;
        this.correctAnswer = correctAnswer;
    }

    public Integer getTopicGraphic() {
        return topicGraphic;
    }

    public void setTopicGraphic(Integer topicGraphic) {
        this.topicGraphic = topicGraphic;
    }

    public List<Integer> getTopicCharacter() {
        return topicCharacter;
    }

    public void setTopicCharacter(List<Integer> topicCharacter) {
        this.topicCharacter = topicCharacter;
    }

    public Integer getCorrectAnswer() {
        return correctAnswer;
    }

    public void setCorrectAnswer(Integer correctAnswer) {
        this.correctAnswer = correctAnswer;
    }
}
