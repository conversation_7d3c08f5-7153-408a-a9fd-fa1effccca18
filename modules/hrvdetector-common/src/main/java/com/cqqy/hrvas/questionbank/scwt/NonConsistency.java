package com.cqqy.hrvas.questionbank.scwt;

import java.util.List;

public class NonConsistency {

    private Integer topicCharacter; // 红黄蓝绿四个字中随机一个 0-红 1-黄 2-蓝 3-绿
    private Integer topicCharacterColor; // 字的颜色（不与字含义相同） 0-红 1-黄 2-蓝 3-绿
    private List<Integer> topicGraphic; // 红黄蓝绿四个颜色的圆形 0-红 1-黄 2-蓝 3-绿
    private Integer correctAnswer; // 正确的圆形的序号 <=>character_color

    public NonConsistency(Integer topicCharacter, Integer topicCharacterColor, List<Integer> topicGraphic, Integer correctAnswer) {
        this.topicCharacter = topicCharacter;
        this.topicCharacterColor = topicCharacterColor;
        this.topicGraphic = topicGraphic;
        this.correctAnswer = correctAnswer;
    }

    public Integer getTopicCharacter() {
        return topicCharacter;
    }

    public void setTopicCharacter(Integer topicCharacter) {
        this.topicCharacter = topicCharacter;
    }

    public Integer getTopicCharacterColor() {
        return topicCharacterColor;
    }

    public void setTopicCharacterColor(Integer topicCharacterColor) {
        this.topicCharacterColor = topicCharacterColor;
    }

    public List<Integer> getTopicGraphic() {
        return topicGraphic;
    }

    public void setTopicGraphic(List<Integer> topicGraphic) {
        this.topicGraphic = topicGraphic;
    }

    public Integer getCorrectAnswer() {
        return correctAnswer;
    }

    public void setCorrectAnswer(Integer correctAnswer) {
        this.correctAnswer = correctAnswer;
    }
}
