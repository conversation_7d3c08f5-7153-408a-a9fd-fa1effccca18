package com.cqqy.hrvas.questionbank.scwt;

import java.util.*;
import java.util.stream.Collectors;

public class TopicScwtGenerator {

    private static TopicScwtGenerator instance;

    public static TopicScwtGenerator getInstance() {
        if (null == instance) {
            instance = new TopicScwtGenerator();
        }
        return instance;
    }

    private static final Integer[] meta = {0, 1, 2, 3};

    private static final int RANDOM_MAX = 4;

    private static final Random rand = new Random();
    
    public List<Consistency> getConsistency(int topicNum) {
        List<Consistency> consistencies = new ArrayList<>();
        Integer distinct = meta[rand.nextInt(RANDOM_MAX)];
        for (int i = 0; i < topicNum; i++) {
            Integer graphic = getRandom(distinct);
            List<Integer> character = Arrays.asList(Arrays.copyOf(meta, meta.length));
            Collections.shuffle(character);
            Consistency consistency = new Consistency(graphic, character, graphic);
            consistencies.add(consistency);
            distinct = graphic;
        }
        return consistencies;
    }

    public List<NonConsistency> getNonConsistency(int topicNum) {
        List<NonConsistency> notConsistencies = new ArrayList<>();
        Integer distinct = 0;
        for (int i = 0; i < topicNum; i++) {
            Integer character = getRandom(distinct);
            Integer character_color = getRandom(character);
            List<Integer> graphic = Arrays.asList(Arrays.copyOf(meta, meta.length));
            Collections.shuffle(graphic);
            NonConsistency notConsistency = new NonConsistency(character, character_color, graphic, character_color);
            notConsistencies.add(notConsistency);
            distinct = character;
        }
        return notConsistencies;
    }

    private Integer getRandom(Integer distinct) {
        Integer[] copy = Arrays.copyOf(meta, meta.length);
        List<Integer> copyList = Arrays.asList(Arrays.copyOf(copy, copy.length));
        int temp = distinct;
        copyList = copyList.stream()
                .filter(index -> !Objects.equals(index, temp))
                .collect(Collectors.toList());
        return copyList.get(rand.nextInt(RANDOM_MAX - 1));
    }
}
