package com.cqqy.hrvas.response;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class ApiResponse<T> {

    private int code;

    private boolean success;

    private String msg;

    private T data;

    // 成功响应（无数据）
    public static <T> ApiResponse<T> ok() {
        return ok(null);
    }

    // 成功响应（带数据）
    public static <T> ApiResponse<T> ok(T data) {
        return result(ApiCode.SUCCESS.getCode(), ApiCode.SUCCESS.getMsg(), data);
    }

    // 失败响应
    public static <T> ApiResponse<T> fail() {
        return fail(ApiCode.FAIL.getMsg());
    }

    public static <T> ApiResponse<T> fail(String message) {
        return fail(ApiCode.FAIL.getCode(), message);
    }

    public static <T> ApiResponse<T> fail(int code, String message) {
        return result(code, message, null);
    }

    public static <T> ApiResponse<T> result(int code, String message, T data) {
        boolean success = false;
        if (code == ApiCode.SUCCESS.getCode()) {
            success = true;
        }
        return new ApiResponse<>(code, success, message, data);
    }
}
