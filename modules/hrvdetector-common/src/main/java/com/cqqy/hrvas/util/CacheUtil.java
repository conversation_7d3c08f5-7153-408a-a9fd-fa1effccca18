package com.cqqy.hrvas.util;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CacheUtil {

    private final CacheManager cacheManager;

    //添加
    public void putToCache(String cacheName, Object key, Object value) {
        Cache cache = cacheManager.getCache(cacheName);
        if (ObjectUtil.isNotEmpty(cache)) {
            cache.put(key, value);
        }
    }

    //获取
    public <T> T getFromCache(String cacheName, Object key) {
        Cache cache = cacheManager.getCache(cacheName);
        if (ObjectUtil.isNotEmpty(cache)) {
            Cache.ValueWrapper wrapper = cache.get(key);
            if (ObjectUtil.isNotEmpty(wrapper)) {
                return (T) wrapper.get();
            }
        }
        return null;
    }

    //移除
    public void evictCache(String cacheName, Object key) {
        Cache cache = cacheManager.getCache(cacheName);
        if (ObjectUtil.isNotEmpty(cache)) {
            cache.evict(key);
        }
    }

    //清空整个缓存
    public void clearCache(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (ObjectUtil.isNotEmpty(cache)) {
            cache.clear();
        }
    }
}