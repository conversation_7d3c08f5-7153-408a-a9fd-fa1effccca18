package com.cqqy.hrvas.controller;

import com.cqqy.hrvas.annotation.Permission;
import com.cqqy.hrvas.dto.config.DetectorAlarmConfigDTO;
import com.cqqy.hrvas.dto.config.DetectorEcgConfigDTO;
import com.cqqy.hrvas.dto.config.DetectorNetworkConfigDTO;
import com.cqqy.hrvas.dto.config.DetectorTestConfigDTO;
import com.cqqy.hrvas.entity.DetectorConfig;
import com.cqqy.hrvas.response.ApiResponse;
import com.cqqy.hrvas.service.DetectorConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "系统配置")
@RestController
@RequestMapping("/api/config")
public class DetectorConfigController {

    private final DetectorConfigService detectorConfigService;

    @Autowired
    public DetectorConfigController(DetectorConfigService detectorConfigService) {
        this.detectorConfigService = detectorConfigService;
    }

    @Permission("detector:config:info")
    @Operation(summary = "配置详情")
    @GetMapping("/config_info")
    public ApiResponse<DetectorConfig> configDetails() {
        return ApiResponse.ok(detectorConfigService.getDetectorConfig());
    }

    @Permission("detector:config:update:network")
    @Operation(summary = "更新网络配置")
    @PostMapping("/update/network_config")
    public ApiResponse<Void> updateNetworkConfig(@RequestBody @Valid DetectorNetworkConfigDTO dto) {
        detectorConfigService.updateNetworkConfig(dto);
        return ApiResponse.ok();
    }

    @Permission("detector:config:update:ecg")
    @Operation(summary = "更新心电配置")
    @PostMapping("/update/ecg_config")
    public ApiResponse<Void> updateEcgConfig(@RequestBody @Valid DetectorEcgConfigDTO dto) {
        detectorConfigService.updateEcgConfig(dto);
        return ApiResponse.ok();
    }

    @Permission("detector:config:update:alarm")
    @Operation(summary = "更新报警配置")
    @PostMapping("/update/alarm_config")
    public ApiResponse<Void> updateAlarmConfig(@RequestBody @Valid DetectorAlarmConfigDTO dto) {
        detectorConfigService.updateAlarmConfig(dto);
        return ApiResponse.ok();
    }

    @Permission("detector:config:update:test")
    @Operation(summary = "更新测试配置")
    @PostMapping("/update/test_config")
    public ApiResponse<Void> updateTestConfig(@RequestBody @Valid DetectorTestConfigDTO dto) {
        detectorConfigService.updateTestConfig(dto);
        return ApiResponse.ok();
    }
}
