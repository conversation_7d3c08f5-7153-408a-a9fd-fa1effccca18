package com.cqqy.hrvas.controller;

import com.cqqy.hrvas.annotation.Permission;
import com.cqqy.hrvas.response.ApiResponse;
import com.cqqy.hrvas.service.DeviceInfoService;
import com.cqqy.hrvas.vo.config.DeviceInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "设备信息")
@RestController
@RequestMapping("/api/device")
public class DeviceInfoController {

    private final DeviceInfoService deviceInfoService;

    public DeviceInfoController(DeviceInfoService deviceInfoService) {
        this.deviceInfoService = deviceInfoService;
    }

    @Permission("detector:device:info")
    @Operation(summary = "设备详情")
    @GetMapping("/device_info")
    public ApiResponse<DeviceInfoVO> deviceInfo() {
        return ApiResponse.ok(deviceInfoService.getDeviceInfoAndStatus());
    }

    @Permission("detector:device:update:device_no")
    @Operation(summary = "修改设备编号")
    @GetMapping("/update_device_no")
    public ApiResponse<Void> updateDeviceNo(@RequestParam(name = "deviceNo") Integer deviceNo) {
        deviceInfoService.updateDeviceNo(deviceNo);
        return ApiResponse.ok();
    }
}
