package com.cqqy.hrvas.controller;

import com.cqqy.hrvas.annotation.Permission;
import com.cqqy.hrvas.response.ApiResponse;
import com.cqqy.hrvas.websocket.service.WebSocketService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "HRV同步")
@RestController
@RequestMapping("/api/hrv_sync")
public class HrvSyncController {

    private final WebSocketService webSocketService;

    public HrvSyncController(WebSocketService webSocketService) {
        this.webSocketService = webSocketService;
    }

    @Permission("detector:hrv_sync:confirm")
    @Operation(summary = "确认开始")
    @GetMapping("/confirm")
    public ApiResponse<Void> confirm() {
        webSocketService.handleHrvSyncTaskStartConfirm();
        return ApiResponse.ok();
    }
}
