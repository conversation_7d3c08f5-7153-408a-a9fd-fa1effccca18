package com.cqqy.hrvas.controller;

import com.cqqy.hrvas.annotation.Permission;
import com.cqqy.hrvas.page.ManagePageQuery;
import com.cqqy.hrvas.response.ApiResponse;
import com.cqqy.hrvas.service.InspectListService;
import com.cqqy.hrvas.vo.PageVo;
import com.cqqy.hrvas.vo.business.InspectListVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "任务管理和数据统计")
@RestController
@RequestMapping("/api/inspect_list")
public class InspectListController {

    private final InspectListService inspectListService;

    @Autowired
    public InspectListController(InspectListService inspectListService) {
        this.inspectListService = inspectListService;
    }

    @Permission("detector:inspect_list:offline:list")
    @PostMapping("/offline/list")
    @Operation(summary = "离线任务列表")
    public ApiResponse<List<InspectListVo>> offlineList(@RequestParam(name = "prefix", required = false) String prefix) {
        List<InspectListVo> inspectListVoList = inspectListService.findOfflineTaskList(prefix);
        return ApiResponse.ok(inspectListVoList);
    }

    @Permission("detector:inspect_list:offline:details")
    @PostMapping("/offline/details")
    @Operation(summary = "离线任务详情")
    public ApiResponse<InspectListVo> offlineDetails(@RequestParam(name = "id") Long id) {
        InspectListVo inspectListVo = inspectListService.findOfflineTaskDetails(id);
        return ApiResponse.ok(inspectListVo);
    }

    @Permission("detector:inspect_list:offline:end")
    @PostMapping("/offline/end")
    @Operation(summary = "离线任务终止")
    public ApiResponse<Void> offlineEnd(@RequestParam(name = "id") Long id) {
        inspectListService.taskEnd(id);
        return ApiResponse.ok();
    }

    @Permission("detector:inspect_list:manage:statistics")
    @PostMapping("/manage_statistics")
    @Operation(summary = "管理端统计")
    public ApiResponse<PageVo<InspectListVo>> manageStatistics(@RequestBody ManagePageQuery managePageQuery) {
        return ApiResponse.ok(inspectListService.manageStatistics(managePageQuery));
    }

    @Permission("detector:inspect_list:statistics")
    @PostMapping("/detector_statistics")
    @Operation(summary = "采集端统计")
    public ApiResponse<PageVo<InspectListVo>> detectorStatistics(@RequestBody ManagePageQuery managePageQuery) {
        return ApiResponse.ok(inspectListService.detectorStatistics(managePageQuery));
    }

    @Permission("detector:inspect_list:not_report:statistics")
    @PostMapping("/not_report_statistics")
    @Operation(summary = "未上报统计")
    public ApiResponse<PageVo<InspectListVo>> notReportStatistics(@RequestBody ManagePageQuery managePageQuery) {
        return ApiResponse.ok(inspectListService.notReportStatistics(managePageQuery));
    }

    @Permission("detector:inspect_list:temp:remove")
    @PostMapping("/temp/remove")
    @Operation(summary = "临时任务终止")
    public ApiResponse<Void> tempRemove(@RequestParam(name = "id") Long id) {
        inspectListService.taskEnd(id);
        return ApiResponse.ok();
    }

}
