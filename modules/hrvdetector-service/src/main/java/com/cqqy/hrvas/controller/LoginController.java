package com.cqqy.hrvas.controller;

import com.cqqy.hrvas.dto.business.login.LoginDTO;
import com.cqqy.hrvas.response.ApiResponse;
import com.cqqy.hrvas.websocket.api.ClientGetServerService;
import com.cqqy.hrvas.vo.login.LoginTokenVo;
import com.cqqy.hrvas.vo.login.LoginVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "登录")
@RestController
@RequestMapping("/api/login")
public class LoginController {

    private final ClientGetServerService clientGetServerService;

    @Autowired
    public LoginController(ClientGetServerService clientGetServerService) {
        this.clientGetServerService = clientGetServerService;
    }

    @PostMapping("/login")
    @Operation(summary = "管理后台登录")
    public ApiResponse<LoginTokenVo> login(@Valid @RequestBody LoginDTO loginDTO, HttpServletRequest request, HttpServletResponse response) {
        LoginTokenVo loginTokenVo = clientGetServerService.login(loginDTO, request, response);
        return ApiResponse.ok(loginTokenVo);
    }

    @PostMapping("/getLoginUserInfo")
    @Operation(summary = "获取管理后台登录用户信息")
    public ApiResponse<LoginVo> getLoginUserInfo(HttpServletRequest request, HttpServletResponse response) {
        LoginVo loginVo = clientGetServerService.getLoginUserInfo(request, response);
        return ApiResponse.ok(loginVo);
    }

    @PostMapping("/logout")
    @Operation(summary = "管理后台退出")
    public ApiResponse<Boolean> logout(HttpServletRequest request, HttpServletResponse response) {
        clientGetServerService.logout(request, response);
        return ApiResponse.ok(true);
    }
}
