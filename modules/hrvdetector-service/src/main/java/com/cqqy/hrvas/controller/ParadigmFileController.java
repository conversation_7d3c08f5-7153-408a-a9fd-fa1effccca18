package com.cqqy.hrvas.controller;

import cn.hutool.core.util.IdUtil;
import com.cqqy.hrvas.annotation.Permission;
import com.cqqy.hrvas.dto.business.ParadigmFileDTO;
import com.cqqy.hrvas.dto.business.topic.ConsistencyDTO;
import com.cqqy.hrvas.dto.business.topic.MatTopicDTO;
import com.cqqy.hrvas.dto.business.topic.NonConsistencyDTO;
import com.cqqy.hrvas.dto.business.topic.ParadigmEventDTO;
import com.cqqy.hrvas.response.ApiResponse;
import com.cqqy.hrvas.service.ParadigmFileService;
import com.cqqy.hrvas.vo.topic.ConsistencyVo;
import com.cqqy.hrvas.vo.topic.MatTopicVo;
import com.cqqy.hrvas.vo.topic.NonConsistencyVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@Tag(name = "HRV范式")
@RestController
@RequestMapping("/api/paradigm_file")
public class ParadigmFileController {

    private final ParadigmFileService paradigmFileService;

    @Autowired
    public ParadigmFileController(ParadigmFileService paradigmFileService) {
        this.paradigmFileService = paradigmFileService;
    }

    @Permission("detector:paradigm_file:scwt")
    @GetMapping("/scwt")
    @Operation(summary = "获取scwt音乐和图片")
    public ApiResponse<ParadigmFileDTO> sourceScwt() {
        return ApiResponse.ok(paradigmFileService.getParadigmFile("scwt"));
    }

    @Permission("detector:paradigm_file:mat")
    @GetMapping("/mat")
    @Operation(summary = "获取mat音乐和图片")
    public ApiResponse<ParadigmFileDTO> sourceMat() {
        return ApiResponse.ok(paradigmFileService.getParadigmFile("mat"));
    }

    @Permission("detector:paradigm_file:scwt:consistency")
    @GetMapping("/topic_scwt_consistency")
    @Operation(summary = "获取scwt 一致性测试题目")
    public ApiResponse<ConsistencyVo> topicMatConsistency() {
        ConsistencyVo consistencyVo = new ConsistencyVo();
        List<ConsistencyDTO> list = paradigmFileService.getScwtConsistency();
        consistencyVo.setId(IdUtil.fastSimpleUUID());
        consistencyVo.setList(list);
        return ApiResponse.ok(consistencyVo);
    }

    @Permission("detector:paradigm_file:scwt:not_consistency")
    @GetMapping("/topic_scwt_not_consistency")
    @Operation(summary = "获取scwt 非一致性测试题目")
    public ApiResponse<NonConsistencyVo> topicMatNotConsistency() {
        NonConsistencyVo nonConsistencyVo = new NonConsistencyVo();
        List<NonConsistencyDTO> list = paradigmFileService.getScwtNonConsistency();
        nonConsistencyVo.setId(IdUtil.fastSimpleUUID());
        nonConsistencyVo.setList(list);
        return ApiResponse.ok(nonConsistencyVo);
    }

    @Permission("detector:paradigm_file:mat:es")
    @GetMapping("/topic_mat_es")
    @Operation(summary = "获取mat ES阶段题目")
    public ApiResponse<MatTopicVo> topicScwtEs() {
        MatTopicVo matTopicVo = new MatTopicVo();
        List<MatTopicDTO> list = paradigmFileService.getMatES();
        matTopicVo.setId(IdUtil.fastSimpleUUID());
        matTopicVo.setList(list);
        return ApiResponse.ok(matTopicVo);
    }

    @Permission("detector:paradigm_file:mat:ds")
    @GetMapping("/topic_mat_ds")
    @Operation(summary = "获取mat DS阶段题目")
    public ApiResponse<MatTopicVo> topicScwtDs() {
        MatTopicVo matTopicVo = new MatTopicVo();
        List<MatTopicDTO> list = paradigmFileService.getMatDS();
        matTopicVo.setId(IdUtil.fastSimpleUUID());
        matTopicVo.setList(list);
        return ApiResponse.ok(matTopicVo);
    }

    @Permission("detector:paradigm_file:commit")
    @PostMapping("/commit_event")
    @Operation(summary = "提交scwt/mat记录")
    public ApiResponse<Void> commitEvent(@RequestParam(name = "inspectListId") Long inspectListId,
                            @RequestParam(name = "taskType") String taskType,
                            @RequestBody List<ParadigmEventDTO> paradigmEventDTOList) throws IOException {
        paradigmFileService.commitEvent(inspectListId, taskType, paradigmEventDTOList);
        return ApiResponse.ok();
    }
}
