package com.cqqy.hrvas.controller;

import com.cqqy.hrvas.dto.ProjectVersionDTO;
import com.cqqy.hrvas.response.ApiResponse;
import com.cqqy.hrvas.service.ProjectVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/version")
public class ProjectVersionController {
    private final ProjectVersionService projectVersionService;

    @Autowired
    public ProjectVersionController(ProjectVersionService projectVersionService) {
        this.projectVersionService = projectVersionService;
    }

    @GetMapping("/current")
    public ApiResponse<ProjectVersionDTO> getCurrentVersion() {
        ProjectVersionDTO currentVersion = projectVersionService.getCurrentVersion();
        return ApiResponse.ok(currentVersion);
    }
}