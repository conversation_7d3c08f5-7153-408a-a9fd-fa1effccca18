package com.cqqy.hrvas.controller;

import com.cqqy.hrvas.annotation.Permission;
import com.cqqy.hrvas.response.ApiResponse;
import com.cqqy.hrvas.service.task.TaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@Tag(name = "comm-任务控制")
@Slf4j
@RestController
@RequestMapping("/api/task")
public class TaskController {

    private final TaskService taskService;

    public TaskController(TaskService taskService) {
        this.taskService = taskService;
    }

    @Operation(summary = "采集任务开始")
    @Permission("detector:task:exam:start")
    @GetMapping("/exam/start")
    public ApiResponse<Void> examStart(@RequestParam(name = "taskId") Long taskId,
                                       @RequestParam(name = "time") Integer time,
                                       @RequestParam(name = "age") Integer age,
                                       @RequestParam(name = "isGeneralTask") Boolean isGeneralTask) throws IOException {
        int patientType;
        if (age >= 18) {
            patientType = 0;
        } else {
            patientType = 2;
        }
        // 非临时或离线任务则修改平台端清单状态
        if (isGeneralTask) {
            taskService.taskStart(taskId);
        }
        taskService.examStart(taskId, time, patientType);
        return ApiResponse.ok();
    }

    @Operation(summary = "采集任务结束")
    @Permission("detector:task:exam:stop")
    @GetMapping("/exam/stop")
    public ApiResponse<Void> examStop(@RequestParam(name = "taskId") Long taskId,
                                      @RequestParam(name = "isGeneralTask") Boolean isGeneralTask) {
        // 非临时或离线任务则修改平台端清单状态
        if (isGeneralTask) {
            taskService.taskEnd(taskId);
        }
        taskService.examStop();
        return ApiResponse.ok();
    }

    @Operation(summary = "采集任务退出")
    @Permission("detector:task:exam:exit")
    @GetMapping("/exam/exit")
    public ApiResponse<Void> examExit(@RequestParam(name = "taskId") Long taskId,
                                      @RequestParam(name = "isGeneralTask") Boolean isGeneralTask) {
        if (isGeneralTask) {
            taskService.taskExit(taskId);
        }
        return ApiResponse.ok();
    }

    @Operation(summary = "采集任务终止（删除）")
    @Permission("detector:task:exam:discontinue")
    @GetMapping("/exam/discontinue")
    public ApiResponse<Void> examDiscontinue(@RequestParam(name = "taskId") Long taskId,
                                             @RequestParam(name = "isGeneralTask") Boolean isGeneralTask) {
        if (isGeneralTask) {
            taskService.taskDiscontinue(taskId);
        }
        return ApiResponse.ok();
    }
}
