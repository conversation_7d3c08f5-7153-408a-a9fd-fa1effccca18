package com.cqqy.hrvas.controller;

import com.cqqy.hrvas.annotation.Permission;
import com.cqqy.hrvas.dto.business.TempPatientInfoDTO;
import com.cqqy.hrvas.response.ApiResponse;
import com.cqqy.hrvas.service.TempPatientInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "临时任务")
@RestController
@RequestMapping("/api/patient_info")
public class TempPatientInfoController {

    private final TempPatientInfoService tempPatientInfoService;

    @Autowired
    public TempPatientInfoController(TempPatientInfoService tempPatientInfoService) {
        this.tempPatientInfoService = tempPatientInfoService;
    }

    @Permission("detector:patient_info:add")
    @PostMapping("/add")
    @Operation(summary = "临时任务添加")
    public ApiResponse<Long> add(@RequestBody TempPatientInfoDTO tempPatientInfoDTO) {
        return ApiResponse.ok(tempPatientInfoService.addTempPatientInfo(tempPatientInfoDTO));
    }
}
