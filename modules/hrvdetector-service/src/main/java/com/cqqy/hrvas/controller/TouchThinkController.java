package com.cqqy.hrvas.controller;

import com.cqqy.hrvas.annotation.Permission;
import com.cqqy.hrvas.response.ApiResponse;
import com.cqqy.hrvas.service.TouchThinkService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "平板控制")
@RestController
@RequestMapping("/api/touch_think")
public class TouchThinkController {

    private final TouchThinkService touchThinkService;

    public TouchThinkController(TouchThinkService touchThinkService) {
        this.touchThinkService = touchThinkService;
    }

    @Permission("detector:touch_think:audio:mute")
    @GetMapping("/audio/mute")
    @Operation(summary = "静音")
    public ApiResponse<Void> audioMute() {
        touchThinkService.audioMute();
        return ApiResponse.ok();
    }

    @Permission("detector:touch_think:audio:unmute")
    @GetMapping("/audio/unmute")
    @Operation(summary = "取消静音")
    public ApiResponse<Void> audioUnmute() {
        touchThinkService.audioUnmute();
        return ApiResponse.ok();
    }

    @Permission("detector:touch_think:backlight:sleep")
    @GetMapping("/backlight/sleep")
    @Operation(summary = "休眠")
    public ApiResponse<Void> backlightSleep() {
        touchThinkService.backlightSleep();
        return ApiResponse.ok();
    }

    @Permission("detector:touch_think:backlight:wakeup")
    @GetMapping("/backlight/wakeup")
    @Operation(summary = "唤醒")
    public ApiResponse<Void> backlightWakeup() {
        touchThinkService.backlightWakeup();
        return ApiResponse.ok();
    }

    @GetMapping("/shutdown")
    @Operation(summary = "关机")
    public ApiResponse<Void> shutdown() {
        touchThinkService.shutdown();
        return ApiResponse.ok();
    }
}
