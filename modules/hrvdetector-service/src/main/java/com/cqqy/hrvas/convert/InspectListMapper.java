package com.cqqy.hrvas.convert;

import com.cqqy.hrvas.dto.business.InspectListDTO;
import com.cqqy.hrvas.entity.InspectList;
import com.cqqy.hrvas.vo.business.InspectListVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface InspectListMapper {

    InspectListMapper INSTANCE = Mappers.getMapper(InspectListMapper.class);

    InspectListVo entityToVo(InspectList inspectList);

    List<InspectListVo> listToVoList(List<InspectList> inspectLists);
}
