package com.cqqy.hrvas.convert.topic;

import com.cqqy.hrvas.dto.business.topic.ConsistencyDTO;
import com.cqqy.hrvas.dto.business.topic.MatTopicDTO;
import com.cqqy.hrvas.dto.business.topic.NonConsistencyDTO;
import com.cqqy.hrvas.entity.topic.RecordMat;
import com.cqqy.hrvas.entity.topic.RecordScwtCons;
import com.cqqy.hrvas.entity.topic.RecordScwtNonCons;
import com.cqqy.hrvas.questionbank.scwt.Consistency;
import com.cqqy.hrvas.questionbank.scwt.NonConsistency;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Mapper
public interface TopicMapper {

    TopicMapper INSTANCE = Mappers.getMapper(TopicMapper.class);

    ConsistencyDTO consToDto(Consistency consistency);

    NonConsistencyDTO nonConsToDto(NonConsistency nonConsistency);

    List<RecordMat> dtoListToEntityListMat(List<MatTopicDTO> matTopicDTOList);

    @Named("listToString")
    default String listToString(List<?> list) {
        return list.stream()
                .map(Object::toString)
                .collect(Collectors.joining(","));
    }

    @Mapping(target = "topicCharacter", source = "topicCharacter", qualifiedByName = "listToString")
    RecordScwtCons dtoToEntityCons(ConsistencyDTO consistencyDTO);

    List<RecordScwtCons> dtoListToEntityListCons(List<ConsistencyDTO> consistencyDTOList);

    @Mapping(target = "topicGraphic", source = "topicGraphic", qualifiedByName = "listToString")
    RecordScwtNonCons dtoToEntityNonCons(NonConsistencyDTO nonConsistencyDTO);

    List<RecordScwtNonCons> dtoListToEntityListNonCons(List<NonConsistencyDTO> nonConsistencyDTOList);


    @Named("stringToList")
    default List<Integer> stringToList(String str) {
        if (str == null || str.trim().isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.stream(str.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .flatMap(s -> {
                    try {
                        return Stream.of(Integer.parseInt(s));
                    } catch (NumberFormatException e) {
                        return Stream.empty();  // 忽略非法值
                    }
                })
                .collect(Collectors.toList());
    }

    @Mapping(target = "topicCharacter", source = "topicCharacter", qualifiedByName = "stringToList")
    ConsistencyDTO entityToDtoCons(RecordScwtCons recordScwtCons);

    List<ConsistencyDTO> entityListToDtoListCons(List<RecordScwtCons> recordScwtConsList);

    @Mapping(target = "topicGraphic", source = "topicGraphic", qualifiedByName = "stringToList")
    NonConsistencyDTO entityToDtoNonCons(RecordScwtNonCons recordScwtNonCons);

    List<NonConsistencyDTO> entityListToDtoListNonCons(List<RecordScwtNonCons> recordScwtNonConsList);
}
