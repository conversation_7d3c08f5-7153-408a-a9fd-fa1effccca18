package com.cqqy.hrvas.dto;

import java.time.LocalDateTime;

public class ProjectVersionDTO {
    private Long id;
    private String versionNumber;
    private String description;
    private LocalDateTime releaseDate;
    private Boolean isActive;

    public ProjectVersionDTO(Long id, String versionNumber, String description, LocalDateTime releaseDate, Boolean isActive) {
        this.id = id;
        this.versionNumber = versionNumber;
        this.description = description;
        this.releaseDate = releaseDate;
        this.isActive = isActive;
    }

    public Long getId() {
        return id;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public String getDescription() {
        return description;
    }

    public LocalDateTime getReleaseDate() {
        return releaseDate;
    }

    public Boolean getIsActive() {
        return isActive;
    }
}