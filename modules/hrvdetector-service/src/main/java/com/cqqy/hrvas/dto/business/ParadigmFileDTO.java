package com.cqqy.hrvas.dto.business;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

public class ParadigmFileDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String stroopType;

    private List<String> musicList;

    private List<String> pictureList;

    public String getStroopType() {
        return stroopType;
    }

    public void setStroopType(String stroopType) {
        this.stroopType = stroopType;
    }

    public List<String> getMusicList() {
        return musicList;
    }

    public void setMusicList(List<String> musicList) {
        this.musicList = musicList;
    }

    public List<String> getPictureList() {
        return pictureList;
    }

    public void setPictureList(List<String> pictureList) {
        this.pictureList = pictureList;
    }
}
