package com.cqqy.hrvas.dto.business.topic;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "ConsistencyDTO")
public class ConsistencyDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "题目顺序")
    @NotNull(message = "topicSort不能为空")
    private Integer topicSort;

    @Schema(description = "四种颜色之一的圆形 0-红 1-黄 2-蓝 3-绿")
    @NotNull(message = "topicGraphic不能为空")
    private Integer topicGraphic; // 随机一个红黄蓝绿四个颜色的圆形 0-红 1-黄 2-蓝 3-绿

    @Schema(description = "字和字的颜色 0-红 1-黄 2-蓝 3-绿")
    @NotNull(message = "topicCharacter不能为空")
    private List<Integer> topicCharacter; // 红黄蓝绿四个颜色与含义相同的字 0-红 1-黄 2-蓝 3-绿

    @Schema(description = "正确答案：圆形的颜色 0-红 1-黄 2-蓝 3-绿")
    @NotNull(message = "correctAnswer不能为空")
    private Integer correctAnswer; // 正确的字的序号<=>graphic

    @Schema(description = "用户答案：圆形的颜色 0-红 1-黄 2-蓝 3-绿")
    @NotNull(message = "actualAnswer不能为空")
    private Integer actualAnswer;

    public Integer getTopicSort() {
        return topicSort;
    }

    public void setTopicSort(Integer topicSort) {
        this.topicSort = topicSort;
    }

    public Integer getTopicGraphic() {
        return topicGraphic;
    }

    public void setTopicGraphic(Integer topicGraphic) {
        this.topicGraphic = topicGraphic;
    }

    public List<Integer> getTopicCharacter() {
        return topicCharacter;
    }

    public void setTopicCharacter(List<Integer> topicCharacter) {
        this.topicCharacter = topicCharacter;
    }

    public Integer getCorrectAnswer() {
        return correctAnswer;
    }

    public void setCorrectAnswer(Integer correctAnswer) {
        this.correctAnswer = correctAnswer;
    }

    public Integer getActualAnswer() {
        return actualAnswer;
    }

    public void setActualAnswer(Integer actualAnswer) {
        this.actualAnswer = actualAnswer;
    }
}
