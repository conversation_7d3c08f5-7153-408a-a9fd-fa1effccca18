package com.cqqy.hrvas.dto.business.topic;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "MatTopicDTO")
public class MatTopicDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "题目顺序")
    @NotNull(message = "topicSort不能为空")
    private Integer topicSort;

    @Schema(description = "题目")
    @NotBlank(message = "topic不能为空")
    @NotNull(message = "topic不能为空")
    private String topic;

    @Schema(description = "正确答案")
    @NotBlank(message = "correctAnswer不能为空")
    @NotNull(message = "correctAnswer不能为空")
    private String correctAnswer;

    @Schema(description = "用户答案")
    @NotNull(message = "actualAnswer不能为空")
    private String actualAnswer;

    public Integer getTopicSort() {
        return topicSort;
    }

    public void setTopicSort(Integer topicSort) {
        this.topicSort = topicSort;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getCorrectAnswer() {
        return correctAnswer;
    }

    public void setCorrectAnswer(String correctAnswer) {
        this.correctAnswer = correctAnswer;
    }

    public String getActualAnswer() {
        return actualAnswer;
    }

    public void setActualAnswer(String actualAnswer) {
        this.actualAnswer = actualAnswer;
    }
}
