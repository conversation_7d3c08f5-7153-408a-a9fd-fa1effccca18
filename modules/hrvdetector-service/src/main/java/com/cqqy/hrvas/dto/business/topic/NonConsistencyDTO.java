package com.cqqy.hrvas.dto.business.topic;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "NonConsistencyDTO")
public class NonConsistencyDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "题目顺序")
    @NotNull(message = "topicSort不能为空")
    private Integer topicSort;

    @Schema(description = "字 0-红 1-黄 2-蓝 3-绿")
    @NotNull(message = "topicCharacter不能为空")
    private Integer topicCharacter; // 红黄蓝绿四个字中随机一个 0-红 1-黄 2-蓝 3-绿

    @Schema(description = "字的颜色 0-红 1-黄 2-蓝 3-绿")
    @NotNull(message = "topicCharacterColor不能为空")
    private Integer topicCharacterColor; // 字的颜色（不与字含义相同） 0-红 1-黄 2-蓝 3-绿

    @Schema(description = "四个颜色的圆形 0-红 1-黄 2-蓝 3-绿")
    @NotNull(message = "topicGraphic不能为空")
    private List<Integer> topicGraphic; // 红黄蓝绿四个颜色的圆形 0-红 1-黄 2-蓝 3-绿

    @Schema(description = "正确答案：字的颜色 0-红 1-黄 2-蓝 3-绿")
    @NotNull(message = "correctAnswer不能为空")
    private Integer correctAnswer; // 正确的圆形的序号 <=>character_color

    @Schema(description = "用户答案：字的颜色 0-红 1-黄 2-蓝 3-绿")
    @NotNull(message = "actualAnswer不能为空")
    private Integer actualAnswer; // 正确的圆形的序号 <=>character_color

    public Integer getTopicSort() {
        return topicSort;
    }

    public void setTopicSort(Integer topicSort) {
        this.topicSort = topicSort;
    }

    public Integer getTopicCharacter() {
        return topicCharacter;
    }

    public void setTopicCharacter(Integer topicCharacter) {
        this.topicCharacter = topicCharacter;
    }

    public Integer getTopicCharacterColor() {
        return topicCharacterColor;
    }

    public void setTopicCharacterColor(Integer topicCharacterColor) {
        this.topicCharacterColor = topicCharacterColor;
    }

    public List<Integer> getTopicGraphic() {
        return topicGraphic;
    }

    public void setTopicGraphic(List<Integer> topicGraphic) {
        this.topicGraphic = topicGraphic;
    }

    public Integer getCorrectAnswer() {
        return correctAnswer;
    }

    public void setCorrectAnswer(Integer correctAnswer) {
        this.correctAnswer = correctAnswer;
    }

    public Integer getActualAnswer() {
        return actualAnswer;
    }

    public void setActualAnswer(Integer actualAnswer) {
        this.actualAnswer = actualAnswer;
    }
}
