package com.cqqy.hrvas.dto.business.topic;

import io.swagger.v3.oas.annotations.media.Schema;

public class ParadigmEventDTO {

    @Schema(description = "时间戳")
    private Long timestamp;

    @Schema(description = "事件名称 start(开始) end(结束) topic(答题) ")
    private String eventName;

    @Schema(description = "题目类型 0-mat 1-scwt一致性测试 2-scwt非一致性测试")
    private Integer topicType;

    @Schema(description = "scwt 一致性测试答题记录")
    private ConsistencyDTO consistencyDTO;

    @Schema(description = "scwt 非一致性测试答题记录")
    private NonConsistencyDTO nonConsistencyDTO;

    @Schema(description = "mat 答题记录")
    private MatTopicDTO matTopicDTO;

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public Integer getTopicType() {
        return topicType;
    }

    public void setTopicType(Integer topicType) {
        this.topicType = topicType;
    }

    public ConsistencyDTO getConsistencyDTO() {
        return consistencyDTO;
    }

    public void setConsistencyDTO(ConsistencyDTO consistencyDTO) {
        this.consistencyDTO = consistencyDTO;
    }

    public NonConsistencyDTO getNonConsistencyDTO() {
        return nonConsistencyDTO;
    }

    public void setNonConsistencyDTO(NonConsistencyDTO nonConsistencyDTO) {
        this.nonConsistencyDTO = nonConsistencyDTO;
    }

    public MatTopicDTO getMatTopicDTO() {
        return matTopicDTO;
    }

    public void setMatTopicDTO(MatTopicDTO matTopicDTO) {
        this.matTopicDTO = matTopicDTO;
    }
}
