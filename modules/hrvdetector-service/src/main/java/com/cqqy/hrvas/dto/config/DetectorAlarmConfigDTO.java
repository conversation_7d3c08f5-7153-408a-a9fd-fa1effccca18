package com.cqqy.hrvas.dto.config;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

public class DetectorAlarmConfigDTO {

    @NotNull(message = "心电过速/过缓设置不能为空")
    @Schema(description = "心电过速/过缓-成人-上限")
    private Integer alarmEcgHeartRateAdultUpper;

    @NotNull(message = "心电过速/过缓设置不能为空")
    @Schema(description = "心电过速/过缓-成人-下限")
    private Integer alarmEcgHeartRateAdultLower;

    @NotNull(message = "心电过速/过缓设置不能为空")
    @Schema(description = "心电过速/过缓-儿童-上限")
    private Integer alarmEcgHeartRateChildUpper;

    @NotNull(message = "心电过速/过缓设置不能为空")
    @Schema(description = "心电过速/过缓-儿童-下限")
    private Integer alarmEcgHeartRateChildLower;

    @NotNull(message = "心电过速/过缓设置不能为空")
    @Schema(description = "心电过速/过缓-老年及冠心病-上限")
    private Integer alarmEcgHeartRateOldUpper;

    @NotNull(message = "心电过速/过缓设置不能为空")
    @Schema(description = "心电过速/过缓-老年及冠心病-下限")
    private Integer alarmEcgHeartRateOldLower;

    @NotNull(message = "纤维性颤动设置不能为空")
    @Schema(description = "纤维性颤动-成人")
    private Integer alarmFibroticTremorAdult;

    @NotNull(message = "纤维性颤动设置不能为空")
    @Schema(description = "纤维性颤动-儿童")
    private Integer alarmFibroticTremorChild;

    @NotNull(message = "低电报警电量不能为空")
    @Schema(description = "低电报警电量")
    @Max(1)
    @Min(0)
    private Double alarmBatteryLow;

    public Integer getAlarmEcgHeartRateAdultUpper() {
        return alarmEcgHeartRateAdultUpper;
    }

    public void setAlarmEcgHeartRateAdultUpper(Integer alarmEcgHeartRateAdultUpper) {
        this.alarmEcgHeartRateAdultUpper = alarmEcgHeartRateAdultUpper;
    }

    public Integer getAlarmEcgHeartRateAdultLower() {
        return alarmEcgHeartRateAdultLower;
    }

    public void setAlarmEcgHeartRateAdultLower(Integer alarmEcgHeartRateAdultLower) {
        this.alarmEcgHeartRateAdultLower = alarmEcgHeartRateAdultLower;
    }

    public Integer getAlarmEcgHeartRateChildUpper() {
        return alarmEcgHeartRateChildUpper;
    }

    public void setAlarmEcgHeartRateChildUpper(Integer alarmEcgHeartRateChildUpper) {
        this.alarmEcgHeartRateChildUpper = alarmEcgHeartRateChildUpper;
    }

    public Integer getAlarmEcgHeartRateChildLower() {
        return alarmEcgHeartRateChildLower;
    }

    public void setAlarmEcgHeartRateChildLower(Integer alarmEcgHeartRateChildLower) {
        this.alarmEcgHeartRateChildLower = alarmEcgHeartRateChildLower;
    }

    public Integer getAlarmEcgHeartRateOldUpper() {
        return alarmEcgHeartRateOldUpper;
    }

    public void setAlarmEcgHeartRateOldUpper(Integer alarmEcgHeartRateOldUpper) {
        this.alarmEcgHeartRateOldUpper = alarmEcgHeartRateOldUpper;
    }

    public Integer getAlarmEcgHeartRateOldLower() {
        return alarmEcgHeartRateOldLower;
    }

    public void setAlarmEcgHeartRateOldLower(Integer alarmEcgHeartRateOldLower) {
        this.alarmEcgHeartRateOldLower = alarmEcgHeartRateOldLower;
    }

    public Integer getAlarmFibroticTremorAdult() {
        return alarmFibroticTremorAdult;
    }

    public void setAlarmFibroticTremorAdult(Integer alarmFibroticTremorAdult) {
        this.alarmFibroticTremorAdult = alarmFibroticTremorAdult;
    }

    public Integer getAlarmFibroticTremorChild() {
        return alarmFibroticTremorChild;
    }

    public void setAlarmFibroticTremorChild(Integer alarmFibroticTremorChild) {
        this.alarmFibroticTremorChild = alarmFibroticTremorChild;
    }

    public Double getAlarmBatteryLow() {
        return alarmBatteryLow;
    }

    public void setAlarmBatteryLow(Double alarmBatteryLow) {
        this.alarmBatteryLow = alarmBatteryLow;
    }
}
