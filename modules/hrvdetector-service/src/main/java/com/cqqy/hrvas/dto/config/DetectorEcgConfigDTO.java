package com.cqqy.hrvas.dto.config;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

public class DetectorEcgConfigDTO {

    @Max(-20)
    @Min(-50)
    @NotNull(message = "ST模板-ISO不能为空")
    @Schema(description = "ST模板-ISO 范围：-50 ~ -20")
    private Integer ecgStTemplateIso;

    @Max(50)
    @Min(20)
    @NotNull(message = "ST模板-ST不能为空")
    @Schema(description = "ST模板-ST 范围：20 ~ 50")
    private Integer ecgStTemplateSt;

    @Max(3)
    @Min(0)
    @NotNull(message = "陷波模式不能为空")
    @Schema(description = "陷波模式 0-50hz 1-60hz 2-50/60hz 3-无陷波")
    private Integer ecgNotchMode;

    @Max(0)
    @Min(0)
    @NotNull(message = "心率失常通道不能为空")
    @Schema(description = "心率失常通道 0-I通道")
    private Integer ecgArrhythmiaChannel;

    @Max(0)
    @Min(0)
    @NotNull(message = "上电滤波不能为空")
    @Schema(description = "上电滤波 0-诊断模式")
    private Integer ecgPowerFiltering;

    @Max(3)
    @Min(0)
    @NotNull(message = "增益设置不能为空")
    @Schema(description = "增益设置 0-x250 1-x500 2-x1000 3-x2000")
    private Integer ecgGain;

    @Max(2)
    @Min(0)
    @NotNull(message = "病人模式不能为空")
    @Schema(description = "病人模式 0-成人 1-儿童 2-新生儿")
    private Integer ecgPatientModel;

    @Max(0)
    @Min(0)
    @NotNull(message = "导联模式不能为空")
    @Schema(description = "导联模式 0-3导联")
    private Integer ecgLeadModel;

    @Max(0)
    @Min(0)
    @NotNull(message = "导联通道不能为空")
    @Schema(description = "导联通道 0-I通道")
    private Integer ecgLeadChannel;

    public Integer getEcgStTemplateIso() {
        return ecgStTemplateIso;
    }

    public void setEcgStTemplateIso(Integer ecgStTemplateIso) {
        this.ecgStTemplateIso = ecgStTemplateIso;
    }

    public Integer getEcgStTemplateSt() {
        return ecgStTemplateSt;
    }

    public void setEcgStTemplateSt(Integer ecgStTemplateSt) {
        this.ecgStTemplateSt = ecgStTemplateSt;
    }

    public Integer getEcgNotchMode() {
        return ecgNotchMode;
    }

    public void setEcgNotchMode(Integer ecgNotchMode) {
        this.ecgNotchMode = ecgNotchMode;
    }

    public Integer getEcgArrhythmiaChannel() {
        return ecgArrhythmiaChannel;
    }

    public void setEcgArrhythmiaChannel(Integer ecgArrhythmiaChannel) {
        this.ecgArrhythmiaChannel = ecgArrhythmiaChannel;
    }

    public Integer getEcgPowerFiltering() {
        return ecgPowerFiltering;
    }

    public void setEcgPowerFiltering(Integer ecgPowerFiltering) {
        this.ecgPowerFiltering = ecgPowerFiltering;
    }

    public Integer getEcgGain() {
        return ecgGain;
    }

    public void setEcgGain(Integer ecgGain) {
        this.ecgGain = ecgGain;
    }

    public Integer getEcgPatientModel() {
        return ecgPatientModel;
    }

    public void setEcgPatientModel(Integer ecgPatientModel) {
        this.ecgPatientModel = ecgPatientModel;
    }

    public Integer getEcgLeadModel() {
        return ecgLeadModel;
    }

    public void setEcgLeadModel(Integer ecgLeadModel) {
        this.ecgLeadModel = ecgLeadModel;
    }

    public Integer getEcgLeadChannel() {
        return ecgLeadChannel;
    }

    public void setEcgLeadChannel(Integer ecgLeadChannel) {
        this.ecgLeadChannel = ecgLeadChannel;
    }
}
