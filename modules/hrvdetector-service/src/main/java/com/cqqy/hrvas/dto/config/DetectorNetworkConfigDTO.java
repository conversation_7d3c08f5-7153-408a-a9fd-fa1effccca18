package com.cqqy.hrvas.dto.config;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

public class DetectorNetworkConfigDTO {

    @NotNull(message = "服务器配置不能为空")
    @NotBlank(message = "服务器配置不能为空")
    @Schema(description = "服务器配置")
    private String netServerUrl;

    @NotNull(message = "平台服务不能为空")
    @NotBlank(message = "平台服务不能为空")
    @Schema(description = "平台服务")
    private String netPlatformUrl;

    @NotNull(message = "报告服务不能为空")
    @NotBlank(message = "报告服务不能为空")
    @Schema(description = "报告服务")
    private String netReportUrl;

    @NotNull(message = "是否在线升级不能为空")
    @Schema(description = "是否在线升级")
    private Integer netOnlineUpgrade;

    public String getNetServerUrl() {
        return netServerUrl;
    }

    public void setNetServerUrl(String netServerUrl) {
        this.netServerUrl = netServerUrl;
    }

    public String getNetPlatformUrl() {
        return netPlatformUrl;
    }

    public void setNetPlatformUrl(String netPlatformUrl) {
        this.netPlatformUrl = netPlatformUrl;
    }

    public String getNetReportUrl() {
        return netReportUrl;
    }

    public void setNetReportUrl(String netReportUrl) {
        this.netReportUrl = netReportUrl;
    }

    public Integer getNetOnlineUpgrade() {
        return netOnlineUpgrade;
    }

    public void setNetOnlineUpgrade(Integer netOnlineUpgrade) {
        this.netOnlineUpgrade = netOnlineUpgrade;
    }
}
