package com.cqqy.hrvas.dto.config;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

public class DetectorTestConfigDTO {

    @NotNull(message = "测试通过区间设置不能为空")
    @Schema(description = "测试通过区间-上限")
    private Integer testPassedIntervalUpper;

    @NotNull(message = "测试通过区间设置不能为空")
    @Schema(description = "测试通过区间-下限")
    private Integer testPassedIntervalLower;

    public Integer getTestPassedIntervalUpper() {
        return testPassedIntervalUpper;
    }

    public void setTestPassedIntervalUpper(Integer testPassedIntervalUpper) {
        this.testPassedIntervalUpper = testPassedIntervalUpper;
    }

    public Integer getTestPassedIntervalLower() {
        return testPassedIntervalLower;
    }

    public void setTestPassedIntervalLower(Integer testPassedIntervalLower) {
        this.testPassedIntervalLower = testPassedIntervalLower;
    }
}
