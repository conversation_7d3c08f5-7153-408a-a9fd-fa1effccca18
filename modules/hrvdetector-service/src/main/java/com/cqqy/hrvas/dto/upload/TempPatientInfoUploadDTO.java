package com.cqqy.hrvas.dto.upload;

import com.cqqy.hrvas.entity.InspectList;

public class TempPatientInfoUploadDTO {

    private Long id;

    private String name;

    private Integer age;

    private String gender;

    private String outpatientNo;

    private Long deptId;

    private String deptName;

    private InspectList inspectList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getOutpatientNo() {
        return outpatientNo;
    }

    public void setOutpatientNo(String outpatientNo) {
        this.outpatientNo = outpatientNo;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public InspectList getInspectList() {
        return inspectList;
    }

    public void setInspectList(InspectList inspectList) {
        this.inspectList = inspectList;
    }
}
