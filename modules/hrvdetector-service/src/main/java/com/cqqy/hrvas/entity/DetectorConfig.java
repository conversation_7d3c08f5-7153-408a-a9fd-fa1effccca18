package com.cqqy.hrvas.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "detector_config")
public class DetectorConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String netServerUrl;
    
    private String netPlatformUrl;
    
    private String netReportUrl;
    
    private Integer netOnlineUpgrade;
    
    private Integer ecgStTemplateIso;

    private Integer ecgStTemplateSt;

    private Integer ecgNotchMode;
    
    private Integer ecgArrhythmiaChannel;
    
    private Integer ecgPowerFiltering;
    
    private Integer ecgGain;
    
    private Integer alarmEcgHeartRateAdultUpper;
    
    private Integer alarmEcgHeartRateAdultLower;

    private Integer alarmEcgHeartRateChildUpper;

    private Integer alarmEcgHeartRateChildLower;

    private Integer alarmEcgHeartRateOldUpper;

    private Integer alarmEcgHeartRateOldLower;

    private Integer alarmFibroticTremorAdult;

    private Integer alarmFibroticTremorChild;

    private Double alarmBatteryLow;

    private Integer testPassedIntervalUpper;

    private Integer testPassedIntervalLower;

    private Integer ecgPatientModel;

    private Integer ecgLeadModel;

    private Integer ecgLeadChannel;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNetServerUrl() {
        return netServerUrl;
    }

    public void setNetServerUrl(String netServerUrl) {
        this.netServerUrl = netServerUrl;
    }

    public String getNetPlatformUrl() {
        return netPlatformUrl;
    }

    public void setNetPlatformUrl(String netPlatformUrl) {
        this.netPlatformUrl = netPlatformUrl;
    }

    public String getNetReportUrl() {
        return netReportUrl;
    }

    public void setNetReportUrl(String netReportUrl) {
        this.netReportUrl = netReportUrl;
    }

    public Integer getNetOnlineUpgrade() {
        return netOnlineUpgrade;
    }

    public void setNetOnlineUpgrade(Integer netOnlineUpgrade) {
        this.netOnlineUpgrade = netOnlineUpgrade;
    }

    public Integer getEcgStTemplateIso() {
        return ecgStTemplateIso;
    }

    public void setEcgStTemplateIso(Integer ecgStTemplateIso) {
        this.ecgStTemplateIso = ecgStTemplateIso;
    }

    public Integer getEcgStTemplateSt() {
        return ecgStTemplateSt;
    }

    public void setEcgStTemplateSt(Integer ecgStTemplateSt) {
        this.ecgStTemplateSt = ecgStTemplateSt;
    }

    public Integer getEcgNotchMode() {
        return ecgNotchMode;
    }

    public void setEcgNotchMode(Integer ecgNotchMode) {
        this.ecgNotchMode = ecgNotchMode;
    }

    public Integer getEcgArrhythmiaChannel() {
        return ecgArrhythmiaChannel;
    }

    public void setEcgArrhythmiaChannel(Integer ecgArrhythmiaChannel) {
        this.ecgArrhythmiaChannel = ecgArrhythmiaChannel;
    }

    public Integer getEcgPowerFiltering() {
        return ecgPowerFiltering;
    }

    public void setEcgPowerFiltering(Integer ecgPowerFiltering) {
        this.ecgPowerFiltering = ecgPowerFiltering;
    }

    public Integer getEcgGain() {
        return ecgGain;
    }

    public void setEcgGain(Integer ecgGain) {
        this.ecgGain = ecgGain;
    }

    public Integer getAlarmEcgHeartRateAdultUpper() {
        return alarmEcgHeartRateAdultUpper;
    }

    public void setAlarmEcgHeartRateAdultUpper(Integer alarmEcgHeartRateAdultUpper) {
        this.alarmEcgHeartRateAdultUpper = alarmEcgHeartRateAdultUpper;
    }

    public Integer getAlarmEcgHeartRateAdultLower() {
        return alarmEcgHeartRateAdultLower;
    }

    public void setAlarmEcgHeartRateAdultLower(Integer alarmEcgHeartRateAdultLower) {
        this.alarmEcgHeartRateAdultLower = alarmEcgHeartRateAdultLower;
    }

    public Integer getAlarmEcgHeartRateChildUpper() {
        return alarmEcgHeartRateChildUpper;
    }

    public void setAlarmEcgHeartRateChildUpper(Integer alarmEcgHeartRateChildUpper) {
        this.alarmEcgHeartRateChildUpper = alarmEcgHeartRateChildUpper;
    }

    public Integer getAlarmEcgHeartRateChildLower() {
        return alarmEcgHeartRateChildLower;
    }

    public void setAlarmEcgHeartRateChildLower(Integer alarmEcgHeartRateChildLower) {
        this.alarmEcgHeartRateChildLower = alarmEcgHeartRateChildLower;
    }

    public Integer getAlarmEcgHeartRateOldUpper() {
        return alarmEcgHeartRateOldUpper;
    }

    public void setAlarmEcgHeartRateOldUpper(Integer alarmEcgHeartRateOldUpper) {
        this.alarmEcgHeartRateOldUpper = alarmEcgHeartRateOldUpper;
    }

    public Integer getAlarmEcgHeartRateOldLower() {
        return alarmEcgHeartRateOldLower;
    }

    public void setAlarmEcgHeartRateOldLower(Integer alarmEcgHeartRateOldLower) {
        this.alarmEcgHeartRateOldLower = alarmEcgHeartRateOldLower;
    }

    public Integer getAlarmFibroticTremorAdult() {
        return alarmFibroticTremorAdult;
    }

    public void setAlarmFibroticTremorAdult(Integer alarmFibroticTremorAdult) {
        this.alarmFibroticTremorAdult = alarmFibroticTremorAdult;
    }

    public Integer getAlarmFibroticTremorChild() {
        return alarmFibroticTremorChild;
    }

    public void setAlarmFibroticTremorChild(Integer alarmFibroticTremorChild) {
        this.alarmFibroticTremorChild = alarmFibroticTremorChild;
    }

    public Double getAlarmBatteryLow() {
        return alarmBatteryLow;
    }

    public void setAlarmBatteryLow(Double alarmBatteryLow) {
        this.alarmBatteryLow = alarmBatteryLow;
    }

    public Integer getTestPassedIntervalUpper() {
        return testPassedIntervalUpper;
    }

    public void setTestPassedIntervalUpper(Integer testPassedIntervalUpper) {
        this.testPassedIntervalUpper = testPassedIntervalUpper;
    }

    public Integer getTestPassedIntervalLower() {
        return testPassedIntervalLower;
    }

    public void setTestPassedIntervalLower(Integer testPassedIntervalLower) {
        this.testPassedIntervalLower = testPassedIntervalLower;
    }

    public Integer getEcgPatientModel() {
        return ecgPatientModel;
    }

    public void setEcgPatientModel(Integer ecgPatientModel) {
        this.ecgPatientModel = ecgPatientModel;
    }

    public Integer getEcgLeadModel() {
        return ecgLeadModel;
    }

    public void setEcgLeadModel(Integer ecgLeadModel) {
        this.ecgLeadModel = ecgLeadModel;
    }

    public Integer getEcgLeadChannel() {
        return ecgLeadChannel;
    }

    public void setEcgLeadChannel(Integer ecgLeadChannel) {
        this.ecgLeadChannel = ecgLeadChannel;
    }
}
