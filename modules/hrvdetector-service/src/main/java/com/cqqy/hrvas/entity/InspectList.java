package com.cqqy.hrvas.entity;

import com.cqqy.hrvas.util.LocalDateTimeUtil;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "inspect_list")
public class InspectList {

    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long deptId;

    private String deptName;

    private Long doctorId;

    private String doctorName;

    private Integer sync;

    private Integer inspectListType;

    private String inspectItem;

    private Integer inspectItemHrv;

    private Integer hrvDuration;

    private Long patientId;

    private String patientName;

    private LocalDateTime detectionTime;

    private String detectionStaff;

    private Integer uploadStatus;

    private Integer completeStatus;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer localTaskType;

    private Integer taskType;

    private Integer deleted;

    private Integer age;

    private Integer gender;

    private String cardIdLastSix;

    private String documentNo;

    private String outpatientNo;

    private String nameInitial;

    private Long analysisId;

    @PrePersist
    void onCreate() {
        this.createTime = LocalDateTimeUtil.now();
        this.uploadStatus = 0;
        this.completeStatus = 0;
        this.deleted = 0;
    }

    @PreUpdate
    void onUpdate() {
        this.updateTime = LocalDateTimeUtil.now();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Long getDoctorId() {
        return doctorId;
    }

    public void setDoctorId(Long doctorId) {
        this.doctorId = doctorId;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public Integer getSync() {
        return sync;
    }

    public void setSync(Integer sync) {
        this.sync = sync;
    }

    public String getInspectItem() {
        return inspectItem;
    }

    public void setInspectItem(String inspectItem) {
        this.inspectItem = inspectItem;
    }

    public Integer getHrvDuration() {
        return hrvDuration;
    }

    public void setHrvDuration(Integer hrvDuration) {
        this.hrvDuration = hrvDuration;
    }

    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public LocalDateTime getDetectionTime() {
        return detectionTime;
    }

    public void setDetectionTime(LocalDateTime detectionTime) {
        this.detectionTime = detectionTime;
    }

    public String getDetectionStaff() {
        return detectionStaff;
    }

    public void setDetectionStaff(String detectionStaff) {
        this.detectionStaff = detectionStaff;
    }

    public Integer getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(Integer uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public Integer getCompleteStatus() {
        return completeStatus;
    }

    public void setCompleteStatus(Integer completeStatus) {
        this.completeStatus = completeStatus;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getLocalTaskType() {
        return localTaskType;
    }

    public void setLocalTaskType(Integer localTaskType) {
        this.localTaskType = localTaskType;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getInspectListType() {
        return inspectListType;
    }

    public void setInspectListType(Integer inspectListType) {
        this.inspectListType = inspectListType;
    }

    public Integer getInspectItemHrv() {
        return inspectItemHrv;
    }

    public void setInspectItemHrv(Integer inspectItemHrv) {
        this.inspectItemHrv = inspectItemHrv;
    }

    public String getCardIdLastSix() {
        return cardIdLastSix;
    }

    public void setCardIdLastSix(String cardIdLastSix) {
        this.cardIdLastSix = cardIdLastSix;
    }

    public String getDocumentNo() {
        return documentNo;
    }

    public void setDocumentNo(String documentNo) {
        this.documentNo = documentNo;
    }

    public String getOutpatientNo() {
        return outpatientNo;
    }

    public void setOutpatientNo(String outpatientNo) {
        this.outpatientNo = outpatientNo;
    }

    public String getNameInitial() {
        return nameInitial;
    }

    public void setNameInitial(String nameInitial) {
        this.nameInitial = nameInitial;
    }

    public Long getAnalysisId() {
        return analysisId;
    }

    public void setAnalysisId(Long analysisId) {
        this.analysisId = analysisId;
    }
}
