package com.cqqy.hrvas.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "paradigm_file")
public class ParadigmFile {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String stroopType;

    private String fileType;

    private String path;

    private String describe;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStroopType() {
        return stroopType;
    }

    public void setStroopType(String stroopType) {
        this.stroopType = stroopType;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }
}
