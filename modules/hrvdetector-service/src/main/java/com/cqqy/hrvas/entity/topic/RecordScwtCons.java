package com.cqqy.hrvas.entity.topic;

import com.cqqy.hrvas.util.LocalDateTimeUtil;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "record_scwt_cons")
public class RecordScwtCons {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long inspectListId;

    private Integer topicSort;

    private Integer topicGraphic;

    private String topicCharacter;

    private Integer correctAnswer;

    private Integer actualAnswer;

    private Integer isCorrect;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer deleted;

    @PrePersist
    void onCreate() {
        this.createTime = LocalDateTimeUtil.now();
        this.deleted = 0;
    }

    @PreUpdate
    void onUpdate() {
        this.updateTime = LocalDateTimeUtil.now();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getInspectListId() {
        return inspectListId;
    }

    public void setInspectListId(Long inspectListId) {
        this.inspectListId = inspectListId;
    }

    public Integer getTopicSort() {
        return topicSort;
    }

    public void setTopicSort(Integer topicSort) {
        this.topicSort = topicSort;
    }

    public Integer getTopicGraphic() {
        return topicGraphic;
    }

    public void setTopicGraphic(Integer topicGraphic) {
        this.topicGraphic = topicGraphic;
    }

    public String getTopicCharacter() {
        return topicCharacter;
    }

    public void setTopicCharacter(String topicCharacter) {
        this.topicCharacter = topicCharacter;
    }

    public Integer getCorrectAnswer() {
        return correctAnswer;
    }

    public void setCorrectAnswer(Integer correctAnswer) {
        this.correctAnswer = correctAnswer;
    }

    public Integer getActualAnswer() {
        return actualAnswer;
    }

    public void setActualAnswer(Integer actualAnswer) {
        this.actualAnswer = actualAnswer;
    }

    public Integer getIsCorrect() {
        return isCorrect;
    }

    public void setIsCorrect(Integer isCorrect) {
        this.isCorrect = isCorrect;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
}
