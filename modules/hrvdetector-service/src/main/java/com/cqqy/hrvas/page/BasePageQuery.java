package com.cqqy.hrvas.page;

import io.swagger.v3.oas.annotations.media.Schema;

public class BasePageQuery {

    @Schema(description = "页码,默认为1", example = "1")
    private Integer pageIndex = 1;

    @Schema(description = "页大小,默认为10", example = "10")
    private Integer pageSize = 10;

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
