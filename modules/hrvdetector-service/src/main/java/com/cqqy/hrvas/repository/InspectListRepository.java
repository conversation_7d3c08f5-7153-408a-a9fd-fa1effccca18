package com.cqqy.hrvas.repository;

import com.cqqy.hrvas.entity.InspectList;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface InspectListRepository extends JpaRepository<InspectList, Long> {

    List<InspectList> findAllByDeleted(Integer deleted);

    InspectList findInspectListByIdAndDeleted(Long id, Integer deleted);

    Page<InspectList> findAllByTaskTypeAndDeleted(Pageable pageable, Integer taskType, Integer deleted);

    Page<InspectList> findAllByUploadStatusAndDeleted(Pageable pageable, Integer uploadStatus, Integer deleted);

    @Query("select e from InspectList e where e.patientName like CONCAT(:prefix1, '%') or e.outpatientNo like CONCAT(:prefix2, '%') or e.cardIdLastSix like CONCAT(:prefix3, '%') or e.nameInitial like CONCAT(:prefix4, '%') and e.deleted = 0")
    List<InspectList> findByPrefixes(@Param("prefix1") String prefix1,
                                     @Param("prefix2") String prefix2,
                                     @Param("prefix3") String prefix3,
                                     @Param("prefix4") String prefix4);
}
