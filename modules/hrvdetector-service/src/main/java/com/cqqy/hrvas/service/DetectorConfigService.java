package com.cqqy.hrvas.service;

import cn.hutool.core.bean.BeanUtil;
import com.cqqy.hrvas.cache.DetectorConfigCache;
import com.cqqy.hrvas.comm.service.CommService;
import com.cqqy.hrvas.dto.config.DetectorAlarmConfigDTO;
import com.cqqy.hrvas.dto.config.DetectorEcgConfigDTO;
import com.cqqy.hrvas.dto.config.DetectorNetworkConfigDTO;
import com.cqqy.hrvas.dto.config.DetectorTestConfigDTO;
import com.cqqy.hrvas.entity.DetectorConfig;
import com.cqqy.hrvas.repository.DetectorConfigRepository;
import com.cqqy.hrvas.util.CacheUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.cqqy.hrvas.constant.CacheConstant.CACHE_NAME;
import static com.cqqy.hrvas.constant.CacheConstant.CONFIG_KEY;

@Service
public class DetectorConfigService {

    private final DetectorConfigRepository detectorConfigRepository;

    private final CommService commService;

    private final CacheUtil cacheUtil;

    @Autowired
    public DetectorConfigService(DetectorConfigRepository detectorConfigRepository, CommService commService, CacheUtil cacheUtil) {
        this.detectorConfigRepository = detectorConfigRepository;
        this.commService = commService;
        this.cacheUtil = cacheUtil;
    }


    public DetectorConfig getDetectorConfig() {
        return detectorConfigRepository.findDetectorConfigById(1L).get();
    }

    public void updateNetworkConfig(DetectorNetworkConfigDTO dto) {
        DetectorConfig detectorConfig = getDetectorConfig();
        BeanUtil.copyProperties(dto, detectorConfig);
        detectorConfigRepository.save(detectorConfig);
    }

    public void updateEcgConfig(DetectorEcgConfigDTO dto) {
        DetectorConfig detectorConfig = getDetectorConfig();
        BeanUtil.copyProperties(dto, detectorConfig);
        detectorConfigRepository.save(detectorConfig);
        commService.executeCommand(dto.getEcgStTemplateIso(), dto.getEcgStTemplateSt(), dto.getEcgNotchMode(),
                dto.getEcgArrhythmiaChannel(), dto.getEcgPowerFiltering(), dto.getEcgGain(),
                dto.getEcgPatientModel(), dto.getEcgLeadModel(), dto.getEcgLeadChannel());
    }

    public void updateAlarmConfig(DetectorAlarmConfigDTO dto) {
        DetectorConfig detectorConfig = getDetectorConfig();
        BeanUtil.copyProperties(dto, detectorConfig);
        detectorConfigRepository.save(detectorConfig);
        DetectorConfigCache cache = BeanUtil.copyProperties(detectorConfig, DetectorConfigCache.class);
        cacheUtil.putToCache(CACHE_NAME, CONFIG_KEY, cache);
    }

    public void updateTestConfig(DetectorTestConfigDTO dto) {
        DetectorConfig detectorConfig = getDetectorConfig();
        BeanUtil.copyProperties(dto, detectorConfig);
        detectorConfigRepository.save(detectorConfig);
    }
}
