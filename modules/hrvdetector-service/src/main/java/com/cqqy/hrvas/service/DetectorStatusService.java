package com.cqqy.hrvas.service;

import cn.hutool.core.bean.BeanUtil;
import com.cqqy.hrvas.dto.config.DetectorStatusDTO;
import com.cqqy.hrvas.entity.DetectorStatus;
import com.cqqy.hrvas.exception.BizException;
import com.cqqy.hrvas.repository.DetectorStatusRepository;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class DetectorStatusService {

    private final DetectorStatusRepository detectorStatusRepository;


    public DetectorStatusService(DetectorStatusRepository detectorStatusRepository) {
        this.detectorStatusRepository = detectorStatusRepository;
    }

    public void update(DetectorStatusDTO detectorStatusDTO) {
        DetectorStatus detectorStatus = getDetectorStatus();
        BeanUtil.copyProperties(detectorStatusDTO, detectorStatus);
        detectorStatusRepository.save(detectorStatus);
    }

    public DetectorStatus getDetectorStatus() {
        Optional<DetectorStatus> optionalDetectorStatus = detectorStatusRepository.findDetectorConfigById(1L);
        return optionalDetectorStatus.orElseThrow(() -> new BizException("采集端状态信息不存在"));
    }
}
