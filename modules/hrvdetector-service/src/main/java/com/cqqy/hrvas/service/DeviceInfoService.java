package com.cqqy.hrvas.service;

import com.cqqy.hrvas.dto.config.DeviceInfoDTO;
import com.cqqy.hrvas.entity.DetectorStatus;
import com.cqqy.hrvas.entity.DeviceInfo;
import com.cqqy.hrvas.exception.BizException;
import com.cqqy.hrvas.repository.DeviceInfoRepository;
import com.cqqy.hrvas.vo.config.DeviceInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DeviceInfoService {

    private final DeviceInfoRepository deviceInfoRepository;

    private final DetectorStatusService detectorStatusService;

    @Autowired
    public DeviceInfoService(DeviceInfoRepository deviceInfoRepository, DetectorStatusService detectorStatusService) {
        this.deviceInfoRepository = deviceInfoRepository;
        this.detectorStatusService = detectorStatusService;
    }

    public void update(DeviceInfoDTO deviceInfoDTO) {
        DeviceInfo deviceInfo = findDeviceInfo();
        deviceInfo.setDeviceNo(deviceInfoDTO.getDeviceNo());
        deviceInfo.setDeviceType(deviceInfoDTO.getDeviceType());
        deviceInfo.setDeviceMac(deviceInfoDTO.getDeviceMac());
        // TODO 修改设备mac
        deviceInfoRepository.save(deviceInfo);
    }

    public DeviceInfoDTO getDeviceInfo() {
        DeviceInfo deviceInfo = findDeviceInfo();
        return new DeviceInfoDTO(deviceInfo.getDeviceNo(),
                deviceInfo.getDeviceType(),
                deviceInfo.getDeviceMac(),
                deviceInfo.getDeviceIp());
    }

    private DeviceInfo findDeviceInfo() {
        return deviceInfoRepository.findById(1L)
                .orElseThrow(() -> new BizException("设备信息不存在"));
    }

    public DeviceInfoVO getDeviceInfoAndStatus() {
        DeviceInfo deviceInfo = findDeviceInfo();
        DetectorStatus detectorStatus = detectorStatusService.getDetectorStatus();
        return new DeviceInfoVO(deviceInfo.getDeviceNo(),
                deviceInfo.getDeviceType(),
                deviceInfo.getDeviceMac(),
                deviceInfo.getDeviceIp(),
                detectorStatus.getRegistered());
    }

    public void updateDeviceNo(Integer deviceNo) {
        if (deviceNo <= 0) {
            throw new BizException("设备编号只能为正整数");
        }
        DeviceInfo deviceInfo = findDeviceInfo();
        deviceInfo.setDeviceNo(deviceNo);
        deviceInfoRepository.save(deviceInfo);
    }
}
