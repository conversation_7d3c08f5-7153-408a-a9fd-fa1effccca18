package com.cqqy.hrvas.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.cqqy.hrvas.convert.InspectListMapper;
import com.cqqy.hrvas.dto.business.InspectListDTO;
import com.cqqy.hrvas.entity.InspectList;
import com.cqqy.hrvas.enums.HrvMode;
import com.cqqy.hrvas.exception.BizException;
import com.cqqy.hrvas.exception.LoginException;
import com.cqqy.hrvas.page.ManagePageQuery;
import com.cqqy.hrvas.repository.InspectListRepository;
import com.cqqy.hrvas.util.LocalDateTimeUtil;
import com.cqqy.hrvas.util.SessionUtil;
import com.cqqy.hrvas.vo.PageVo;
import com.cqqy.hrvas.vo.business.InspectListVo;
import com.cqqy.hrvas.vo.login.LoginVo;
import com.cqqy.hrvas.websocket.api.biz.CommonBiz;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Optional;

@Service
public class InspectListService {

    private final InspectListRepository inspectListRepository;

    private final WebClient webClient;

    private final DeviceInfoService deviceInfoService;

    private final SessionUtil sessionUtil;

    private final CommonBiz commonBiz;

    @Autowired
    public InspectListService(InspectListRepository inspectListRepository, WebClient webClient, DeviceInfoService deviceInfoService, SessionUtil sessionUtil, CommonBiz commonBiz) {
        this.inspectListRepository = inspectListRepository;
        this.webClient = webClient;
        this.deviceInfoService = deviceInfoService;
        this.sessionUtil = sessionUtil;
        this.commonBiz = commonBiz;
    }

    // 添加离线任务（平台端调用）
    public void addOfflineTask(InspectList inspectList) {
        inspectListRepository.save(inspectList);
    }

    // 离线任务列表
    public List<InspectListVo> findOfflineTaskList(String prefix) {
        List<InspectList> inspectLists;
        if (ObjectUtil.isEmpty(prefix)) {
            inspectLists = inspectListRepository.findAllByDeleted(0);
        } else {
            inspectLists = inspectListRepository.findByPrefixes(prefix, prefix, prefix, prefix);
        }
        return InspectListMapper.INSTANCE.listToVoList(inspectLists);
    }

    // 离线任务详情
    public InspectListVo findOfflineTaskDetails(Long id) {
        InspectList inspectList = inspectListRepository.findInspectListByIdAndDeleted(id, 0);
        return InspectListMapper.INSTANCE.entityToVo(inspectList);
    }

    // 离线任务终止（删除）
    public void taskEnd(Long id) {
        Optional<InspectList> optional = inspectListRepository.findById(id);
        InspectList inspectList = optional.orElseThrow(() -> new BizException("任务不存在"));
        inspectList.setCompleteStatus(2);
        inspectList.setDeleted(1);
        inspectListRepository.save(inspectList);
    }

    // 临时任务添加
    public Long addInspectList(InspectListDTO inspectListDTO) {
        InspectList inspectList = BeanUtil.copyProperties(inspectListDTO, InspectList.class);

        inspectList.setId(System.currentTimeMillis() + (long) (Math.random() * 1000));
        inspectList.setSync(1);
        inspectList.setInspectItem(HrvMode.getDesc(inspectListDTO.getInspectItemHrv()));
        inspectList.setInspectItemHrv(inspectListDTO.getInspectItemHrv());
        inspectList.setInspectListType("HRV");
        inspectList.setDetectionTime(LocalDateTimeUtil.now());
        inspectList.setDetectionStaff(inspectList.getDoctorName());
        inspectList.setTaskType(2);
        inspectList.setLocalTaskType(1);
        inspectListRepository.save(inspectList);
        return inspectList.getId();
    }


    // 数据统计-管理端统计（平台端任务+离线任务）
    public PageVo<InspectListVo> manageStatistics(ManagePageQuery managePageQuery) {
        Integer pageIndex = managePageQuery.getPageIndex();
        Integer pageSize = managePageQuery.getPageSize();
        Pageable pageable = PageRequest.of(pageIndex - 1, pageSize);
        Page<InspectList> inspectListPage = inspectListRepository.findAllByLocalTaskTypeOrLocalTaskTypeAndDeleted(pageable, 0, 2, 0);
        List<InspectListVo> inspectListVoList = InspectListMapper.INSTANCE.listToVoList(inspectListPage.getContent());
        return new PageVo<>(inspectListPage, inspectListVoList);
    }

    // 数据统计-采集端统计（临时任务）
    public PageVo<InspectListVo> detectorStatistics(ManagePageQuery managePageQuery) {
        Integer pageIndex = managePageQuery.getPageIndex();
        Integer pageSize = managePageQuery.getPageSize();
        Pageable pageable = PageRequest.of(pageIndex - 1, pageSize);
        Page<InspectList> inspectListPage = inspectListRepository.findAllByLocalTaskTypeAndDeleted(pageable, 1, 0);
        List<InspectListVo> inspectListVoList = InspectListMapper.INSTANCE.listToVoList(inspectListPage.getContent());
        return new PageVo<>(inspectListPage, inspectListVoList);
    }

    // 数据统计-未上报统计（所有未上报任务）
    public PageVo<InspectListVo> notReportStatistics(ManagePageQuery managePageQuery) {
        Integer pageIndex = managePageQuery.getPageIndex();
        Integer pageSize = managePageQuery.getPageSize();
        Pageable pageable = PageRequest.of(pageIndex - 1, pageSize);
        Page<InspectList> inspectListPage = inspectListRepository.findAllByUploadStatusOrUploadStatusAndDeleted(pageable, 0, 2,0);
        List<InspectListVo> inspectListVoList = InspectListMapper.INSTANCE.listToVoList(inspectListPage.getContent());
        return new PageVo<>(inspectListPage, inspectListVoList);
    }

    // 上传时查询
    public InspectList getInspectListById(Long id) {
        Optional<InspectList> inspectListOptional = Optional.ofNullable(inspectListRepository.findInspectListByIdAndDeleted(id, 0));
        return inspectListOptional.orElseThrow(() -> new BizException("任务不存在"));
    }

    // 上传回复，修改上传状态
    public void updateUploadStatus(Long id, Integer status) {
        InspectList inspectList = getInspectListById(id);
        inspectList.setUploadStatus(status);
        inspectListRepository.save(inspectList);
    }

    // 在线任务保存到本地
    public void addOnlineTask(InspectList inspectList) {
        HttpSession session = sessionUtil.getSession();
        LoginVo loginVo = (LoginVo) session.getAttribute("loginVo");
        Assert.notNull(loginVo, () -> new LoginException("获取用户登录信息失败"));

        inspectList.setDetectionTime(LocalDateTimeUtil.now());
        inspectList.setDetectionStaff(loginVo.getUsername());
        inspectList.setUploadStatus(0);
        inspectList.setLocalTaskType(2);
        inspectList.setDeleted(0);
        inspectListRepository.save(inspectList);
    }
}
