package com.cqqy.hrvas.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.cqqy.hrvas.convert.topic.TopicMapper;
import com.cqqy.hrvas.dto.business.ParadigmFileDTO;
import com.cqqy.hrvas.dto.business.topic.ConsistencyDTO;
import com.cqqy.hrvas.dto.business.topic.MatTopicDTO;
import com.cqqy.hrvas.dto.business.topic.NonConsistencyDTO;
import com.cqqy.hrvas.dto.business.topic.ParadigmEventDTO;
import com.cqqy.hrvas.entity.ParadigmFile;
import com.cqqy.hrvas.exception.BizException;
import com.cqqy.hrvas.questionbank.TopicUtil;
import com.cqqy.hrvas.questionbank.scwt.Consistency;
import com.cqqy.hrvas.questionbank.scwt.NonConsistency;
import com.cqqy.hrvas.repository.ParadigmFileRepository;
import com.cqqy.hrvas.repository.RecordMatRepository;
import com.cqqy.hrvas.repository.RecordScwtConsRepository;
import com.cqqy.hrvas.repository.RecordScwtNonConsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ParadigmFileService {

    private final ParadigmFileRepository paradigmFileRepository;

    private final RecordMatRepository recordMatRepository;

    private final RecordScwtConsRepository recordScwtConsRepository;

    private final RecordScwtNonConsRepository recordScwtNonConsRepository;

    @Value("${hrvdetector.storage.filepath}")
    private String filepath;

    @Autowired
    public ParadigmFileService(ParadigmFileRepository paradigmFileRepository, RecordMatRepository recordMatRepository, RecordScwtConsRepository recordScwtConsRepository, RecordScwtNonConsRepository recordScwtNonConsRepository) {
        this.paradigmFileRepository = paradigmFileRepository;
        this.recordMatRepository = recordMatRepository;
        this.recordScwtConsRepository = recordScwtConsRepository;
        this.recordScwtNonConsRepository = recordScwtNonConsRepository;
    }

    // 获取图片、音乐资源
    public ParadigmFileDTO getParadigmFile(String stroopType) {
        List<ParadigmFile> sources = paradigmFileRepository.querySourcesByStroopType(stroopType);
        Assert.notNull(sources, () -> new BizException(""));

        ParadigmFileDTO sourceDTO = new ParadigmFileDTO();
        sourceDTO.setStroopType(stroopType);
        sourceDTO.setMusicList(sources.stream()
                .filter(source -> "music".equals(source.getFileType()))
                .map(ParadigmFile::getPath)
                .collect(Collectors.toList()));
        sourceDTO.setPictureList(sources.stream()
                .filter(source -> "picture".equals(source.getFileType()))
                .map(ParadigmFile::getPath)
                .collect(Collectors.toList()));
        return sourceDTO;
    }

    // 一致性测试题目
    public List<ConsistencyDTO> getScwtConsistency() {
        List<Consistency> consistencyList = TopicUtil.getConsistency(20);

        List<ConsistencyDTO> consistencyDTOList = new ArrayList<>();
        for (int i = 0; i < consistencyList.size(); i++) {
            ConsistencyDTO consistencyDTO = TopicMapper.INSTANCE.consToDto(consistencyList.get(i));
            consistencyDTO.setTopicSort(i);
            consistencyDTOList.add(consistencyDTO);
        }
        return consistencyDTOList;
    }

    // 非一致性测试题目
    public List<NonConsistencyDTO> getScwtNonConsistency() {
        List<NonConsistency> nonConsistencyList = TopicUtil.getNonConsistency(20);

        List<NonConsistencyDTO> nonConsistencyDTOList = new ArrayList<>();
        for (int i = 0; i < nonConsistencyList.size(); i++) {
            NonConsistencyDTO nonConsistencyDTO = TopicMapper.INSTANCE.nonConsToDto(nonConsistencyList.get(i));
            nonConsistencyDTO.setTopicSort(i);
            nonConsistencyDTOList.add(nonConsistencyDTO);
        }
        return nonConsistencyDTOList;
    }

    public List<MatTopicDTO> getMatES() {
        List<String> es = new ArrayList<>();
        es.addAll(TopicUtil.getESAdd(1, 9, 1, 9, 10, 99, 5));
        es.addAll(TopicUtil.getESSub(10, 99, 1, 9, 10, 99, 4));
        es.addAll(TopicUtil.getESMul(1, 9, 1, 9, 10, 99, 4));
        es.addAll(TopicUtil.getESDiv(10, 99, 1, 9, 1, 9, 4));
        return format(es);
    }

    public List<MatTopicDTO> getMatDS() {
        List<String> ds = new ArrayList<>();
        ds.addAll(TopicUtil.getDSAdd(100, 999,100, 999, 100, 999, 1));
        ds.addAll(TopicUtil.getDSSub(100, 999,100, 999, 100, 999,  1));
        ds.addAll(TopicUtil.getDSMul(10, 99, 10, 99, 100, 999, 1));
        ds.addAll(TopicUtil.getDSDiv(100, 999, 1, 9, 100, 999, 1));
        return format(ds);
    }

    private List<MatTopicDTO> format(List<String> list) {
        List<MatTopicDTO> result = new ArrayList<>();

        Collections.shuffle(list);
        for (int i = 0; i < list.size(); i++) {
            MatTopicDTO topicDTO = new MatTopicDTO();
            String[] split = list.get(i).split("=");
            topicDTO.setTopicSort(i);
            topicDTO.setTopic(split[0].trim());
            topicDTO.setCorrectAnswer(split[1].trim());
            result.add(topicDTO);
        }
        return result;
    }

    public void commitEvent(Long inspectListId, String taskType, List<ParadigmEventDTO> paradigmEventDTOList) throws IOException {
        String fileName = "paradigm_data_" + inspectListId + ".json";
        String path = filepath + fileName;
        FileUtil.mkParentDirs(path);
        File file = FileUtil.touch(path);
        JSONObject data = new JSONObject();
        data.put("task_id", inspectListId);
        data.put("task_type", taskType);
        JSONArray eventNode = new JSONArray();
        paradigmEventDTOList.forEach(paradigmEventDTO -> {
            JSONObject node = new JSONObject();
            if (paradigmEventDTO.getEventName().equals("topic")) {
                node.put("timestamp", paradigmEventDTO.getTimestamp());
                node.put("event_name", paradigmEventDTO.getEventName());
                switch (paradigmEventDTO.getTopicType()) {
                    case 0 -> {
                        MatTopicDTO matTopicDTO = paradigmEventDTO.getMatTopicDTO();
                        node.put("topic", matTopicDTO.getTopic());
                        node.put("correct_answer", matTopicDTO.getCorrectAnswer());
                        node.put("actual_answer", matTopicDTO.getActualAnswer());
                    }
                    case 1 -> {
                        ConsistencyDTO consistencyDTO = paradigmEventDTO.getConsistencyDTO();
                        node.put("topic_graphic", consistencyDTO.getTopicGraphic());
                        node.put("topic_character", consistencyDTO.getTopicCharacter());
                        node.put("correct_answer", consistencyDTO.getCorrectAnswer());
                        node.put("actual_answer", consistencyDTO.getActualAnswer());
                    }
                    case 2 -> {
                        NonConsistencyDTO nonConsistencyDTO = paradigmEventDTO.getNonConsistencyDTO();
                        node.put("topic_character", nonConsistencyDTO.getTopicCharacter());
                        node.put("topic_character_color", nonConsistencyDTO.getTopicCharacterColor());
                        node.put("topic_graphic", nonConsistencyDTO.getTopicGraphic());
                        node.put("correct_answer", nonConsistencyDTO.getCorrectAnswer());
                        node.put("actual_answer", nonConsistencyDTO.getActualAnswer());
                    }
                }
            } else {
                node.put("timestamp", paradigmEventDTO.getTimestamp());
                node.put("event_name", paradigmEventDTO.getEventName());
            }
            eventNode.add(node);
        });
        data.put("event_node", eventNode);
        FileUtil.writeString(data.toJSONString(), file, StandardCharsets.UTF_8);
    }
}
