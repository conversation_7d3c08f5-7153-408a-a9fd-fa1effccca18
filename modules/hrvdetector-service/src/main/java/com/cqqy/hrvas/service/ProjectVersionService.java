package com.cqqy.hrvas.service;

import com.cqqy.hrvas.dto.ProjectVersionDTO;
import com.cqqy.hrvas.entity.ProjectVersion;
import com.cqqy.hrvas.repository.ProjectVersionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ProjectVersionService {
    private final ProjectVersionRepository projectVersionRepository;

    @Autowired
    public ProjectVersionService(ProjectVersionRepository projectVersionRepository) {
        this.projectVersionRepository = projectVersionRepository;
    }

    public ProjectVersionDTO getCurrentVersion() {
        ProjectVersion currentVersion = projectVersionRepository.findByIsActiveTrue()
                .orElseThrow(() -> new RuntimeException("No active version found"));

        return new ProjectVersionDTO(
                currentVersion.getId(),
                currentVersion.getVersionNumber(),
                currentVersion.getDescription(),
                currentVersion.getReleaseDate(),
                currentVersion.getIsActive()
        );
    }
}