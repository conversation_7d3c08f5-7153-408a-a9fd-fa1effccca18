package com.cqqy.hrvas.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.cqqy.hrvas.dto.business.InspectListDTO;
import com.cqqy.hrvas.dto.business.TempPatientInfoDTO;
import com.cqqy.hrvas.entity.TempPatientInfo;
import com.cqqy.hrvas.exception.BizException;
import com.cqqy.hrvas.exception.LoginException;
import com.cqqy.hrvas.repository.TempPatientInfoRepository;
import com.cqqy.hrvas.util.SessionUtil;
import com.cqqy.hrvas.vo.login.LoginVo;
import jakarta.servlet.http.HttpSession;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class TempPatientInfoService {

    private final TempPatientInfoRepository tempPatientInfoRepository;

    private final InspectListService inspectListService;

    private final SessionUtil sessionUtil;

    @Autowired
    public TempPatientInfoService(TempPatientInfoRepository tempPatientInfoRepository , InspectListService inspectListService, SessionUtil sessionUtil) {
        this.tempPatientInfoRepository = tempPatientInfoRepository;
        this.inspectListService = inspectListService;
        this.sessionUtil = sessionUtil;
    }

    @Transactional
    public Long addTempPatientInfo(TempPatientInfoDTO tempPatientInfoDTO) {
        TempPatientInfo tempPatientInfo = new TempPatientInfo();
        BeanUtil.copyProperties(tempPatientInfoDTO, tempPatientInfo);

        HttpSession session = sessionUtil.getSession();
        LoginVo loginVo = (LoginVo) session.getAttribute("loginVo");
        Assert.notNull(loginVo, () -> new LoginException("获取用户登录信息失败"));

        Long deptId = loginVo.getDeptId();
        String deptName = loginVo.getDeptName();
        tempPatientInfo.setDeptId(deptId);
        tempPatientInfo.setDeptName(deptName);
        tempPatientInfoRepository.save(tempPatientInfo);

        // 开单
        InspectListDTO inspectListDTO = tempPatientInfoDTO.getInspectListDTO();
        inspectListDTO.setPatientId(tempPatientInfo.getId());
        inspectListDTO.setPatientName(tempPatientInfo.getName());
        inspectListDTO.setAge(tempPatientInfo.getAge());
        inspectListDTO.setGender(tempPatientInfo.getGender());
        inspectListDTO.setDeptId(deptId);
        inspectListDTO.setDeptName(deptName);
        inspectListDTO.setDoctorId(loginVo.getUserId());
        inspectListDTO.setDoctorName(loginVo.getUsername());
        return inspectListService.addInspectList(inspectListDTO);
    }

    public TempPatientInfo getTempPatientInfoById(Long id) {
        Optional<TempPatientInfo> tempPatientInfoOptional = Optional.ofNullable(tempPatientInfoRepository.findTempPatientInfoByIdAndDeleted(id, 0));
        return tempPatientInfoOptional.orElseThrow(() -> new BizException("患者不存在"));
    }
}
