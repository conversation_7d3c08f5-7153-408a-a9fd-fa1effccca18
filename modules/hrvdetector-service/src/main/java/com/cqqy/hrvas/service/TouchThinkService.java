package com.cqqy.hrvas.service;

import cn.hutool.core.util.RuntimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TouchThinkService {

    /**
     * 音量 范围： 3--255 这里设置的值越大，音量越小
     * i2cset -y -f 0 0x20 0x31 0x22 左声道 34
     * i2cset -y -f 0 0x20 0x32 0x22 右声道 34
     */
    public void audioMute() {
        String cmd = "bash /usr/local/software/pad_mute.sh";
        Process process = RuntimeUtil.exec(cmd);
        log.info("executed shell : {}, process : {}", cmd, process.info());
        resultHandle(process);
    }

    public void audioUnmute() {
        String cmd = "bash /usr/local/software/pad_unmute.sh";
        Process process = RuntimeUtil.exec(cmd);
        log.info("executed shell : {}, process : {}", cmd, process.info());
        resultHandle(process);
    }

    /**
     * 1： 打开休眠功能：
     * 0： 关闭休眠功能；
     */
    public void backlightSleep() {
        String cmd = "bash /usr/local/software/pad_sleep.sh";
        Process process = RuntimeUtil.exec(cmd);
        log.info("executed shell : {}, process : {}", cmd, process.info());
        resultHandle(process);
    }

    public void backlightWakeup() {
        String cmd = "bash /usr/local/software/pad_wakeup.sh";
        Process process = RuntimeUtil.exec(cmd);
        log.info("executed shell : {}, process : {}", cmd, process.info());
        resultHandle(process);
    }

    /**
     * 关机
     */
    public void shutdown() {
        String cmd = "bash /usr/local/software/shutdown.sh";
        Process process = RuntimeUtil.exec(cmd);
        log.info("executed shell : {}, process : {}", cmd, process.info());
        resultHandle(process);
    }

    private void resultHandle(Process process) {
        // 获取命令输出流
        String output = RuntimeUtil.getResult(process); // 获取完整输出
        log.info("命令输出：{}", output);

        // 获取命令退出码（0 表示成功）
        int exitCode = process.exitValue();
        log.info("退出码：{}", exitCode);
    }
}
