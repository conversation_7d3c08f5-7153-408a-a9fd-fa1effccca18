package com.cqqy.hrvas.service.algorithm;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.cqqy.hrvas.exception.BizException;
import com.cqqy.hrvas.websocket.api.biz.CommonBiz;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Service
public class HrvAnalysisService {



    @Value("${algorithm.uri}")
    private String uri;

    private final CommonBiz commonBiz;

    public HrvAnalysisService(CommonBiz commonBiz) {
        this.commonBiz = commonBiz;
    }


    public JSONObject createHrvAnalysis(String subjectId, Integer duration, float samplingRate,
                                  String deviceInfo, Resource ecgFile) {
        MultipartBodyBuilder builder = new MultipartBodyBuilder();
        builder.part("subject_id", subjectId);
        builder.part("duration", duration);
        builder.part("sampling_rate", samplingRate);
        builder.part("device_info", deviceInfo);
        builder.part("ecg_file", ecgFile);
        MultiValueMap<String, HttpEntity<?>> body = builder.build();

        WebClient webClient = WebClient.builder().build();
        Mono<JSONObject> mono = webClient.post()
                .uri(commonBiz.completeAnalysisUrl())
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .body(BodyInserters.fromMultipartData(body))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<JSONObject>() {
                })
                .onErrorResume(throwable -> Mono.error(new BizException(StrUtil.format("请求算法服务异常:{}", throwable.getMessage()))));

        return mono.block();
    }

}
