package com.cqqy.hrvas.service.task;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.cqqy.hrvas.comm.service.EarlyWarningService;
import com.cqqy.hrvas.comm.service.waveform.WaveformService;
import com.cqqy.hrvas.exception.BizException;
import com.cqqy.hrvas.util.CacheUtil;
import com.cqqy.hrvas.websocket.api.biz.CommonBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.io.IOException;

import static com.cqqy.hrvas.constant.CacheConstant.CACHE_NAME;
import static com.cqqy.hrvas.constant.CacheConstant.PATIENT_TYPE_KEY;
import static com.cqqy.hrvas.constant.UrlConstant.*;

@Slf4j
@Service
public class TaskService {

    private final WaveformService waveformService;

    private final EarlyWarningService earlyWarningService;

    private final CacheUtil cacheUtil;

    private final WebClient webClient;

    private final CommonBiz commonBiz;

    public TaskService(WaveformService waveformService, EarlyWarningService earlyWarningService, CacheUtil cacheUtil, WebClient webClient, CommonBiz commonBiz) {
        this.waveformService = waveformService;
        this.earlyWarningService = earlyWarningService;
        this.cacheUtil = cacheUtil;
        this.webClient = webClient;
        this.commonBiz = commonBiz;
    }


    public void taskStart(Long id) {
        Boolean result = connectServer(id, TASK_START);
        if (Boolean.TRUE.equals(result)) {
            log.info("任务{}开始，状态修改成功", id);
        } else {
            log.error("任务{}开始，状态修改失败", id);
            throw new BizException(StrUtil.format("任务{}开始，状态修改失败", id));
        }
    }

    public void taskExit(Long id) {
        Boolean result = connectServer(id, TASK_EXIT);
        if (Boolean.TRUE.equals(result)) {
            log.info("任务{}退出，状态修改成功", id);
        } else {
            log.error("任务{}退出，状态修改失败", id);
            throw new BizException(StrUtil.format("任务{}退出，状态修改失败", id));
        }
    }

    public void taskEnd(Long id) {
        Boolean result = connectServer(id, TASK_END);
        if (Boolean.TRUE.equals(result)) {
            log.info("任务{}完成，状态修改成功", id);
        } else {
            log.error("任务{}完成，状态修改失败", id);
            throw new BizException(StrUtil.format("任务{}完成，状态修改失败", id));
        }
    }

    public void taskDiscontinue(Long id) {
        Boolean result = connectServer(id, TASK_DISCONTINUE);
        if (Boolean.TRUE.equals(result)) {
            log.info("任务{}终止，移除成功", id);
        } else {
            log.error("任务{}终止，移除失败", id);
            throw new BizException(StrUtil.format("任务{}终止，移除失败", id));
        }
    }

    private Boolean connectServer(Long id, String path) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("id", String.valueOf(id));
        Mono<Boolean> mono = webClient.post()
                .uri(commonBiz.completePlatformUrl(path))
                .contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromFormData(formData))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<JSONObject>() {
                })
                .flatMap(result -> {
                    Integer code = result.getInteger("code");
                    return Mono.just(ObjectUtil.isNotEmpty(code) && code == 200);
                });
        return mono.block();
    }

    public void examStart(Long taskId,
                          Integer time,
                          Integer patientType) throws IOException {
        cacheUtil.putToCache(CACHE_NAME, PATIENT_TYPE_KEY, patientType);
        waveformService.startWriting(taskId, time);
        earlyWarningService.start(time);
    }

    public void examStop() {
        waveformService.stopWriting();
        earlyWarningService.stop();
    }
}
