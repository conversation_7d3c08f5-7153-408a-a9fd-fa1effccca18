package com.cqqy.hrvas.vo;

import org.springframework.data.domain.Page;

import java.util.List;

public class PageVo<T> {

    private Integer pageIndex;

    private Integer pageSize;

    private Long total;

    private Integer totalPage;

    private List<T> list;

    public PageVo(Page<?> queryPage, List<T> records){
        this.pageIndex = queryPage.getNumber() + 1;
        this.pageSize = queryPage.getSize();
        this.total = queryPage.getTotalElements();
        this.totalPage = queryPage.getTotalPages();
        this.list = records;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Integer getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(Integer totalPage) {
        this.totalPage = totalPage;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }
}
