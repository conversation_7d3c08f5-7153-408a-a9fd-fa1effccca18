package com.cqqy.hrvas.vo.business;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

public class InspectListVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    @Schema(description = "科室ID")
    private Long deptId;

    @Schema(description = "科室名称")
    private String deptName;

    @Schema(description = "医生id")
    private Long doctorId;

    @Schema(description = "医生姓名")
    private String doctorName;

    @Schema(description = "是否同步，0-同步，1-不同步")
    private Integer sync;

    @Schema(description = "hrv类型")
    private String hrvType;

    @Schema(description = "项目（hrv）")
    private String inspectItem;

    @Schema(description = "HRV采集时长")
    private Integer hrvDuration;

    @Schema(description = "患者id")
    private Long patientId;

    @Schema(description = "患者姓名")
    private String patientName;

    @Schema(description = "检测时间")
    private LocalDateTime detectionTime;

    @Schema(description = "检测人员")
    private String detectionStaff;

    @Schema(description = "上传状态(0-未上传，1-已上传)")
    private Integer uploadStatus;

    @Schema(description = "检查状态，0-未完成，1-已完成")
    @NotNull(message = "检查状态不能为空")
    private Integer completeStatus;

    @Schema(description = "开单时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "任务类型(0-离线任务，1-临时任务)")
    private Integer taskType;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "性别")
    private Integer gender;

    @Schema(description = "清单类型 0-HRV 1-量表 2-同步")
    private Integer inspectListType;

    @Schema(description = "项目（hrv）")
    private Integer inspectItemHrv;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Long getDoctorId() {
        return doctorId;
    }

    public void setDoctorId(Long doctorId) {
        this.doctorId = doctorId;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public Integer getSync() {
        return sync;
    }

    public void setSync(Integer sync) {
        this.sync = sync;
    }

    public String getHrvType() {
        return hrvType;
    }

    public void setHrvType(String hrvType) {
        this.hrvType = hrvType;
    }

    public String getInspectItem() {
        return inspectItem;
    }

    public void setInspectItem(String inspectItem) {
        this.inspectItem = inspectItem;
    }

    public Integer getHrvDuration() {
        return hrvDuration;
    }

    public void setHrvDuration(Integer hrvDuration) {
        this.hrvDuration = hrvDuration;
    }

    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public LocalDateTime getDetectionTime() {
        return detectionTime;
    }

    public void setDetectionTime(LocalDateTime detectionTime) {
        this.detectionTime = detectionTime;
    }

    public String getDetectionStaff() {
        return detectionStaff;
    }

    public void setDetectionStaff(String detectionStaff) {
        this.detectionStaff = detectionStaff;
    }

    public Integer getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(Integer uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public Integer getCompleteStatus() {
        return completeStatus;
    }

    public void setCompleteStatus(Integer completeStatus) {
        this.completeStatus = completeStatus;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getInspectListType() {
        return inspectListType;
    }

    public void setInspectListType(Integer inspectListType) {
        this.inspectListType = inspectListType;
    }

    public Integer getInspectItemHrv() {
        return inspectItemHrv;
    }

    public void setInspectItemHrv(Integer inspectItemHrv) {
        this.inspectItemHrv = inspectItemHrv;
    }
}
