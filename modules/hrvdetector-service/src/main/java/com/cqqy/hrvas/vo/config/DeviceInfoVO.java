package com.cqqy.hrvas.vo.config;

public class DeviceInfoVO {

    private Integer deviceNo;

    private Integer deviceType;

    private String deviceMac;


    private String deviceIp;

    private Integer registered;

    public Integer getDeviceNo() {
        return deviceNo;
    }

    public void setDeviceNo(Integer deviceNo) {
        this.deviceNo = deviceNo;
    }

    public Integer getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceMac() {
        return deviceMac;
    }

    public void setDeviceMac(String deviceMac) {
        this.deviceMac = deviceMac;
    }

    public String getDeviceIp() {
        return deviceIp;
    }

    public void setDeviceIp(String deviceIp) {
        this.deviceIp = deviceIp;
    }

    public Integer getRegistered() {
        return registered;
    }

    public void setRegistered(Integer registered) {
        this.registered = registered;
    }

    public DeviceInfoVO(Integer deviceNo, Integer deviceType, String deviceMac, String deviceIp, Integer registered) {
        this.deviceNo = deviceNo;
        this.deviceType = deviceType;
        this.deviceMac = deviceMac;
        this.deviceIp = deviceIp;
        this.registered = registered;
    }
}
