package com.cqqy.hrvas.vo.topic;

import com.cqqy.hrvas.dto.business.topic.ConsistencyDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "ConsistencyVo")
public class ConsistencyVo {

    private String id;

    private List<ConsistencyDTO> list;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<ConsistencyDTO> getList() {
        return list;
    }

    public void setList(List<ConsistencyDTO> list) {
        this.list = list;
    }
}
