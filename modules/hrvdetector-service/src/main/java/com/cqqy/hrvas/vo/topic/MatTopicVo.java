package com.cqqy.hrvas.vo.topic;

import com.cqqy.hrvas.dto.business.topic.MatTopicDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "MatTopicVo")
public class MatTopicVo {

    private String id;

    private List<MatTopicDTO> list;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<MatTopicDTO> getList() {
        return list;
    }

    public void setList(List<MatTopicDTO> list) {
        this.list = list;
    }
}
