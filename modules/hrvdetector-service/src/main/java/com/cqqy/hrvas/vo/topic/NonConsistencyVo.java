package com.cqqy.hrvas.vo.topic;

import com.cqqy.hrvas.dto.business.topic.NonConsistencyDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "NonConsistencyVo")
public class NonConsistencyVo {

    private String id;

    private List<NonConsistencyDTO> list;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<NonConsistencyDTO> getList() {
        return list;
    }

    public void setList(List<NonConsistencyDTO> list) {
        this.list = list;
    }
}
