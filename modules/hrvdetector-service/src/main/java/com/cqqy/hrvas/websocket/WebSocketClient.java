package com.cqqy.hrvas.websocket;

import cn.hutool.core.util.ObjectUtil;
import com.cqqy.hrvas.websocket.event.MessageReceivedEvent;
import jakarta.websocket.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;
import java.net.InetAddress;
import java.net.URI;
import java.nio.ByteBuffer;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@ClientEndpoint
public class WebSocketClient {

    private Session session;
    private final URI endpointURI;
    private final ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();

    private final ApplicationEventPublisher eventPublisher;

    private final Object lock = new Object();

    // 通过构造函数注入事件发布器
    public WebSocketClient(String serverUrl, ApplicationEventPublisher eventPublisher) {
        this.endpointURI = URI.create(serverUrl);
        this.eventPublisher = eventPublisher;
    }


    /**
     * 建立连接
     */
    public void connect() {
        try {
            InetAddress address = InetAddress.getByName(endpointURI.getHost());
            if (address.isReachable(3_000)) {
                WebSocketContainer container = ContainerProvider.getWebSocketContainer();
                container.connectToServer(this, endpointURI);
            } else {
                scheduleReconnect();
            }
        } catch (Exception e) {
            handleConnectionError(e);
        }
    }

    /**
     * 连接成功回调
     */
    @OnOpen
    public void onOpen(Session session) {
        this.session = session;
        log.info("Connected to server");
        startHeartbeat(); // 启动心跳
    }

    /**
     * 接收消息
     */
    @OnMessage
    public void onMessage(String message) {
        log.info("接收消息: " + message);
        if (ObjectUtil.isEmpty(message) || !message.contains("msg_type")) {
            return;
        }
        eventPublisher.publishEvent(new MessageReceivedEvent(this, message));
    }

    /**
     * 连接关闭
     */
    @OnClose
    public void onClose(CloseReason reason) {
        log.info("连接关闭: " + reason);
        scheduleReconnect(); // 自动重连
    }

    /**
     * 错误处理
     */
    @OnError
    public void onError(Throwable throwable) {
        log.error("Connection error:", throwable);
        handleConnectionError(throwable);
    }

    // --------------- 核心功能 ---------------

    /**
     * 发送消息给服务端
     */
    public synchronized void sendMessage(String message) {
        if (session != null && session.isOpen()) {
            try {
                System.out.println("send: " + message);
                session.getBasicRemote().sendText(message);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    private final Queue<String> messageQueue = new ConcurrentLinkedQueue<>();
    private boolean isSending = false;
    public synchronized void sendEcgMessage(String message) {
//        messageQueue.add(message);
//        if (!isSending) {
//            isSending = true;
//            new Thread(() -> {
//                while (!messageQueue.isEmpty()) {
//                    String msg = messageQueue.poll();
//                    session.getAsyncRemote().sendText(msg);
//                }
//                isSending = false;
//            }).start();
//        }
        synchronized (lock) {
            if (session != null && session.isOpen()) {
                try {
                    session.getAsyncRemote().sendText(message);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 启动心跳（每30秒发送一次）
     */
    private synchronized void startHeartbeat() {
        executor.scheduleAtFixedRate(() -> {
            if (session != null && session.isOpen()) {
                try {
                    session.getBasicRemote().sendPing(ByteBuffer.wrap("ping".getBytes()));
                } catch (IOException e) {
                    handleConnectionError(e);
                }
            }
        }, 30, 30, TimeUnit.SECONDS);
    }

    /**
     * 自动重连机制（5秒后重连）
     */
    private void scheduleReconnect() {
        executor.schedule(this::connect, 5, TimeUnit.SECONDS);
    }

    // --------------- 辅助方法 ---------------

    private void handleConnectionError(Throwable throwable) {
        log.error("Connection failed: " + throwable.getMessage());
        scheduleReconnect();
    }
}
