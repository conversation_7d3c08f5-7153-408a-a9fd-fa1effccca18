package com.cqqy.hrvas.websocket.api;

import com.alibaba.fastjson2.JSONObject;
import com.cqqy.hrvas.dto.business.login.LoginDTO;
import com.cqqy.hrvas.websocket.api.biz.ClientGetServerBiz;
import com.cqqy.hrvas.websocket.api.biz.CommonBiz;
import com.cqqy.hrvas.vo.login.LoginTokenVo;
import com.cqqy.hrvas.vo.login.LoginVo;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import static com.cqqy.hrvas.constant.UrlConstant.*;

/**
 * 采集端 从 平台端 获取信息
 */
@Service
public class ClientGetServerService {

    private final WebClient webClient;

    private final CommonBiz commonBiz;

    private final ClientGetServerBiz clientGetServerBiz;

    public ClientGetServerService(WebClient webClient, CommonBiz commonBiz, ClientGetServerBiz clientGetServerBiz) {
        this.webClient = webClient;
        this.commonBiz = commonBiz;
        this.clientGetServerBiz = clientGetServerBiz;
    }

    public LoginTokenVo login(LoginDTO loginDTO, HttpServletRequest request, HttpServletResponse response) {
        Mono<String> mono = webClient.post()
                .uri(commonBiz.completePlatformUrl(LOGIN_PATH))
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(loginDTO)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<JSONObject>() {
                })
                .flatMap(result -> {
                    System.out.println(result);
                    clientGetServerBiz.check(result);
                    JSONObject data = JSONObject.from(result.get("data"));
                    String token = data.getString("token");
                    return Mono.just(token);
                });
        String token = mono.block();
        LoginTokenVo loginTokenVo = new LoginTokenVo();
        loginTokenVo.setToken(token);
        request.getSession().setAttribute("token", token);
        return loginTokenVo;
    }

    public LoginVo getLoginUserInfo(HttpServletRequest request, HttpServletResponse response) {
        HttpSession session = request.getSession();
        Mono<LoginVo> mono = webClient.post()
                .uri(commonBiz.completePlatformUrl(USER_INFO_PATH))
                .header("Authorization", clientGetServerBiz.getToken(session))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<JSONObject>() {
                })
                .flatMap(result -> {
                    System.out.println(result);
                    clientGetServerBiz.check(result);
                    JSONObject data = JSONObject.from(result.get("data"));
                    LoginVo loginVo = JSONObject.parseObject(String.valueOf(data), LoginVo.class);
                    return Mono.just(loginVo);
                });
        LoginVo loginVo = mono.block();
        session.setAttribute("loginVo", loginVo);
        return loginVo;
    }

    public void logout(HttpServletRequest request, HttpServletResponse response) {
        HttpSession session = request.getSession();
        webClient.post()
                .uri(commonBiz.completePlatformUrl(LOGOUT_PATH))
                .header("Authorization", clientGetServerBiz.getToken(session))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<JSONObject>() {
                })
                .subscribe(
                        clientGetServerBiz::check
                );
        session.invalidate();
    }
}
