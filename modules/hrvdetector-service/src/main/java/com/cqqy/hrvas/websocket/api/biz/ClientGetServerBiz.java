package com.cqqy.hrvas.websocket.api.biz;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.cqqy.hrvas.exception.BizException;
import com.cqqy.hrvas.exception.LoginException;
import com.cqqy.hrvas.service.DetectorConfigService;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ClientGetServerBiz {

    public final DetectorConfigService detectorConfigService;

    @Autowired
    public ClientGetServerBiz(DetectorConfigService detectorConfigService) {
        this.detectorConfigService = detectorConfigService;
    }

    public String getToken(HttpSession session) {
        Object tokenObj = session.getAttribute("token");
        Assert.notNull(tokenObj, () -> new LoginException("token信息失效"));
        return String.valueOf(tokenObj);
    }

    public void check(JSONObject result) {
        Assert.isTrue((int) result.get("code") == 200,
                () -> new BizException(StrUtil.format("调用平台端服务失败：{}", result.getString("msg"))));
    }
}
