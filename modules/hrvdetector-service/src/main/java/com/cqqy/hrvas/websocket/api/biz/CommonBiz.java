package com.cqqy.hrvas.websocket.api.biz;

import cn.hutool.core.lang.Assert;
import com.cqqy.hrvas.entity.DetectorConfig;
import com.cqqy.hrvas.exception.BizException;
import com.cqqy.hrvas.service.DetectorConfigService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static com.cqqy.hrvas.constant.UrlConstant.*;

@Component
public class CommonBiz {

    public final DetectorConfigService detectorConfigService;

    @Value("${spring.profiles.active}")
    private String active;

    @Value("${algorithm.uri}")
    private String uri;

    public CommonBiz(DetectorConfigService detectorConfigService) {
        this.detectorConfigService = detectorConfigService;
    }

    public String completePlatformUrl(String path) {
        DetectorConfig detectorConfig = detectorConfigService.getDetectorConfig();
        String netPlatformUrl = detectorConfig.getNetPlatformUrl();
        Assert.notNull(netPlatformUrl, () -> new BizException("平台端地址未配置"));
        return netPlatformUrl.concat(path);
    }

    public String completeAnalysisUrl() {
        return HTTP_PREFIX + uri + HTTP_ANALYSIS;
    }
}
