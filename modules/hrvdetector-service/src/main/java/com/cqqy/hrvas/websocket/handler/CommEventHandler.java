package com.cqqy.hrvas.websocket.handler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.cqqy.hrvas.comm.event.CommEvent;
import com.cqqy.hrvas.util.CacheUtil;
import com.cqqy.hrvas.websocket.service.WebSocketService;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import static com.cqqy.hrvas.constant.CacheConstant.CACHE_NAME_HRV_SYNC;
import static com.cqqy.hrvas.constant.CacheConstant.DEST_ID_KEY;

@Component
public class CommEventHandler {

    private final WebSocketService WebSocketService;

    private final CacheUtil cacheUtil;

    public CommEventHandler(com.cqqy.hrvas.websocket.service.WebSocketService webSocketService, CacheUtil cacheUtil) {
        WebSocketService = webSocketService;
        this.cacheUtil = cacheUtil;
    }

    @EventListener
    public void handleChildEvent(CommEvent event) {
        // 处理来自子模块的数据
        String destId = cacheUtil.getFromCache(CACHE_NAME_HRV_SYNC, DEST_ID_KEY);
        if (ObjectUtil.isEmpty(destId)) {
            return;
        }
        Object data = event.getData();
        String msgType = event.getMsgType();
        JSONObject message = JSONObject.from(data);
        WebSocketService.sendEcgToServer(message, msgType, destId);
    }
}
