package com.cqqy.hrvas.websocket.handler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.cqqy.hrvas.constant.WebSocketMsgType;
import com.cqqy.hrvas.websocket.event.MessageReceivedEvent;
import com.cqqy.hrvas.websocket.service.WebSocketService;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class MessageEventHandler {

    private final WebSocketService webSocketService;


    public MessageEventHandler(WebSocketService webSocketService) {
        this.webSocketService = webSocketService;
    }

    @EventListener
    public void handleMessageEvent(MessageReceivedEvent event) {
        String message = event.getMessage();
        JSONObject from = JSONObject.parseObject(message);
        String msgType = from.getString("msg_type");
        JSONObject data = from.getJSONObject("data");
        String httpType = from.getString("http_type");
        if (ObjectUtil.isNotEmpty(httpType) && "resp".equals(httpType)) {
            Integer code = from.getInteger("code");
            webSocketService.handleResponse(msgType, data, code);
        } else if (checkHrvSync(msgType)){
            webSocketService.handleHrvSync(msgType, data);
        } else {
            webSocketService.handleData(msgType, data);
        }
    }

    private boolean checkHrvSync(String msgType) {
        return WebSocketMsgType.EV_PUSH_START.equals(msgType) ||
                WebSocketMsgType.EV_TASK_START.equals(msgType) ||
                WebSocketMsgType.EV_TASK_STOP.equals(msgType);
    }
}
