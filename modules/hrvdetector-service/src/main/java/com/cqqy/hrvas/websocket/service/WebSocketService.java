package com.cqqy.hrvas.websocket.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.cqqy.hrvas.dto.config.DetectorStatusDTO;
import com.cqqy.hrvas.dto.config.DeviceInfoDTO;
import com.cqqy.hrvas.entity.InspectList;
import com.cqqy.hrvas.service.DetectorStatusService;
import com.cqqy.hrvas.service.DeviceInfoService;
import com.cqqy.hrvas.service.InspectListService;
import com.cqqy.hrvas.service.task.TaskService;
import com.cqqy.hrvas.util.CacheUtil;
import com.cqqy.hrvas.websocket.WebSocketClient;
import com.cqqy.hrvas.websocket.util.WebSocketUtil;
import org.springframework.stereotype.Service;

import static com.cqqy.hrvas.constant.CacheConstant.CACHE_NAME_HRV_SYNC;
import static com.cqqy.hrvas.constant.CacheConstant.DEST_ID_KEY;
import static com.cqqy.hrvas.constant.WebSocketMsgType.*;

@Service
public class WebSocketService {

    private final WebSocketClient webSocketClient;

    private final DeviceInfoService deviceInfoService;

    private final InspectListService inspectListService;

    private final DetectorStatusService detectorStatusService;

    private final CacheUtil cacheUtil;

    private final TaskService taskService;

    public WebSocketService(WebSocketClient webSocketClient, DeviceInfoService deviceInfoService, InspectListService inspectListService, DetectorStatusService detectorStatusService, CacheUtil cacheUtil, TaskService taskService) {
        this.webSocketClient = webSocketClient;
        this.deviceInfoService = deviceInfoService;
        this.inspectListService = inspectListService;
        this.detectorStatusService = detectorStatusService;
        this.cacheUtil = cacheUtil;
        this.taskService = taskService;
    }


    public void handleData(String event, JSONObject data) {
        switch (event) {
            case "offline_task" -> handleOfflineTask(data);
            case "device_info" -> handleDeviceInfo(data);
            case "request_device_info" -> handleRequestDeviceInfo();
        }
    }

    public void handleResponse(String event, JSONObject data, int code) {
        switch (event) {
            case "device_register" -> handleDeviceRegisterResponse(data, code);
            case "temp_task_upload", "offline_task_upload", "online_task_upload" -> handleTaskUpload(data, code);
        }
    }

    public void handleHrvSync(String event, JSONObject data) {
        switch (event) {
            case EV_PUSH_START -> handleHrvSyncPushStart(data);
            case EV_TASK_START -> handleHrvSyncTaskStart(event, data);
            case EV_TASK_STOP -> handleHrvSyncTaskStop(event, data);
        }
    }

    public void handleOfflineTask(JSONObject data) {
        InspectList inspectList = data.to(InspectList.class);
        inspectList.setLocalTaskType(0);
        inspectListService.addOfflineTask(inspectList);
    }

    public void handleDeviceInfo(JSONObject data) {
        DeviceInfoDTO deviceInfoDTO = data.to(DeviceInfoDTO.class);
        deviceInfoService.update(deviceInfoDTO);
    }

    public void handleDeviceRegisterResponse(JSONObject data, int code) {
        if (code == 200) {
            DetectorStatusDTO detectorStatusDTO = new DetectorStatusDTO();
            detectorStatusDTO.setRegistered(1);
            detectorStatusService.update(detectorStatusDTO);
        }
    }

    public void handleRequestDeviceInfo() {
        DeviceInfoDTO deviceInfo = deviceInfoService.getDeviceInfo();
        JSONObject data = JSONObject.from(deviceInfo);
        JSONObject msg = WebSocketUtil.setWebSocketMsg(data, "device_info");
        webSocketClient.sendMessage(msg.toJSONString());
    }

    public void handleTaskUpload(JSONObject data, int code) {
        Long id = data.getLong("id");
        if (ObjectUtil.isNotEmpty(code) && ObjectUtil.isNotEmpty(id)) {
            inspectListService.updateUploadStatus(id, code == 0 ? 1 : 2);
        }
    }

    public void sendMessageToServer(JSONObject jsonObject, String msgType) {
        JSONObject msg = new JSONObject();
        msg.put("msg_type", msgType);
        msg.put("data", jsonObject);
        webSocketClient.sendMessage(msg.toJSONString());
    }

    /**
     * 同步hrv
     */
    public void handleHrvSyncPushStart(JSONObject data) {
        String srcId = data.getString("src_id");
        cacheUtil.putToCache(CACHE_NAME_HRV_SYNC, DEST_ID_KEY, srcId); // 平台端来的src_id，即采集端的dest_id
        // 成功返回消息
        JSONObject msg = new JSONObject();
        msg.put("msg_type", EV_PUSH_START);
        msg.put("http_type", "resp");
        msg.put("code", 200);
        msg.put("msg", "success");
        JSONObject newData = new JSONObject();
        newData.put(DEST_ID_KEY, srcId);
        msg.put("data", newData);
        webSocketClient.sendMessage(msg.toJSONString());
    }

    public void handleHrvSyncTaskStart(String event, JSONObject data) {
        String srcId = data.getString("src_id");
        cacheUtil.putToCache(CACHE_NAME_HRV_SYNC, DEST_ID_KEY, srcId); // 平台端来的src_id，即采集端的dest_id
        handleHrvSyncTaskStartConfirm();
        // 自动开始
        Long inspectListId = data.getLong("inspect_list_id");
        taskService.examSyncStart(inspectListId);
    }

    public void handleHrvSyncTaskStop(String event, JSONObject data) {
        cacheUtil.evictCache(CACHE_NAME_HRV_SYNC, DEST_ID_KEY);
        Long inspectListId = data.getLong("inspect_list_id");
        taskService.examSyncStop(inspectListId);
        taskService.taskUpload(inspectListId);
    }


    // 确认 推送到测评端
    public void handleHrvSyncTaskStartConfirm() {
        String destId = cacheUtil.getFromCache(CACHE_NAME_HRV_SYNC, DEST_ID_KEY);
        JSONObject msg = new JSONObject();
        msg.put("msg_type", DE_TASK_START_CONFIRM);
        JSONObject data = new JSONObject();
        data.put(DEST_ID_KEY, destId);
        msg.put("data", data);
        webSocketClient.sendMessage(msg.toJSONString());
    }

    /**
     * msgType: waveform heart_rate confirm
     */
    public void sendEcgToServer(JSONObject jsonObject, String msgType, String destId) {
        JSONObject msg = new JSONObject();
        msg.put("msg_type", msgType);
        JSONObject data = new JSONObject();
        data.put(DEST_ID_KEY, destId);
        if ("waveform".equals(msgType)) {
            data.put("waveform", jsonObject);
        } else {
            data.put("heart_rate", jsonObject);
        }
        msg.put("data", data);
        webSocketClient.sendEcgMessage(msg.toJSONString());
    }
}
