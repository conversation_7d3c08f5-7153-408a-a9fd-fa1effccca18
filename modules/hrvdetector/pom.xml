<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <artifactId>hrvdetector-parent</artifactId>
    <groupId>com.cqqy.hrvas</groupId>
    <version>1.1.2-SNAPSHOT</version>
    <relativePath>../../pom.xml</relativePath>
  </parent>

  <artifactId>hrvdetector</artifactId>
  <name>hrvdetector</name>
  <description>心率变异分析系统采集端本地服务</description>

  <dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

    <dependency>
      <groupId>org.liquibase</groupId>
      <artifactId>liquibase-core</artifactId>
    </dependency>
    
    <!-- PostgreSQL JDBC Driver -->
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
    </dependency>

    <!-- OpenAPI 3 Documentation -->
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    </dependency>

    <dependency>
      <groupId>com.cqqy.hrvas</groupId>
      <artifactId>hrvdetector-service</artifactId>
    </dependency>

    <dependency>
      <groupId>com.cqqy.hrvas</groupId>
      <artifactId>hrvdetector-comm</artifactId>
    </dependency>

    <dependency>
      <groupId>com.cqqy.hrvas</groupId>
      <artifactId>hrvdetector-common</artifactId>
    </dependency>

    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- Optionally: parameterized tests support -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
          <configuration>
              <source>17</source>
              <target>17</target>
              <encoding>UTF-8</encoding>
          </configuration>
      </plugin>
      
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${spring-boot.version}</version>
        <configuration>
          <executable>true</executable>
          <excludes>
            <exclude>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
            </exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>dev</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <build>
        <plugins>
          <plugin>
              <groupId>org.springframework.boot</groupId>
              <artifactId>spring-boot-maven-plugin</artifactId>
              <version>${spring-boot.version}</version>
              <executions>
                  <execution>
                      <goals>
                          <goal>repackage</goal>
                      </goals>
                  </execution>
              </executions>
          </plugin>
          <plugin>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-maven-plugin</artifactId>
            <version>${liquibase.version}</version>
            <configuration>
                <changeSetAuthor>system</changeSetAuthor>
                <changeLogFile>src/main/resources/db/changelog/db.changelog-master.xml</changeLogFile>
                <outputChangeLogFile>${project.basedir}/src/main/resources/db/changelog/changes/${maven.build.timestamp}_changelog.xml</outputChangeLogFile>
                <diffChangeLogFile>${project.basedir}/src/main/resources/db/changelog/changes/${maven.build.timestamp}_changelog.xml</diffChangeLogFile>
                <driver>${liquibase-plugin.driver}</driver>
                <defaultSchemaName>${liquibase-plugin.defaultSchemaName}</defaultSchemaName>
                <url>${liquibase-plugin.url}</url>
                <username>${liquibase-plugin.username}</username>
                <password>${liquibase-plugin.password}</password>
<!--                <referenceUrl>hibernate:spring:com.cqqy.hrvas.entity?dialect=org.hibernate.dialect.PostgreSQLDialect&amp;hibernate.physical_naming_strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy&amp;hibernate.implicit_naming_strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy</referenceUrl>-->
                <referenceUrl>${liquibase-plugin.ref_url}</referenceUrl>
                <referenceDriver>${liquibase-plugin.ref_driver}</referenceDriver>
                <referenceUsername>${liquibase-plugin.ref_username}</referenceUsername>
                <referencePassword>${liquibase-plugin.ref_password}</referencePassword>
                <referenceDefaultSchemaName>${liquibase-plugin.ref_defaultSchemaName}</referenceDefaultSchemaName>
            </configuration>
            <dependencies>
                <dependency>
                    <groupId>org.liquibase</groupId>
                    <artifactId>liquibase-core</artifactId>
                    <version>${liquibase.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.liquibase.ext</groupId>
                    <artifactId>liquibase-hibernate6</artifactId>
                    <version>${liquibase.version}</version>
                </dependency>
                <dependency>
                    <groupId>jakarta.validation</groupId>
                    <artifactId>jakarta.validation-api</artifactId>
                    <version>${jakarta-validation.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-jpa</artifactId>
                    <version>${spring-boot.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-validation</artifactId>
                    <version>${spring-boot.version}</version>
                </dependency>
            </dependencies>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
