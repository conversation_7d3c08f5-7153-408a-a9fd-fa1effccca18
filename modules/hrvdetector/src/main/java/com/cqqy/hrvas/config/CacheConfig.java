package com.cqqy.hrvas.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.cqqy.hrvas.constant.CacheConstant.CACHE_NAME;
import static com.cqqy.hrvas.constant.CacheConstant.CACHE_NAME_HRV_SYNC;

@Configuration
@EnableCaching
public class CacheConfig {

    @Bean
    public CacheManager cacheManager() {
        return new ConcurrentMapCacheManager(CACHE_NAME, CACHE_NAME_HRV_SYNC);
    }
}