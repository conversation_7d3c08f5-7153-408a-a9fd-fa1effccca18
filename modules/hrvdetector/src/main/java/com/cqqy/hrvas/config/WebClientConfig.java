package com.cqqy.hrvas.config;

import com.cqqy.hrvas.entity.DetectorConfig;
import com.cqqy.hrvas.service.DetectorConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Slf4j
@Configuration
public class WebClientConfig {

    public final DetectorConfigService detectorConfigService;

    public WebClientConfig(DetectorConfigService detectorConfigService) {
        this.detectorConfigService = detectorConfigService;
    }

    @Bean
    public WebClient webClient() {
        DetectorConfig detectorConfig = detectorConfigService.getDetectorConfig();
        String netPlatformUrl = detectorConfig.getNetPlatformUrl();
        return WebClient.builder()
                .baseUrl(netPlatformUrl)
                .build();
    }
}
