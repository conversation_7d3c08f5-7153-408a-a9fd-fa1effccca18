package com.cqqy.hrvas.config;

import cn.hutool.core.util.URLUtil;
import com.cqqy.hrvas.dto.config.DeviceInfoDTO;
import com.cqqy.hrvas.entity.DetectorConfig;
import com.cqqy.hrvas.service.DetectorConfigService;
import com.cqqy.hrvas.service.DeviceInfoService;
import com.cqqy.hrvas.websocket.WebSocketClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.net.URL;

@Slf4j
@Configuration
public class WebSocketClientConfig {

    private final DeviceInfoService deviceInfoService;

    private final DetectorConfigService detectorConfigService;

    private final ApplicationEventPublisher eventPublisher;

    @Value("${spring.profiles.active}")
    private String active;

    public WebSocketClientConfig(DeviceInfoService deviceInfoService, DetectorConfigService detectorConfigService, ApplicationEventPublisher eventPublisher) {
        this.deviceInfoService = deviceInfoService;
        this.detectorConfigService = detectorConfigService;
        this.eventPublisher = eventPublisher;
    }

    @DependsOn("startupRunner")
    @Bean
    public WebSocketClient webSocketClient() {
        String newUrl = getUrl();
        WebSocketClient client = new WebSocketClient(newUrl, eventPublisher);
        client.connect();
        return client;
    }

    private String getUrl() {
        DetectorConfig detectorConfig = detectorConfigService.getDetectorConfig();
        String originalUrl = detectorConfig.getNetPlatformUrl();
        URL url = URLUtil.url(originalUrl);
        String newUrl = url.getHost() + ":" + url.getPort();
        DeviceInfoDTO deviceInfo = deviceInfoService.getDeviceInfo();
        String userId = deviceInfo.getDeviceNo().toString();
        String protocol = "ws";
        String route = "/api/hrv/";
        if (!"dev".equals(active)) {
            protocol = "wss";
            route = "/websocket/api/hrv/";
        }
        return protocol + "://"+ newUrl + route + userId;
    }
}
