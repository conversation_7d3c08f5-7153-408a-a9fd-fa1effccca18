package com.cqqy.hrvas.interceptor;

import cn.hutool.core.lang.Assert;
import com.cqqy.hrvas.annotation.Permission;
import com.cqqy.hrvas.exception.LoginException;
import com.cqqy.hrvas.util.SessionUtil;
import com.cqqy.hrvas.vo.login.LoginVo;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/26
 **/
@Slf4j
@Component
public class LoginInterceptor implements HandlerInterceptor {

    private final SessionUtil sessionUtil;

    public LoginInterceptor(SessionUtil sessionUtil) {
        this.sessionUtil = sessionUtil;
    }

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            HttpSession session = sessionUtil.getSession();
            String token = (String) session.getAttribute("token");
            LoginVo loginVo = (LoginVo) session.getAttribute("loginVo");
            // 校验登录
            Assert.notNull(token, () -> new LoginException("获取用户登录信息失败"));
            // 校验登录用户信息
            Assert.notNull(loginVo, () -> new LoginException("登录已过期或登录信息不存在，请重新登录"));

            // 获取登录信息判断
            String roleCode = loginVo.getRoleCode();
            boolean admin = loginVo.isAdmin();
            if (!admin) {
                Permission permission = handlerMethod.getMethodAnnotation(Permission.class);
                if (permission != null) {
                    // 从redis中获取权限列表
                    List<String> permissions = loginVo.getPermissions();
                    if (CollectionUtils.isEmpty(permissions)) {
                        throw new LoginException("当前用户未设置权限");
                    }
                    String value = permission.value();
                    String role = permission.role();
                    if (!permissions.contains(value)) {
                        log.error("没有访问权限的登录用户：" + loginVo);
                        throw new LoginException("没有访问权限");
                    }
                    if (StringUtils.isNotBlank(role)) {
                        if (roleCode.equals(role)) {
                            log.error("没有访问权限的登录用户：" + loginVo);
                            throw new LoginException("该角色没有访问权限");
                        }
                    }
                }
            }
        }

        return true;
    }

    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable ModelAndView modelAndView) throws Exception {
    }

    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable Exception ex) throws Exception {
    }
}
