package com.cqqy.hrvas.start;

import com.alibaba.fastjson2.JSONObject;
import com.cqqy.hrvas.dto.config.DeviceInfoDTO;
import com.cqqy.hrvas.entity.DetectorStatus;
import com.cqqy.hrvas.service.DetectorStatusService;
import com.cqqy.hrvas.service.DeviceInfoService;
import com.cqqy.hrvas.websocket.WebSocketClient;
import com.cqqy.hrvas.websocket.service.WebSocketService;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class ClientRunner implements CommandLineRunner {

    private final WebSocketClient webSocketClient;

    private final WebSocketService webSocketService;

    private final DeviceInfoService deviceInfoService;

    private final DetectorStatusService detectorStatusService;

    public ClientRunner(WebSocketClient webSocketClient, WebSocketService webSocketService, DeviceInfoService deviceInfoService, DetectorStatusService detectorStatusService) {
        this.webSocketClient = webSocketClient;
        this.webSocketService = webSocketService;
        this.deviceInfoService = deviceInfoService;
        this.detectorStatusService = detectorStatusService;
    }

    @Override
    public void run(String... args) {
        // 连接后发送初始化消息
        webSocketClient.sendMessage("{\"msg_type\":\"handshake\"}");
        // 自动注册设备
        DetectorStatus detectorStatus = detectorStatusService.getDetectorStatus();
        if (detectorStatus.getRegistered() == 0) {
            DeviceInfoDTO deviceInfo = deviceInfoService.getDeviceInfo();
            JSONObject data = JSONObject.from(deviceInfo);
            webSocketService.sendMessageToServer(data, "device_register");
        }
    }
}
