package com.cqqy.hrvas.start;

import cn.hutool.core.bean.BeanUtil;
import com.cqqy.hrvas.cache.DetectorConfigCache;
import com.cqqy.hrvas.entity.DetectorConfig;
import com.cqqy.hrvas.service.DetectorConfigService;
import com.cqqy.hrvas.util.CacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import static com.cqqy.hrvas.constant.CacheConstant.CACHE_NAME;
import static com.cqqy.hrvas.constant.CacheConstant.CONFIG_KEY;

@Slf4j
@Configuration
public class StartConfigCache {

    public final DetectorConfigService detectorConfigService;

    public final CacheUtil cacheUtil;

    public StartConfigCache(DetectorConfigService detectorConfigService, CacheUtil cacheUtil) {
        this.detectorConfigService = detectorConfigService;
        this.cacheUtil = cacheUtil;
        DetectorConfig detectorConfig = detectorConfigService.getDetectorConfig();
        DetectorConfigCache cache = BeanUtil.copyProperties(detectorConfig, DetectorConfigCache.class);
        this.cacheUtil.putToCache(CACHE_NAME, CONFIG_KEY, cache);
    }
}
