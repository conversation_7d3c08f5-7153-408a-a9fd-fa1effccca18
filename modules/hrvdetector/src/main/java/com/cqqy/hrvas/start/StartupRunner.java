package com.cqqy.hrvas.start;

import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.Random;


@Slf4j
@Component
public class StartupRunner {

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public StartupRunner(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @PostConstruct
    public void init() {
        initDetectorConfig();
        initDeviceInfo();
        initDetectorStatus();
    }

    private void initDetectorConfig() {
        String countSql = "select count(*) from public.detector_config";
        String sql = "insert into public.detector_config (id, net_server_url, net_platform_url, net_report_url, net_online_upgrade, ecg_st_template_iso, ecg_st_template_st, ecg_notch_mode, ecg_arrhythmia_channel, ecg_power_filtering, ecg_gain, alarm_ecg_tachycardia_adult_upper, alarm_ecg_tachycardia_adult_lower, alarm_ecg_tachycardia_child_upper, alarm_ecg_tachycardia_child_lower, alarm_ecg_tachycardia_old_upper, alarm_ecg_tachycardia_old_lower, alarm_ecg_bradycardia_adult_upper, alarm_ecg_bradycardia_adult_lower, alarm_ecg_bradycardia_child_upper, alarm_ecg_bradycardia_child_lower, alarm_ecg_bradycardia_old_upper, alarm_ecg_bradycardia_old_lower, alarm_fibrotic_tremor_adult, alarm_fibrotic_tremor_child, alarm_battery_low, test_passed_interval_upper, test_passed_interval_lower, ecg_patient_model, ecg_lead_model, ecg_lead_channel)\n" +
                "values  (1, 'http://localhost:8080', 'http://localhost:8080', 'http://localhost:8080', 0, -21, 26, 3, 0, 0, 2, 80, 20, 80, 20, 80, 20, 80, 20, 80, 20, 80, 20, 0, 0, 0.20, 80, 20, 0, 0, 0);";
        Integer count = jdbcTemplate.queryForObject(countSql, Integer.class);
        if (count == 0) {
            jdbcTemplate.execute(sql);
        }
    }

    private void initDeviceInfo() {
        String countSql = "select count(*) from public.device_info";
        Integer count = jdbcTemplate.queryForObject(countSql, Integer.class);
        if (count == 0) {
            int num = getNum();
            String mac = getMac();
            String ip = getIp();
            String sql = "insert into public.device_info (id, device_no, device_type, device_mac, device_ip)\n" +
                    "values  (1, " + num + ", 0, " + "\'" + mac + "\', \'" + ip + "\'" + ")";
            jdbcTemplate.execute(sql);
        }
    }

    private void initDetectorStatus() {
        String countSql = "select count(*) from public.detector_status";
        String sql = "insert into public.detector_status (id, registered)\n" +
                "values  (1, 0);";
        Integer count = jdbcTemplate.queryForObject(countSql, Integer.class);
        if (count == 0) {
            jdbcTemplate.execute(sql);
        }
    }

    private int getNum() {
        String ip = NetUtil.getLocalhost().getHostAddress();
        Random random = new Random(ip.hashCode());
        return random.nextInt(1000);
    }

    private String getMac() {
        String localMacAddress = NetUtil.getLocalMacAddress();
        if (ObjectUtil.isEmpty(localMacAddress)) {
            // 开发环境测试
            localMacAddress = RandomUtil.randomString("ABCDEF0123456789", 12)
                    .replaceAll("(.{2})", "$1:")
                    .substring(0, 17);
        }
        return localMacAddress;
    }

    private String getIp() {
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();

                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    if (!address.isLoopbackAddress() && !address.isLinkLocalAddress()) {
                        return address.getHostAddress();
                    }
                }
            }
        } catch (SocketException e) {
            throw new RuntimeException(e);
        }

        return "127.0.0.1";
    }
}
