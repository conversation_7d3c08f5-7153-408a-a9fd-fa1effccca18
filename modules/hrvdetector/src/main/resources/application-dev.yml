spring:
  datasource:
    url: ********************************************************
#    url: ************************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  liquibase:
    contexts: development

logging:
  level:
    root: info
#    com.cqqy.hrvas: off

hrvdetector:
  comm:
    m10x:
      port: COM3
      maxRetries: 3
      retryInterval: 2000
    power:
      port: COM1
      maxRetries: 3
      retryInterval: 2000
  storage:
    filepath: /usr/local/software/
    filename:
      prefix: waveform_data
      suffix: csv

algorithm:
  uri: 192.168.2.128:8000
