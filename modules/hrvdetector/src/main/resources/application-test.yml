spring:
  datasource:
    url: *********************************************
    username: postgres
    password: postgres
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  liquibase:
    contexts: test

logging:
  level:
    root: info
    com.cqqy.hrvas: off

hrvdetector:
  comm:
    m10x:
      port: ttyS4
      maxRetries: 3
      retryInterval: 2000
    power:
      port: ttyS3
      maxRetries: 3
      retryInterval: 2000
  storage:
    filepath: /usr/local/software/
    filename:
      prefix: waveform_data
      suffix: csv

algorithm:
  uri: *************:8000