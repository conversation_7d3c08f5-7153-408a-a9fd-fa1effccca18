spring:
  application:
    name: hrv-detector-local
  datasource:
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: none
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.xml
    enabled: true
  profiles:
    active: dev
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
    tagsSorter: alpha
    tryItOutEnabled: true
    filter: true
    enabled: true
  packages-to-scan: com.cqqy.hrvas

knife4j:
  enable: true                # 启用Knife4j增强功能
  setting:
    language: zh_cn           # 中文界面
  production: false           # 生产环境设为true以屏蔽文档
