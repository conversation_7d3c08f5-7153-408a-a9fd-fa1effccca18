{"name": "hrvDetector", "productName": "hrvDetector", "version": "1.0.25", "description": "My Electron application description", "main": ".vite/build/main.js", "build": {"appId": "com.hrvDetector.app", "productName": "hrvDetector", "extraResources": ["preload.js"], "mac": {"target": ["dmg", "zip"]}, "win": {"target": ["nsis", "zip"]}, "linux": {"target": ["AppImage", "deb"]}}, "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint --ext .ts,.tsx .", "dist": "electron-packager ./ hrvDetector --platform=linux --arch=arm64 --out ./OutApp --overwrite --icon=./src/favicon.ico --asar", "build": "electron-builder --linux --x64", "deb64": "electron-installer-debian --src OutApp/hrvDetector-linux-arm64/ --dest OutApp/installers/ --arch arm64"}, "keywords": [], "author": {"name": "重庆谦雅科技有限公司", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron-forge/plugin-vite": "^7.7.0", "@electron/fuses": "^1.8.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.11", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-vue": "^5.2.1", "electron": "^35.0.1", "electron-builder": "^26.0.12", "electron-installer-debian": "^3.2.0", "electron-log": "^5.3.2", "electron-packager": "^17.1.2", "eslint": "^8.57.1", "eslint-plugin-import": "^2.31.0", "lodash": "^4.17.21", "sass-embedded": "^1.82.0", "ts-node": "^10.9.2", "typescript": "~4.5.4", "unplugin-auto-import": "^19.1.1", "vite": "^5.4.14", "vite-plugin-app-loading": "^0.3.0", "vite-plugin-archiver": "^0.1.1", "vite-plugin-banner": "^0.8.0", "vite-plugin-compression2": "^1.3.3", "vite-plugin-fake-server": "^2.1.3", "vite-plugin-pages": "^0.32.4", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.6.7", "vite-plugin-vue-layouts": "^0.11.0", "vite-plugin-vue-meta-layouts": "^0.5.1"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.6", "axios": "^1.8.3", "dayjs": "^1.11.13", "echarts": "^5.6.0", "electron-squirrel-startup": "^1.0.1", "konva": "^9.3.20", "md5": "^2.3.0", "mitt": "^3.0.1", "moment": "^2.30.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.2.0", "video.js": "^8.22.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}}