<?xml version="1.0" encoding="UTF-8"?><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.cqqy.hrvas</groupId>
  <artifactId>hrvdetector-parent</artifactId>
  <version>1.1.2-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>hrv-detector</name>
  <url>http://www.example.com</url>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.release>17</maven.compiler.release>
    <junit.version>5.11.0</junit.version>
    <spring-boot.version>3.4.3</spring-boot.version>
    <postgresql.version>42.7.2</postgresql.version>
    <liquibase.version>4.29.2</liquibase.version>
    <liquibase-hibernate.version>4.29.2</liquibase-hibernate.version>
    <jakarta-validation.version>3.0.2</jakarta-validation.version>
    <lombok.version>1.18.30</lombok.version>
    <swagger2.version>3.0.0</swagger2.version>
    <jSerialComm.version>2.11.0</jSerialComm.version>
    <mapstruct.version>1.6.3</mapstruct.version>

    <maven.build.timestamp.format>yyyyMMdd-HHmm</maven.build.timestamp.format>
    <jakarta-annotation.version>2.1.1</jakarta-annotation.version>
    <mockito.version>5.14.2</mockito.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.cqqy.hrvas</groupId>
        <artifactId>hrvdetector</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.cqqy.hrvas</groupId>
        <artifactId>hrvdetector-common</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.cqqy.hrvas</groupId>
        <artifactId>hrvdetector-service</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.cqqy.hrvas</groupId>
        <artifactId>hrvdetector-comm</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- Spring Boot -->
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- 现有的 JUnit 依赖管理 -->
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>${junit.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- OpenAPI 3 Documentation -->
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi</artifactId>
        <version>2.7.0</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- PostgreSQL JDBC Driver -->
      <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <version>${postgresql.version}</version>
      </dependency>

      <!-- Liquibase Dependencies -->
      <dependency>
        <groupId>org.liquibase</groupId>
        <artifactId>liquibase-core</artifactId>
        <version>${liquibase.version}</version>
      </dependency>

      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
      </dependency>

      <!-- Jakarta Annotation API -->
      <dependency>
        <groupId>jakarta.annotation</groupId>
        <artifactId>jakarta.annotation-api</artifactId>
        <version>${jakarta-annotation.version}</version>
      </dependency>

      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>${mockito.version}</version>
      </dependency>

      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-junit-jupiter</artifactId>
        <version>${mockito.version}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-tomcat</artifactId>
        <version>${spring-boot.version}</version>
      </dependency>

      <!-- swagger start -->
      <dependency>
        <groupId>io.springfox</groupId>
        <artifactId>springfox-swagger2</artifactId>
        <version>${swagger2.version}</version>
      </dependency>
      <dependency>
        <groupId>io.springfox</groupId>
        <artifactId>springfox-swagger-ui</artifactId>
        <version>${swagger2.version}</version>
      </dependency>
      <!-- swagger end -->
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-clean-plugin</artifactId>
          <version>3.4.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-resources-plugin</artifactId>
          <version>3.3.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.13.0</version>
          <configuration>
            <annotationProcessorPaths>
              <path>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
              </path>
            </annotationProcessorPaths>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>3.3.0</version>
          <configuration>
            <argLine>
              -javaagent:${settings.localRepository}/org/mockito/mockito-core/${mockito.version}/mockito-core-${mockito.version}.jar
              -Xshare:off
              -Duser.timezone=Asia/Shanghai
            </argLine>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.4.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-install-plugin</artifactId>
          <version>3.1.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>3.1.2</version>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>3.6.0</version>
        <configuration>
          <descriptorRefs>
            <descriptorRef>jar-with-dependencies</descriptorRef>
          </descriptorRefs>
          <archive>
            <manifest>
              <mainClass>com.cqqy.hrvas.hrvdetector.HrvdetectorApplication</mainClass>
            </manifest>
          </archive>
        </configuration>
        <executions>
          <execution>
            <id>make-assembly</id>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>dev</id>
      <activation>
          <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <liquibase-plugin.url>jdbc:postgresql://***************:5432/hrv_detector_target</liquibase-plugin.url>
        <liquibase-plugin.username>postgres</liquibase-plugin.username>
        <liquibase-plugin.password>postgres</liquibase-plugin.password>
        <liquibase-plugin.driver>org.postgresql.Driver</liquibase-plugin.driver>
        <liquibase-plugin.defaultSchemaName>public</liquibase-plugin.defaultSchemaName>
        <liquibase-plugin.ref_url>*******************************************************</liquibase-plugin.ref_url>
        <liquibase-plugin.ref_username>postgres</liquibase-plugin.ref_username>
        <liquibase-plugin.ref_password>postgres</liquibase-plugin.ref_password>
        <liquibase-plugin.ref_driver>org.postgresql.Driver</liquibase-plugin.ref_driver>
        <liquibase-plugin.ref_defaultSchemaName>public</liquibase-plugin.ref_defaultSchemaName>
      </properties>
    </profile>
  </profiles>

  <modules>
    <module>modules/hrvdetector</module>
    <module>modules/hrvdetector-common</module>
    <module>modules/hrvdetector-service</module>
    <module>modules/hrvdetector-comm</module>
  </modules>
</project>
