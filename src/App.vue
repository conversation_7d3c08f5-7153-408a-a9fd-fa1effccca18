<template>
	<a-config-provider :getPopupContainer="getPopupContainer" :theme="{
		token: {
			colorPrimary: '#00A6B0',
		},
	}">
		<router-view />
	</a-config-provider>
</template>

<script setup>
import {
	ref,
	onMounted
} from 'vue';
import {
	RouterView
} from "vue-router";
import { userMessageStore } from "@/stores/modules/userMessage";

const userStore = userMessageStore();
const count = ref(0);
const getPopupContainer = (el, dialogContext) => {
	if (dialogContext) {
		return dialogContext.getDialogWrap();
	} else {
		return document.body;
	}
}

onMounted(() => {
	console.log('欢迎使用-心率变异采集分析系统');
	// console.error('This is an error message from the main process');
	// userStore.initWebSocket();
})
</script>