// import { ElMessage } from "element-plus";
import { message } from 'ant-design-vue';
import { userMessageStore } from "@/stores/modules/userMessage";
// import { LOGIN_URL } from "@/config";
import router from "@/routers";

/**
 * @description: 校验网络请求状态码
 * @param {Number} status
 * @return void
 */
export const checkStatus = (status: number, msg?: string = '', data: any) => {
  switch (status) {
    case 201: // 首次登录
      userMessageStore().setToken(data.token);
      // localStorage.setItem('firstInfo', data.conig);
      message.warning(msg);
      // useUserStore().setFirstLogin(true);
      break;
    case 202: // 批量上传后的数据列表上传
      message.error("请将错误信息填写正确后再上传");
      return { data: false }
      break;
    case 203: // 无数据
      message.error(msg);
      break;
    case 400:
      message.error("请求失败！请您稍后重试");
      break;
    case 5001:
      message.error("登录失效！请您重新登录");
      userMessageStore().setToken("");
      sessionStorage.clear();
      router.push('/login');
      window.location.reload(); // 刷新当前页面  刷新后会重新执行router.beforeEach  然后会重新执行loginAp 
      break;
    case 403:
      message.error("当前账号无权限访问！");
      break;
    case 404:
      message.error("你所访问的资源不存在！");
      break;
    case 405:
      message.error("请求方式错误！请您稍后重试");
      break;
    case 408:
      message.error("请求超时！请您稍后重试");
      break;
    case 500:
      message.error(msg);
      break;
    case 501: // 参数设置密码校验错误返回上一页
      message.error(msg);
      // router.go(-1)
      break;
    case 502:
      message.error("网关错误！");
      break;
    case 503:
      message.error("服务不可用！");
      break;
    case 504:
      message.error("网关超时！");
      break;
    default:
      message.error(msg);
  }
};
