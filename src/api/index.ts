import axios, { AxiosInstance, AxiosError, AxiosRequestConfig, InternalAxiosRequestConfig, AxiosResponse } from "axios";
import Spin from "@/components/Loading/fullScreen";
// import { ElMessage } from "element-plus";
import { message } from 'ant-design-vue';
import { ResultData } from "@/api/interface";
import { ResultEnum } from "@/enums/httpEnum";
import { checkStatus } from "./helper/checkStatus";
import { AxiosCanceler } from "./helper/axiosCancel";
import { userMessageStore } from "@/stores/modules/userMessage";
import router from "@/routers";
import storage from "@/utils/storage";
import useSettingsStore from '@/stores/modules/settings'

export interface CustomAxiosRequestConfig extends InternalAxiosRequestConfig {
  loading?: boolean;
  cancel?: boolean;
  isShowMessage?: boolean;
}

// console.log(storage.local.has("serverUrl"))
let url = storage.local.has("serverUrl") ? storage.local.get("serverUrl") : "http://127.0.0.1:80";

const config = {
  // isShowMessage: true, // 是否显示错误信息
  // 默认地址请求地址，可在 .env.** 文件中修改
  baseURL: url as string,
  // 设置超时时间
  timeout: ResultEnum.TIMEOUT as number,
  // 跨域时候允许携带凭证
  withCredentials: true
};

const axiosCanceler = new AxiosCanceler();

class RequestHttp {
  service: AxiosInstance;
  public constructor(config: AxiosRequestConfig) {
    // instantiation
    this.service = axios.create(config);

    /**
     * @description 请求拦截器
     * 客户端发送请求 -> [请求拦截器] -> 服务器
     * token校验(JWT) : 接受服务器返回的 token,存储到 vuex/pinia/本地储存当中
     */
    this.service.interceptors.request.use(
      (config: CustomAxiosRequestConfig) => {
        const userStore = userMessageStore();
        // 重复请求不需要取消，在 api 服务中通过指定的第三个参数: { cancel: false } 来控制
        config.cancel ??= true;
        config.cancel && axiosCanceler.addPending(config);
        // 当前请求不需要显示 loading，在 api 服务中通过指定的第三个参数: { loading: false } 来控制
        config.loading ??= true;
        // config.loading && Spin.showFullScreenLoading('数据加载中...');
        config.loading && Spin.show('数据加载中...');
        if (config.headers && typeof config.headers.set === "function") {
          config.headers.set("Authorization", sessionStorage.getItem("token") as string);
        }
        return config;
      },
      (error: AxiosError) => {
        return Promise.reject(error);
      }
    );

    /**
     * @description 响应拦截器
     *  服务器换返回信息 -> [拦截统一处理] -> 客户端JS获取到信息
     */
    this.service.interceptors.response.use(
      (response: AxiosResponse & { config: CustomAxiosRequestConfig }) => {
        const { data, config } = response;
        axiosCanceler.removePending(config);
        // config.loading && Spin.tryHideFullScreenLoading();
        config.loading && Spin.hide();
        // 全局错误信息拦截（防止下载文件的时候返回数据流，没有 code 直接报错
        if (data.code && data.code !== ResultEnum.SUCCESS) {
          // 不是200
          if (data.code === 202) return checkStatus(data.code, data.msg);
          data.code === 201
            ? checkStatus(data.code, data.msg, { token: data.data, conig: config.data })
            : checkStatus(data.code, data.msg);
          if (config.baseURL == url) {
            return Promise.reject(data);
          }
          return Promise.reject({ status: 0 });
        }
        if (config.isShowMessage) {
          // 是否成功显示信息
          message.success(data.msg);
        }
        // 成功请求（在页面上除非特殊情况，否则不用处理失败逻辑）
        return data;
      },
      async (error: AxiosError) => {
        const { response } = error;
        // Spin.tryHideFullScreenLoading();
        Spin.hide();
        // 请求超时 && 网络错误单独判断，没有 response
        if (error.message.indexOf("timeout") !== -1) message.error("请求超时！请您稍后重试");
        if (error.message.indexOf("Network Error") !== -1) message.error("网络错误！请您稍后重试");
        // 根据服务器响应的错误状态码，做不同的处理
        if (response) checkStatus(response.status);
        // 服务器结果都没有返回(可能服务器错误可能客户端断网)，断网处理:可以跳转到断网页面
        if (!window.navigator.onLine) router.replace("/500");
        return Promise.reject(error);
      }
    );
  }

  /**
   * @description 常用请求方法封装
   */
  get<T>(url: string, params?: object, _object = {}): Promise<ResultData<T>> {
    return this.service.get(url, { params, ..._object });
  }
  post<T>(url: string, params?: object | string, _object = {}): Promise<ResultData<T>> {
    return this.service.post(url, params, _object);
  }
  put<T>(url: string, params?: object, _object = {}): Promise<ResultData<T>> {
    return this.service.put(url, params, _object);
  }
  delete<T>(url: string, params?: any, _object = {}): Promise<ResultData<T>> {
    return this.service.delete(url, { params, ..._object });
  }
  download(url: string, params?: object, _object = {}): Promise<BlobPart> {
    return this.service.post(url, params, { ..._object, responseType: "blob" });
  }
  downloadTemp(url: string, params?: object, _object = {}): Promise<BlobPart> {
    return this.service.get(url, { params, ..._object, responseType: "blob" });
  }
}

export default new RequestHttp(config);
