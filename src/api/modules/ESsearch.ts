// import localRep from "@/api/local";
import apiReq from "@/api";
import qs from "qs";

// 【采集端】临时登记
export const getPatientInfoList = (params: any) => {
    return apiReq.post(`/api/es/search/patient_info`, qs.stringify(params));
};

// 【采集端】主屏幕 待检清单
export const getInspectList = (params: any) => {
    return apiReq.post(`/api/es/search/inspect_list`, qs.stringify(params));
};

// 【采集端】获取指定患者检查清单列表
export const getInspectListById = (params: any) => {
    return apiReq.post(`/api/es/search/inspect_list_by_patient`, qs.stringify(params));
};