import localRep from "@/api/local";

/**
 * @name 设备SDK模块
 */
const PORT = '/api/touch_think';

// 唤醒
export const backlightWakeup = (params: any) => {
    return localRep.get(`${PORT}/backlight/wakeup`, { ...params });
};

// 休眠
export const backlightSleep = (params: any) => {
    return localRep.get(`${PORT}/backlight/sleep`, { ...params });
};

// 取消静音
export const audioUnmute = (params: any) => {
    return localRep.get(`${PORT}/audio/unmute`, { ...params });
};

// 静音
export const audioMute = (params: any) => {
    return localRep.get(`${PORT}/audio/mute`, { ...params });
};

// 关机
export const shutDown = (params: any) => {
    return localRep.get(`${PORT}/shutdown`, { ...params });
};

/**
 * @name 设备信息模块
 */
const PORT_V = '/api/version';

// 获取设备详情
export const getDeviceInfo = (params: any) => {
    return localRep.get(`/api/device/device_info`, { ...params });
};

// 获取版本信息
export const getVersion = (params: any) => {
    return localRep.get(`${PORT_V}/current`, { ...params });
};

/**
 * @name 设备配置模块
 */
const PORT_C = '/api/config';

// 获取配置详情
export const getConfigDetails = (params: any) => {
    return localRep.get(`${PORT_C}/config_info`, { ...params });
};

// 测试校准区间更新
export const testConfigUpdate = (params: any) => {
    return localRep.post(`${PORT_C}/update/test_config`, { ...params });
};

// 网络配置更新
export const networkConfigUpdate = (params: any) => {
    return localRep.post(`${PORT_C}/update/network_config`, { ...params });
};

// ECG配置更新
export const ecgConfigUpdate = (params: any) => {
    return localRep.post(`${PORT_C}/update/ecg_config`, { ...params });
};

// 心电配置更新
export const alarmConfigUpdate = (params: any) => {
    return localRep.post(`${PORT_C}/update/alarm_config`, { ...params });
};