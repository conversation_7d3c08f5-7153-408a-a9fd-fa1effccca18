import { useSSE } from '@/api/sse';

const { connect, disconnect, isConnected } = useSSE<{ message: string }>();

// const baseUrl = 'http://127.0.0.1:8080';
const baseUrl = 'http://192.168.3.22:8080';
// const baseUrl = window.webConfig.SERVER_API_URL || 'http://127.0.0.1:8080'

// 连接SSE
connect('https://api.example.com/sse', {
    onMessage: (data) => {
        console.log('收到消息:', data.message);
    },
    onError: (error) => {
        console.error('SSE错误:', error);
    },
    onOpen: () => {
        console.log('SSE连接已建立');
    }
}, {
    retryInterval: 5000,
    maxRetries: 3
});

// 断开连接
disconnect();