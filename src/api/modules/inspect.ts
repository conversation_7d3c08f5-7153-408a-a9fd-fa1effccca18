import localRep from "@/api/local";
import apiReq from "@/api";

/**
 * @name 检查清单模块
 */
export const getInspectListPage = (params: any) => {
    return apiReq.post(`/api/admin/inspectList/getInspectListPage`, { ...params, 'inspectListType': '0' });
};

export const deleteInspectList = (id: any) => {
    return apiReq.post(`/api/admin/inspectList/deleteInspectList/${id}`);
};

export const getInspectList = (id: any) => {
    return apiReq.post(`/api/admin/inspectList/getInspectList/${id}`);
};

/**
 * @name 任务控制模块
 */
// 采集任务开始
export const taskExamStart = (params: any) => {
    return localRep.get(`/api/task/exam/start`, { ...params });
};
// 采集任务结束
export const taskExamStop = (params: any) => {
    return localRep.get(`/api/task/exam/stop`, { ...params });
};
// 采集任务退出
export const taskExamExit = (params: any) => {
    return localRep.get(`/api/task/exam/exit`, { ...params });
};
// 采集任务终止（删除）
export const taskExamDiscontinue = (params: any) => {
    return localRep.get(`/api/task/exam/discontinue`, { ...params });
};

/**
 * @name 静态资源模块
 */

const PORT = 'api/paradigm_file';

// 获取mat音乐和图片
export const get_MAT_MusicImg = (params: any) => {
    return localRep.get(`${PORT}/mat`);
};

// 获取scwt音乐和图片
export const get_SCWT_MusicImg = (params: any) => {
    return localRep.get(`${PORT}/scwt`);
};

// 获取mat ES阶段题目
export const get_MAT_ES_Topic = (params: any) => {
    return localRep.get(`${PORT}/topic_mat_es`);
};

// 获取mat DS阶段题目
export const get_MAT_DS_Topic = (params: any) => {
    return localRep.get(`${PORT}/topic_mat_ds`);
};

// 获取scwt 一致性测试题目
export const get_SCWT_Consistency = (params: any) => {
    return localRep.get(`${PORT}/topic_scwt_consistency`);
};

// 获取scwt 非一致性测试题目
export const get_SCWT_Unconsistency = (params: any) => {
    return localRep.get(`${PORT}/topic_scwt_not_consistency`);
};

// 提交mat ES阶段答题记录
export const post_MAT_ES_Topic = (params: any) => {
    return localRep.post(`${PORT}/commit_mat_es`, params);
};

// 提交mat DS阶段答题记录
export const post_MAT_DS_Topic = (params: any) => {
    return localRep.post(`${PORT}/commit_mat_ds`, params);
};

// 提交scwt 一致性测试答题记录
export const post_SCWT_Consistency = (params: any) => {
    return localRep.post(`${PORT}/commit_scwt_consistency`, params);
};

// 提交scwt 非一致性测试答题记录
export const post_SCWT_Unconsistency = (params: any) => {
    return localRep.post(`${PORT}/commit_scwt_not_consistency`, params);
};

// 提交scwt 提交scwt-mat记录
export const commit_event = (params: any) => {
    return localRep.post(`${PORT}/commit_event?inspectListId=${params.inspectListId}&taskType=${params.taskType}`, params.answerData);
};