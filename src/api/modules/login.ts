import { Login } from "@/api/interface/index";
import localRep from "@/api/local";
// import apiReq from "@/api";

/**
 * @name 登录模块
 */
const PORT = '/api/login'
// 用户登录
export const loginApi = (params: Login.ReqLoginForm) => {
    return localRep.post<Login.ResLogin>(`${PORT}/login`, params, { loading: false });
};


// 用户退出登录
export const logoutApi = () => {
    return localRep.post(`${PORT}/logout`);
};

// 获取管理后台登录用户信息
export const getLoginUserInfo = () => {
    return localRep.post(`/api/login/getLoginUserInfo`);
    // 如果想让菜单变为本地数据，注释上一行代码，并引入本地 authMenuList.json 数据
    // return authMenuList;
};