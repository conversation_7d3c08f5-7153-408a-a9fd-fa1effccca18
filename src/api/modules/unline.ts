import localRep from "@/api/local";
import qs from "qs";

/**
 * @name 临时任务模块
 */

// 临时任务添加
export const tempAdd = (params: any) => {
    return localRep.post(`/api/patient_info/add`, { ...params });
};

// 任务上传
export const tempUpload = (params: any) => {
    return localRep.get(`/api/upload/upload`, { ...params });
};

/**
 * @name 离线任务模块
 */
const PORT = '/api/inspect_list';

// 临时任务终止
export const tempRemove = (params: any) => {
    return localRep.post(`${PORT}/temp/remove`, { ...params });
};

// 离线任务列表
export const offlineList = (params: any) => {
    return localRep.post(`${PORT}/offline/list`, qs.stringify({ ...params }));
};

// 离线任务终止
export const offlineEnd = (params: any) => {
    return localRep.post(`${PORT}/offline/end`, { ...params });
};

// 离线任务详情
export const offlineDetails = (params: any) => {
    return localRep.post(`${PORT}/offline/details`, { ...params });
};

// 未上报统计
export const noReportStatistics = (params: any) => {
    return localRep.post(`${PORT}/not_report_statistics`, { ...params });
};

// 管理端统计
export const manageStatistics = (params: any) => {
    return localRep.post(`${PORT}/manage_statistics`, { ...params });
};

// 采集端统计
export const detectorStatistics = (params: any) => {
    return localRep.post(`${PORT}/detector_statistics`, { ...params });
};
