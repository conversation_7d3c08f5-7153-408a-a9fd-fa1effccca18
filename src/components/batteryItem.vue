<template>
    <!-- 电池电量Icon组件 -->
    <div class="electric-panel" :class="bgClass">
        <div class="panel" :style="{ transform: `rotate(${rotate}deg)` }">
            <div class="remainder" :style="{ width: batteryNum + '%' }" />
            <!-- 充电中显示充电Icon -->
            <ThunderboltFilled v-if="proIsCharge"
                style="font-size: .8rem; color: #edd844; transform: rotate(225deg);position: fixed;top: -3px;left: 4px;" />
        </div>
        <div v-show="showText" :style="{ marginLeft: (parseFloat(rotate) ? 0 : '10') + 'px' }" class="text">

            <!-- 充电中不显示电量百分比 -->
            <template v-if="!proIsCharge">{{ batteryNum }}%</template>
            <template v-else>
                <!-- 充电中 -->
                {{ batteryNum }}%
            </template>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, defineProps } from "vue";
import $bus from "@/utils/bus.ts";
import { useSSEStore } from '@/stores/modules/sseRequest';
import { ThunderboltFilled } from '@ant-design/icons-vue';

const props = defineProps({
    // 电池显示的数值
    quantity: {
        type: Number,
        default: 60,
    },
    // 是否显示电量百分比
    showText: {
        type: Boolean,
        default: true,
    },
    // 是否展示充电状态
    proIsCharge: {
        type: Boolean,
        default: true,
    },
    // 旋转百分比
    rotate: {
        type: String,
        default: '0',
    },
});

const proIsCharge = ref(props.proIsCharge);
const batteryNum = ref(props.quantity);
const bgClass = computed(() => {
    if (props.proIsCharge) return 'primary';
    if (batteryNum.value >= 30) {
        return 'success'
    } else if (batteryNum.value >= 15) {
        return 'warning'
    } else if (batteryNum.value >= 5) {
        return 'danger'
    } else {
        return 'danger'
    }
});

let myInterval = null;

const handeRecharge = () => {
    clearInterval(myInterval);
    batteryNum.value = 0;
    if (props.proIsCharge) {
        myInterval = setInterval(() => {
            drawCharge();
        }, 500);
    }
};

const drawCharge = () => {
    batteryNum.value += 20;
    if (batteryNum.value > 100) {
        batteryNum.value = 0;
    }
};

onMounted(async () => {
    // 获取电量
    await useSSEStore().setBattery();
    // 监听SSE事件
    $bus.on('batterySSE', (data) => {
        if (Number(data.charge) >= 0) {
            batteryNum.value = (data.charge * 1000) / 10;
        }
        proIsCharge.value = Number(data.status) ? true : false;
    });
    // if (props.proIsCharge) {
    //     handeRecharge();
    // }
});

onBeforeUnmount(() => {
    if (myInterval) {
        clearInterval(myInterval);
    }
    // 断开连接
    useSSEStore().disconnectSSE('batterySource')
});
</script>

<style lang="scss" scoped>
// custom theme color
$color-primary: #447ced;
$color-success: #13ce66;
$color-warning: #ffba00;
$color-danger: #ff4949;
$color-info: #909399;
$color-white: #fff;

@mixin panel-style($color) {
    .panel {
        border-color: $color;

        &::before {
            background: $color;
        }

        .remainder {
            background: $color;
        }
    }

    .text {
        color: $color;
    }
}

.electric-panel {
    display: flex;
    justify-content: center;
    align-items: center;

    &.primary {
        @include panel-style($color-primary);
    }

    &.success {
        @include panel-style($color-success);
    }

    &.warning {
        @include panel-style($color-warning);
    }

    &.danger {
        @include panel-style($color-danger);
    }

    .panel {
        box-sizing: border-box;
        width: 22px;
        height: 12px;
        position: relative;
        border: 1px solid #ccc;
        padding: 1px;
        border-radius: 2px;

        &::before {
            content: "";
            border-radius: 0 1px 1px 0;
            height: 4px;
            background: #ccc;
            width: 2px;
            position: absolute;
            top: 50%;
            right: -4px;
            transform: translateY(-50%);
        }

        .remainder {
            border-radius: 1px;
            position: relative;
            height: 100%;
            width: 0%;
            left: 0;
            top: 0;
            background: #fff;
        }
    }

    .text {
        text-align: left;
        width: auto;
        font-size: 14px;
        line-height: 14px;
    }
}
</style>