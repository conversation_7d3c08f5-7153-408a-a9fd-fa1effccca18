<template>
  <div class='echart' ref="chartDom"></div>
</template>

<script setup lang='ts' scoped>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts';
import $bus from "@/utils/bus.ts";

const chartDom = ref(null)
const myChart = ref(null);
const props = defineProps({
  options: {
    type: Object,
    default: {},
    required: true
  },
})
const resizeHandler = () => {
  myChart.resize()
}

const updateOptions = (newOptions: any) => {
  myChart.value.setOption(newOptions)
}

onMounted(() => {
  myChart.value = echarts.init(chartDom.value);
  myChart.value.setOption(props.options, true);
  window.addEventListener('resize', resizeHandler);

  //第一个参数:即为事件类型  第二个参数:即为事件回调
  $bus.on("waveForm", (data) => {
    console.log('waveForm', data);
    updateOptions(data)
  });
})
watch(() => props.options, (newOptions) => {
  console.log('newOptions', newOptions)
  updateOptions(newOptions)
}, {
  deep: true
})

// 当组件卸载时触发
onUnmounted(() => {
  //在组件卸载的时候，手动解绑定义的绑定事件
  $bus.off('waveForm');
  //解绑所有的事件
  // $bus.all.clear()
});
</script>

<style lang='scss' scoped>
.echart {
  width: 100%;
  height: 100%;
}
</style>