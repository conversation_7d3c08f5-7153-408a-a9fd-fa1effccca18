<template>
    <div class="keyboard">
        <div v-for="row in keyboardLayout" :key="row.id" class="row">
            <button v-for="key in row" :key="key" @click="handleKeyPress(key)">
                {{ key }}
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';

const emit = defineEmits(['input']);
const keyboardLayout = ref([
    ['1', '2', '3'],
    ['4', '5', '6'],
    ['7', '8', '9'],
    ['删除', '0', '确认']
]);

const handleKeyPress = (key) => {
    if (key === '删除') {
        emit('input', 'backspace'); // 处理删除键的逻辑，例如发送到父组件处理删除操作
    } else if (key === '确认') {
        emit('input', 'enter'); // 发送按键值到父组件处理输入
    } else {
        emit('input', key); // 发送按键值到父组件处理输入
    }
};
</script>

<style scoped>
.keyboard {
    /* display: grid; */
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.row {
    display: flex;
    /* justify-content: space-between; */
    justify-content: center;
}

button {
    width: 8vw;
    padding: 10px;
    font-size: 16px;
}
</style>