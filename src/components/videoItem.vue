<template>
	<div class="player-container">
		<video ref="videoRef" class="video-js"></video>
		<div class="slideshow" v-if="showSlideshow">
			<img :src="currentImage" alt="Slideshow Image" />
		</div>
	</div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref, watch, reactive, computed, defineEmits } from "vue";
import mp4Url from '../assets/video/demo.mp4';
import mp3Url from '../assets/video/relax.mp3';
import Videojs from 'video.js';
import 'video.js/dist/video-js.min.css';
import { useRoute } from "vue-router";
// import "video.js/dist/video-js.css";
import $bus from "@/utils/bus.ts"
import relaxImg01 from "../assets/images/relax_img/1.png";
import relaxImg02 from "../assets/images/relax_img/2.png";
import relaxImg03 from "../assets/images/relax_img/3.png";
import relaxImg04 from "../assets/images/relax_img/4.png";
import relaxImg05 from "../assets/images/relax_img/5.png";
import relaxImg06 from "../assets/images/relax_img/6.png";
import relaxImg07 from "../assets/images/relax_img/7.png";
import relaxImg08 from "../assets/images/relax_img/8.png";
import relaxImg09 from "../assets/images/relax_img/9.png";
import relaxImg10 from "../assets/images/relax_img/10.png";
import relaxImg11 from "../assets/images/relax_img/11.png";
import relaxImg12 from "../assets/images/relax_img/12.png";
import relaxImg13 from "../assets/images/relax_img/13.png";

const route = useRoute();

// 添加轮播图片数组
const images = [
	relaxImg01,
	relaxImg02,
	relaxImg03,
	relaxImg04,
	relaxImg05,
	relaxImg06,
	relaxImg07,
	relaxImg08,
	relaxImg09,
	relaxImg10,
	relaxImg11,
	relaxImg12,
	relaxImg13,
];
const currentImageIndex = ref(0);
const currentImage = computed(() => images[currentImageIndex.value]);
const showSlideshow = ref(true);
let slideshowInterval: number;


const videoRef = ref(null);
const videoInstance = ref(null); //提高性能
const emit = defineEmits(['start']);


const options = {
	sources: [
		// 两种播放格式
		// {
		// 	type: 'video/mp4',
		// 	src: mp4Url,
		// },
		{
			type: 'audio/mp3',
			src: mp3Url,
		},
	],
	// 视频播放器的配置项
	// aspectRatio: '16:9', // 播放区域显示比例
	// notSupportedMessage: '此视频暂无法播放，请稍后再试', //允许覆盖Video.js无法播放媒体源时显示的默认信息。
	// autoplay: true, //是否自动播放
	// loop: true, //是否循环播放
	// controls: true, //控制器
	// preload: true, //是否预加载
	// language: 'zh-CN', //显示的语言 中文
	// playbackRates: [0.5, 1, 1.5, 2], //播放速度
	// poster: '', //封面图
	// currentTimeDisplay: true, //是否显示当前时间
	// durationDisplay: true, //是否显示总时长
	// remainingTimeDisplay: false, //是否显示剩余时间
	// controlBar: {
	// 	//控制条
	// 	playToggle: true, //播放/暂停按钮
	// 	fullscreenToggle: true, //全屏按钮
	// 	volumeMenuButton: true, //音量按钮
	// 	remainingTimeDisplay: true, //剩余时间
	// },
	// 音频播放器配置项
	controls: true,
	autoplay: true,
	loop: true,
	preload: 'auto',
	height: 50, // 音频播放器高度
};

/**
 * 获取当前音量
 */
const getVolume = (): number => {
	return videoInstance.value ? videoInstance.value.volume() : 0;
}

// 添加轮播逻辑
const startSlideshow = () => {
	slideshowInterval = setInterval(() => {
		currentImageIndex.value = (currentImageIndex.value + 1) % images.length;
		// 获取当前音量
		let currentVolume = getVolume();
		console.log('获取当前音量', currentVolume);
	}, 10000); // 每10秒切换一次图片
};

/**
 * 初始化video
 */
const initVideo = () => {
	if (!videoRef.value) return;
	// Videojs第一个参数可以使video标签的id或者目标标签元素，第二个参数为配置项。
	videoInstance.value = Videojs(videoRef.value, options);
	startSlideshow();
	setTimeout(() => {
		emit('start'); // 播放开始
	}, route.query.todoType == 'MAT' ? 120000 : 130000); // 300秒后触发
};

/**
 * 播放video
 */
const playVideo = () => {
	videoInstance.value.play();
}

/**
 * 暂停video
 */
const pauseVideo = () => {
	videoInstance.value.pause();
}

/**
 * 重置当前播放时间video
 */
const currentTime = (second = 0) => {
	videoInstance.value.currentTime(second);
}

onMounted(() => {
	initVideo();
	//第一个参数:即为事件类型  第二个参数:即为事件回调
	$bus.on("videoEvent", (action) => {
		console.log('videoEvent', action);
		let { type } = action; // 1-播放，2-暂停，3-重置
		switch (type) {
			case 1:
				playVideo();
				break;
			case 2:
				pauseVideo();
				break;
			case 3:
				currentTime(); // 重置当前播放时间为0
				break;
		}
	});
});

onUnmounted(() => {
	if (videoInstance.value) {
		videoInstance.value.dispose(); // 销毁video.js实例
	};
	clearInterval(slideshowInterval); // 清除轮播定时器
})
</script>

<style lang="scss" scoped>
.player-container {
	position: relative;
	width: 100%;
	height: 100%;
}

.video-js {
	width: 100%;
	height: 0px; // 音频播放器高度
	display: none
}

.slideshow {
	width: 100%;
	height: calc(100% - 50px);
	overflow: hidden;

	img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}
</style>