import { ref, onMounted, onUnmounted } from 'vue';

export default {
  mounted(el, binding) {
    const pressDuration = binding.value || 2000; // 默认2秒
    let pressTimer = null;
    
    // 处理长按开始
    const handlePressStart = () => {
      pressTimer = setTimeout(() => {
        // 使用预加载脚本暴露的API
        window.electronAPI.showKeyboard();
        
        // 聚焦当前元素
        if (el.tagName === 'INPUT' || el.tagName === 'TEXTAREA' || el.isContentEditable) {
          el.focus();
        }
      }, pressDuration);
    };
    
    // 处理长按取消
    const handlePressEnd = () => {
      clearTimeout(pressTimer);
    };
    
    // 添加触摸和鼠标事件监听
    el.addEventListener('touchstart', handlePressStart);
    el.addEventListener('touchend', handlePressEnd);
    el.addEventListener('touchcancel', handlePressEnd);
    el.addEventListener('mousedown', handlePressStart);
    el.addEventListener('mouseup', handlePressEnd);
    el.addEventListener('mouseleave', handlePressEnd);
    
    // 组件卸载时移除事件监听
    onUnmounted(() => {
      el.removeEventListener('touchstart', handlePressStart);
      el.removeEventListener('touchend', handlePressEnd);
      el.removeEventListener('touchcancel', handlePressEnd);
      el.removeEventListener('mousedown', handlePressStart);
      el.removeEventListener('mouseup', handlePressEnd);
      el.removeEventListener('mouseleave', handlePressEnd);
    });
  }
};