<template>
	<div>
		<a-flex :style="{ ...boxStyle }" justify="space-around" align="center" gap="middle">
			<div :style="{ ...footerBoxStyle }" vertical v-for="item in btnList" :key="item.label"
				class="layout_footer_btn">
				<a-row :gutter="20" @click="item.event">
					<a-col :span="24" style="margin-top: 10px;">
						<a-image :width="'3.2vw'" :height="'3.2vw'" :src="item.imgSrc" :preview="false"
							fallback="data:image/png;base64,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" />
					</a-col>
					<a-col :span="24" style="margin-top: 10px;">{{ item.label }}</a-col>
				</a-row>
			</div>
		</a-flex>
		<a-modal v-model:open="warnVisable" width="95vw" class="warnConfig-dialog" title="报警设置"
			@ok="handleWarnConfigSubmit" okText="提交" cancelText="取消" :okButtonProps="okButtonProps">
			<warn-config ref="warningRef" @close="closeWarnConfig" />
		</a-modal>

		<a-modal v-model:open="signVisable" width="50vw" class="warnConfig-dialog" title="心电模块设置" @ok="handleSignSubmit"
			okText="提交" cancelText="取消" :okButtonProps="okButtonProps">
			<sign-config ref="signRef" @close="closeSignConfig" />
		</a-modal>

		<a-modal v-model:open="logoutVisable" title="待机注销" @ok="handleLogout">
			<template #footer>
				<a-button key="back" @click="handleWakeUp">待机</a-button>
				<a-button key="submit" type="primary" :loading="loadingLogout" @click="handleLogout">登出</a-button>
			</template>
			<p>请选择息屏待机或者登出当前账户</p>
		</a-modal>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { CSSProperties } from 'vue';
import { useRouter } from "vue-router";
import { userMessageStore } from "@/stores/modules/userMessage";
import WarnConfig from '@/layouts/components/warnConfigIndex.vue';
import SignConfig from '@/layouts/components/vitalSignsConfig.vue';
import settingIcon from '@/assets/images/foot_btn/settings.png';
import warnIcon from '@/assets/images/foot_btn/warning.png';
import quietIcon from '@/assets/images/foot_btn/quiet.png';
import waitIcon from '@/assets/images/foot_btn/waiting.png';
import configIcon from '@/assets/images/foot_btn/warnConfig.png';
import handIcon from '@/assets/images/foot_btn/handle.png';
import screenIcon from '@/assets/images/foot_btn/screen.png';
import { backlightSleep, audioMute } from '@/api/modules/device';

const router = useRouter();
const userStore = userMessageStore();

const boxStyle: CSSProperties = {
	width: '100%',
	height: '10vh',
	borderRadius: '6px',
	// border: '1px solid #40a9ff',
};

const warnVisable = ref<boolean>(false);
const warningRef = ref<InstanceType<typeof WarnConfig>>();
const signRef = ref<InstanceType<typeof SignConfig>>();
const signVisable = ref<boolean>(false);

const logoutVisable = ref<boolean>(false);
const loadingLogout = ref<boolean>(false);

// 样式编辑
const footerBoxStyle: CSSProperties = {
	cursor: 'pointer',
	width: '14vw',
	height: '13vh',
	borderRadius: '9%',
	color: '#fff',
	backgroundColor: '#00A6B0',
	fontSize: '1.25rem',
	fontFamily: 'cursive',
	fontWeight: 'bolder',
	letterSpacing: '3px',
	border: '6px double #fff',
};
const okButtonProps = {
	size: 'large'
}

const handleBtnClick = function () { }

// 设置
const handleSettings = function () {
	console.log('设置')
	router.push('/settings')
}

// 报警设置
const handleWarnConfig = function () {
	console.log('报警设置')
	// router.push('/warnConfig')
	warnVisable.value = true;
	warningRef.value?.getConfigDetailsData();
}

// 体征设置
const handleSignConfig = function () {
	console.log('体征设置')
	signVisable.value = true;
}
// 校准
const handleCalibration = function () {
	console.log('校准');
	// router.push('/waveform')
}

// 主屏幕
const handleGoBack = function () {
	console.log('主屏幕')
	router.replace('/')
}

// 待机按钮 
const handleLogoutOpen = () => {
	logoutVisable.value = true;
};

const handleLogout = () => {
	loadingLogout.value = true;
	setTimeout(() => {
		loadingLogout.value = false;
		logoutVisable.value = false;
		userStore.logout();
	}, 2000);
};

const handleWakeUp = async () => {
	logoutVisable.value = false;
	await backlightSleep(null);
};

// 静音
const handleVoiceQuiet = async () => {
	await audioMute(null);
}

// 按钮列表
const btnList = ref([
	{ label: '设置', imgSrc: settingIcon, event: handleSignConfig },
	{ label: '暂停报警', imgSrc: warnIcon, event: handleBtnClick },
	{ label: '静音', imgSrc: quietIcon, event: handleVoiceQuiet },
	{ label: '待机', imgSrc: waitIcon, event: handleLogoutOpen },
	{ label: '报警设置', imgSrc: configIcon, event: handleWarnConfig },
	{ label: '手动校准', imgSrc: handIcon, event: handleCalibration },
	{ label: '主屏幕', imgSrc: screenIcon, event: handleGoBack }
])

// 报警设置提交
const handleWarnConfigSubmit = (e: MouseEvent) => {
	console.log(e);
	warningRef.value?.submitConfigDetailsData();
	// warnVisable.value = false;
};
const closeWarnConfig = () => {
	warnVisable.value = false;
}

// 体征设置提交
const handleSignSubmit = (e: MouseEvent) => {
	console.log(e);
	signRef.value?.submitConfigDetailsData();
}
const closeSignConfig = () => {
	signVisable.value = false;
}

</script>

<style>
.layout_footer_btn:active {
	transform: scale(1.05);
}

.warnConfig-dialog {
	top: 5vh;
}
</style>