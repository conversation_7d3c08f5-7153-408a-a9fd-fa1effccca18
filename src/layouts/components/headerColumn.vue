<template>
	<a-row :gutter="20">
		<a-col :span="9">
			<a-flex align="center" justify="space-between">
				<div>
					<a-image :width="40" :height="40" :src="logoImg" :preview="false"
						:fallback="settingInfo.noneImgBase64" />&nbsp;<span>【医院名称】</span>
				</div>
				<span style="font-size: 1rem;color: #00A6B0;" v-if="deviceInfo.deviceNo">设备编号：{{ deviceInfo.deviceNo ||
		'--'
					}}</span>
			</a-flex>

		</a-col>
		<a-col :span="6">
			<a-flex justify="center" align="center" :style="{ ...titleCss }">心率变异采集分析系统</a-flex>
		</a-col>
		<a-col :span="9">
			<a-row :gutter="20">
				<!-- <a-col :span="7" style="font-size: 1rem;"></a-col> -->
				<a-col :span="8" class="clock_css">
					{{ `${clockStr}` }}
				</a-col>
				<a-col :span="16" class="icon_font_css">
					<div class="header_state_css">
						<div><a-image :width="25" :height="15" :src="deviceIcon01" :preview="false"
								:fallback="settingInfo.noneImgBase64" /></div>
						<div><a-image :width="25" :height="15" :src="daolianIcon01" :preview="false"
								:fallback="settingInfo.noneImgBase64" /></div>
						<div><a-image :width="25" :height="15" :src="serverIcon01" :preview="false"
								:fallback="settingInfo.noneImgBase64" /></div>
						<BatteryItem :proIsCharge="false" />
					</div>
				</a-col>
			</a-row>
		</a-col>
	</a-row>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from "vue";
import type { CSSProperties } from 'vue';
import moment from 'moment';
import settingInfo from '@/settings'
import logoImg from '@/assets/images/logo.png';
import deviceIcon01 from '@/assets/images/header_btn/device_1.png'
import daolianIcon01 from '@/assets/images/header_btn/daolian_1.png'
import serverIcon01 from '@/assets/images/header_btn/server_1.png'
import BatteryItem from "@/components/batteryItem.vue"

import { userMessageStore } from '@/stores/modules/userMessage'
const userStore = userMessageStore()

const titleCss: CSSProperties = {
	textAlign: 'center',
	color: '#000',
	fontSize: '1.35rem',
	fontWeight: 'bold',
	fontFamily: 'cursive'
};

const deviceInfo = computed(() => userStore.deviceGet);

const timer = ref(null);
const deviceId = ref('12');
const clockStr = ref<string>('');

const swiper = function () {
	if (timer.value) return false;
	timer.value = setInterval(() => {
		let time = new Date();
		clockStr.value = moment(time).format('HH:mm:ss');
	}, 1000)
}

// 监听
watch(
	() => clockStr.value,
	(newValue, oldValue) => {
		// console.log('clockStr.value', newValue, oldValue)
	},
	{ immediate: true }
);

onMounted(() => {
	swiper();
})

onUnmounted(() => {
	clearInterval(timer.value);
	timer.value = null
});
</script>

<style lang="scss">
.clock_css {
	font-size: 1.25rem;
	font-weight: bold;
}

.icon_font_css {
	color: #00A6B0;
	font-weight: 500;
	font-family: '思源黑体';

	.header_state_css {
		display: flex;
		width: 80%;
		line-height: 9vh;
		justify-content: space-between;
		align-items: center;
		float: right;
	}
}
</style>