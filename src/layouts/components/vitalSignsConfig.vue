<template>
    <div style="overflow: hidden;">
        <br>
        <a-form :model="signForm" name="signForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }"
            autocomplete="off" class="warn_config_form">
            <a-form-item label="ST模板">
                <a-flex align="center" justify="space-around">
                    ISO<a-input-number id="inputNumber" v-model:value="signForm.ecgStTemplateIso" :min="-50"
                        :max="-20" />ms
                    &nbsp;&nbsp;丨&nbsp;&nbsp;
                    ST<a-input-number id="inputNumber" v-model:value="signForm.ecgStTemplateSt" :min="20" :max="50" />ms
                </a-flex>
            </a-form-item>
            <a-form-item label="陷波模式">
                <a-select v-model:value="signForm.ecgNotchMode" :options="notchOptions"></a-select>
            </a-form-item>
            <a-form-item label="心率失常通道">
                <a-select v-model:value="signForm.ecgArrhythmiaChannel">
                    <a-select-option :value="0">I通道</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="上电滤波">
                <a-select v-model:value="signForm.ecgPowerFiltering" :options="modelOptions"></a-select>
            </a-form-item>
            <a-form-item label="增益设置">
                <a-select v-model:value="signForm.ecgGain" :options="gainOptions"></a-select>
            </a-form-item>
            <a-form-item label="病人模式">
                <a-select v-model:value="signForm.ecgPatientModel" :options="patientOptions"></a-select>
            </a-form-item>
            <a-form-item label="导联模式">
                <a-select v-model:value="signForm.ecgLeadModel" disabled>
                    <a-select-option :value="0">3导联</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="导联通道">
                <a-select v-model:value="signForm.ecgLeadChannel" disabled>
                    <a-select-option :value="0">I通道</a-select-option>
                </a-select>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineEmits } from 'vue';
import { getConfigDetails, ecgConfigUpdate } from '@/api/modules/device';

const EMITS = defineEmits(['close']);

const signForm = ref({
    "ecgStTemplateIso": 0,
    "ecgStTemplateSt": 0,
    "ecgNotchMode": 0,
    "ecgArrhythmiaChannel": 0,
    "ecgPowerFiltering": 0,
    "ecgGain": 0,
    "ecgPatientModel": 0,
    "ecgLeadModel": 0,
    "ecgLeadChannel": 0
});

const notchOptions = [
    { value: 0, label: '50hz' },
    { value: 1, label: '60hz' },
    { value: 2, label: '50/60hz' },
    { value: 3, label: '无陷波' }
]

const modelOptions = [
    { value: 0, label: '诊断模式' },
    { value: 1, label: '监护滤波模式' },
    { value: 2, label: 'HARDEST滤波模式' },
    { value: 3, label: '手术滤波模式' }
]

const gainOptions = [
    { value: 0, label: 'x250' },
    { value: 1, label: 'x500' },
    { value: 2, label: 'x1000' },
    { value: 3, label: 'x2000' },
]

const patientOptions = [
    { value: 0, label: '成人' },
    { value: 1, label: '儿童' },
    { value: 2, label: '新生儿' },
]

const getConfigDetailsData = async () => {
    let res = await getConfigDetails();
    if (res.code == 200) {
        signForm.value = { ...res.data };
    } else {
        console.log(res.msg);
    }
}

const submitConfigDetailsData = async () => {
    let res = await ecgConfigUpdate(signForm.value);
    if (res.code == 200) {
        EMITS('close');
    } else {
        console.log(res.msg);
    }
}

onMounted(() => {
    getConfigDetailsData();
})

defineExpose({ submitConfigDetailsData, getConfigDetailsData })

</script>

<style lang="scss">
.warn_config_header {
    padding: 0 3vw;
    color: #000;
    height: 5vh;
}

.warn_config_form {
    font-size: 1rem;
    font-family: emoji;
    text-align: left;

    .warn_config_input {
        width: 10vw
    }
}
</style>