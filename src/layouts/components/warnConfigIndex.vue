<template>
	<div style="overflow: hidden;">
		<!-- <a-flex justify="space-between" align="center" class="warn_config_header">
			<h1>报警设置</h1>
			<a-button type="primary" size="large" @click="handleToListPage">返回</a-button>
		</a-flex> -->
		<a-row :gutter="16" class="warn_config_grid">
			<a-col :span="12">
				<a-card title="心电报警监测" :bordered="false" class="warn_config_card">
					<a-form :model="warnConfig" name="warnConfig" layout="vertical" autocomplete="off"
						class="warn_config_form">
						<a-form-item label="成人：">
							上限 <a-input-number :precision="0" :min="0"
								v-model:value="warnConfig.alarmEcgHeartRateAdultUpper" class="warn_config_input" />
							pbm
							&nbsp;&nbsp;&nbsp;&nbsp;
							下限 <a-input-number :precision="0" :min="0"
								v-model:value="warnConfig.alarmEcgHeartRateAdultLower" class="warn_config_input" />
							pbm
						</a-form-item>
						<a-form-item label="儿童：">
							上限 <a-input-number :precision="0" :min="0"
								v-model:value="warnConfig.alarmEcgHeartRateChildUpper" class="warn_config_input" />
							pbm
							&nbsp;&nbsp;&nbsp;&nbsp;
							下限 <a-input-number :precision="0" :min="0"
								v-model:value="warnConfig.alarmEcgHeartRateChildLower" class="warn_config_input" />
							pbm
						</a-form-item>
						<a-form-item label="老年及冠心病：">
							上限 <a-input-number :precision="0" :min="0"
								v-model:value="warnConfig.alarmEcgHeartRateOldUpper" class="warn_config_input" /> pbm
							&nbsp;&nbsp;&nbsp;&nbsp;
							下限 <a-input-number :precision="0" :min="0"
								v-model:value="warnConfig.alarmEcgHeartRateOldLower" class="warn_config_input" /> pbm
						</a-form-item>
					</a-form>
				</a-card>
			</a-col>
			<!-- <a-col :span="12">
				<a-card title="心电过缓" :bordered="false" class="warn_config_card">
					<a-form :model="warnConfig" name="warnConfig" layout="vertical" autocomplete="off"
						class="warn_config_form">
						<a-form-item label="成人：">
							上限 <a-input-number :precision="0" :min="0"
								v-model:value="warnConfig.alarmEcgBradycardiaAdultUpper" class="warn_config_input" />
							pbm
							&nbsp;&nbsp;&nbsp;&nbsp;
							下限 <a-input-number :precision="0" :min="0"
								v-model:value="warnConfig.alarmEcgBradycardiaAdultLower" class="warn_config_input" />
							pbm
						</a-form-item>
						<a-form-item label="儿童：">
							上限 <a-input-number :precision="0" :min="0"
								v-model:value="warnConfig.alarmEcgBradycardiaChildUpper" class="warn_config_input" />
							pbm
							&nbsp;&nbsp;&nbsp;&nbsp;
							下限 <a-input-number :precision="0" :min="0"
								v-model:value="warnConfig.alarmEcgBradycardiaChildLower" class="warn_config_input" />
							pbm
						</a-form-item>
						<a-form-item label="老年及冠心病：">
							上限 <a-input-number :precision="0" :min="0"
								v-model:value="warnConfig.alarmEcgBradycardiaOldUpper" class="warn_config_input" /> pbm
							&nbsp;&nbsp;&nbsp;&nbsp;
							下限 <a-input-number :precision="0" :min="0"
								v-model:value="warnConfig.alarmEcgBradycardiaOldLower" class="warn_config_input" /> pbm
						</a-form-item>
					</a-form>
				</a-card>
			</a-col> -->
			<a-col :span="12">
				<a-card title="纤维性颤动" :bordered="false" class="warn_config_card_1">
					<a-form :model="warnConfig" name="warnConfig" layout="inline" autocomplete="off"
						class="warn_config_form">
						<a-form-item label="成人">
							<a-radio-group v-model:value="warnConfig.alarmFibroticTremorAdult" button-style="solid">
								<a-radio-button :value="0">关闭</a-radio-button>
								<a-radio-button :value="1">开启</a-radio-button>
							</a-radio-group>
						</a-form-item>
						<a-form-item label="儿童">
							<a-radio-group v-model:value="warnConfig.alarmFibroticTremorChild" button-style="solid">
								<a-radio-button :value="0">关闭</a-radio-button>
								<a-radio-button :value="1">开启</a-radio-button>
							</a-radio-group>
						</a-form-item>
					</a-form>
				</a-card>
				<br />
				<a-card title="低电报警" :bordered="false" class="warn_config_card_1">
					<a-form :model="warnConfig" name="warnConfig" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }"
						autocomplete="off" class="warn_config_form">
						<a-form-item label="电量低于">
							<a-input-number :precision="0" :min="0" v-model:value="warnConfig.alarmBatteryLow"
								class="warn_config_input" />%
						</a-form-item>
					</a-form>
				</a-card>
			</a-col>
			<!-- <a-col :span="12">
				<a-card title="低电报警" :bordered="false" class="warn_config_card_1">
					<a-form :model="warnConfig" name="warnConfig" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }"
						autocomplete="off" class="warn_config_form">
						<a-form-item label="电量低于">
							<a-input-number :precision="0" :min="0" :value="warnConfig.alarmBatteryLow"
								class="warn_config_input" />%
						</a-form-item>
					</a-form>
				</a-card>
			</a-col> -->
		</a-row>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineEmits } from 'vue';
import { useRouter } from "vue-router";
import { getConfigDetails, alarmConfigUpdate } from '@/api/modules/device';

const router = useRouter();
const EMITS = defineEmits(['close']);

const handleToListPage = function () {
	router.go(-1)
}

const warnConfig = ref({
	// alarmEcgTachycardiaAdultUpper: null, // 心电过速-成人-上限	
	// alarmEcgTachycardiaAdultLower: null, // 心电过速-成人-下限
	// alarmEcgTachycardiaChildUpper: null, // 心电过速-儿童-上限
	// alarmEcgTachycardiaChildLower: null, // 心电过速-儿童-下限
	// alarmEcgTachycardiaOldUpper: null, // 心电过速-老年及冠心病-上限
	// alarmEcgTachycardiaOldLower: null, // 心电过速-老年及冠心病-下限
	// alarmEcgBradycardiaAdultUpper: null, // 心电过缓-成人-上限
	// alarmEcgBradycardiaAdultLower: null, // 心电过缓-成人-下限
	// alarmEcgBradycardiaChildUpper: null, // 心电过缓-儿童-上限
	// alarmEcgBradycardiaChildLower: null, // 心电过缓-儿童-下限
	// alarmEcgBradycardiaOldUpper: null, // 心电过缓-老年及冠心病-上限
	// alarmEcgBradycardiaOldLower: null, // 心电过缓-老年及冠心病-下限
	alarmEcgHeartRateAdultUpper: null, // 成人-心率上限
	alarmEcgHeartRateAdultLower: null, // 成人-心率下限
	alarmEcgHeartRateChildUpper: null, // 儿童-心率上限
	alarmEcgHeartRateChildLower: null, // 儿童-心率下限
	alarmEcgHeartRateOldUpper: null, // 老年及冠心病-心率上限
	alarmEcgHeartRateOldLower: null, // 老年及冠心病-心率下限
	alarmFibroticTremorAdult: 0, // 纤维性颤动-成人
	alarmFibroticTremorChild: 0, // 纤维性颤动-儿童
	alarmBatteryLow: 0, // 低电报警
});

const getConfigDetailsData = async () => {
	let res = await getConfigDetails();
	if (res.code == 200) {
		warnConfig.value = { ...res.data, alarmBatteryLow: res.data.alarmBatteryLow * 100 };
	} else {
		console.log(res.msg);
	}
}

const submitConfigDetailsData = async () => {
	warnConfig.value.alarmBatteryLow = warnConfig.value.alarmBatteryLow / 100;
	let res = await alarmConfigUpdate(warnConfig.value);
	if (res.code == 200) {
		EMITS('close');
	} else {
		console.log(res.msg);
	}
}

onMounted(() => {
	getConfigDetailsData();
})

defineExpose({ submitConfigDetailsData, getConfigDetailsData })
</script>

<style lang="scss">
.warn_config_header {
	padding: 0 3vw;
	color: #000;
	height: 5vh;
}

.warn_config_grid {
	height: 50vh;
	padding: 1vh 3vw;

	.warn_config_card {
		height: 46vh;
	}

	.warn_config_card_1 {
		height: 20vh;
	}

	.warn_config_card .ant-card-head {
		background-color: #00a6b0 !important;
		color: #fff !important;
	}

	.warn_config_card_1 .ant-card-head {
		background-color: #00a6b0 !important;
		color: #fff !important;
	}

	.warn_config_form {
		font-size: 1rem;
		font-family: emoji;
		text-align: left;

		.warn_config_input {
			width: 10vw
		}
	}

}
</style>