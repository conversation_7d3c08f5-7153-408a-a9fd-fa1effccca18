<template>
  <a-space direction="vertical" :style="{ ...containerCss }" :size="[0, 48]">
    <a-layout>
      <a-layout-header :style="headerStyle">
        <HeaderColumn />
      </a-layout-header>
      <a-layout-content :style="contentStyle">
        <RouterView v-slot="{ Component, route }">
          <Transition name="fade-transform" mode="out-in">
            <KeepAlive :include="keepAliveStore.list">
              <component :is="Component" :key="route.fullPath" />
            </KeepAlive>
          </Transition>
        </RouterView>
      </a-layout-content>
      <a-layout-footer :style="footerStyle">
        <FooterBtn />
      </a-layout-footer>
    </a-layout>
  </a-space>
</template>

<script lang="ts" setup>
import type { CSSProperties } from 'vue';
import useKeepAliveStore from '../stores/modules/keepAlive'
import { ref, reactive, onBeforeMount } from "vue";
import { useRouter, useRoute } from 'vue-router';
import FooterBtn from './components/footBtn.vue'
import HeaderColumn from './components/headerColumn.vue'
import { userMessageStore } from '@/stores/modules/userMessage'
const userStore = userMessageStore()

const router = useRouter()
const token = sessionStorage.getItem('token')

const containerCss: CSSProperties = {
  width: '100%',
  height: '100%'
}



onBeforeMount(() => {
  // 登录校验
  // console.log(!token)
  if (!token) return router.replace('/login');

  userStore.initWebSocket();

  // 获取窗口高度
  // let windowHeight = document.documentElement.clientHeight;
  // console.log('windowHeight->', windowHeight);
  // containerCss.value.height = windowHeight;
})

const keepAliveStore = useKeepAliveStore()

const headerStyle: CSSProperties = {
  textAlign: 'center',
  color: '#000',
  height: '8vh',
  paddingInline: 50,
  lineHeight: '64px',
  backgroundColor: '#fff',
  paddingInline: '10px'
};

const contentStyle: CSSProperties = {
  textAlign: 'center',
  height: '78vh',
  lineHeight: '120px',
  color: '#fff',
  backgroundColor: '#F1FCFD',
};

const footerStyle: CSSProperties = {
  textAlign: 'center',
  color: '#fff',
  height: '14vh',
  backgroundColor: '#fff',
  paddingTop: '12px'
};
</script>