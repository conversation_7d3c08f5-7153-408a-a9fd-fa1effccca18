import { app, BrowserWindow, ipc<PERSON>ain, Menu, session, win, shell } from 'electron'
import path from 'node:path'
// const started = require('electron-squirrel-startup')
// const log = require('electron-log')
import started from 'electron-squirrel-startup'
import log from 'electron-log'

// const baseUrl = 'http://127.0.0.1:8080'
const baseUrl = 'http://************:8080'
// const baseUrl = window.webConfig.SERVER_API_URL || 'http://127.0.0.1:8080'

app.commandLine.appendSwitch('ignore-certificate-errors'); // 忽略证书错误（仅开发环境）

// 初始化日志记录
log.initialize()

// 使用日志
// log.debug('这是一条调试信息')
// log.info('这是一条普通信息')
// log.warn('这是一个警告')
// log.error('这是一个错误')

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (started) {
  app.quit()
}

//禁止多开 点击打开原来应用-防止electron-squirrel-startup失效
const gotTheLock = app.requestSingleInstanceLock()
if (!gotTheLock) {
  app.quit()
}

const createWindow = () => {

  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1024,
    height: 768,
    title: '心率变异采集分析',
    fullscreen: true, // 设置为全屏
    // icon: path.join(__dirname, './favicon.ico'),
    icon: path.join(__dirname, 'logo.png'), // 根据平台选择图标文件
    webPreferences: {
      sandbox: false, // 开启沙盒则preload脚本被禁用，所以得设为false
      // 在页面运行其他脚本之前预先加载指定的脚本
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: true, // 注意：根据 Electron 的安全建议，最好使用 contextIsolation, nodeIntegrationInWorker 等替代方案
      contextIsolation: true, // 这将把 Electron 和渲染进程的上下文隔离。
      webSecurity: false, // This is important for SSL to work
      // enableRemoteModule: true, // 如果需要的话，启用 remote 模块
      hardwareAcceleration: false, // 禁用硬件加速
      // devTools: false // 生产环境禁用 DevTools
      // 禁用不必要功能
      spellcheck: false,
      plugins: false,
      experimentalFeatures: false,
      // 渲染优化
      disableBlinkFeatures: 'CSSSmoothScroll,ScrollAnchoring',
      enableDirectWrite: true,
      // 内存管理
      nodeIntegrationInWorker: true,
      additionalArguments: [
        '--disable-http-cache',
        '--max-old-space-size=1024' // 1GB堆内存上限
      ]
    },
  })

  // Menu.setApplicationMenu(null) // 禁用菜单栏

  // and load the index.html of the app.
  if (MAIN_WINDOW_VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(MAIN_WINDOW_VITE_DEV_SERVER_URL)
  } else {
    mainWindow.loadFile(
      path.join(__dirname, `../renderer/${MAIN_WINDOW_VITE_NAME}/index.html`)
    )
  }

  // 默认打开DevTools.
  // mainWindow.webContents.openDevTools()
}

// app.whenReady().then(createWindow);
let storedCookie = ''; // 用于存储提取的 cookie
// app模块准备完成，创建窗口
app.whenReady().then(() => {
  ipcMain.handle('__dirname', () => __dirname)
  // electron升级到20版本后，禁用第三方cookie、跨域问题解决方法
  // 登录请求路径 
  const loginFilter = {
    urls: [baseUrl + '/api/login/login']
  };
  // 监听响应并提取 Set-Cookie  
  session.defaultSession.webRequest.onCompleted(loginFilter, (details) => {
    const setCookieHeader = details.responseHeaders['Set-Cookie'];
    if (setCookieHeader) {
      // 提取 cookie  
      storedCookie = setCookieHeader.join('; '); // 将多个 cookie 合并为一个字符串  
      console.log('Stored Cookie:', storedCookie);
    }
  });

  // 需要设置cookie字段的请求路径
  const requestsFilter = {
    urls: [baseUrl + '/*']
  };
  // 为需要的请求添加请求头中的cookie字段
  session.defaultSession.webRequest.onBeforeSendHeaders(requestsFilter, (details, callback) => {
    if (!details.url.includes(baseUrl + '/api/login/login')) {
      // console.log('Request URL:', details.url); // 打印请求的 URL    
      if (storedCookie) {
        details.requestHeaders['Cookie'] = storedCookie; // 使用提取的 cookie  
        // console.log('Modified Headers:', details.requestHeaders); // 打印修改后的请求头  
      }
    }
    callback({ requestHeaders: details.requestHeaders })
  })

  // 关闭非核心 Chromium 服务
  // app.commandLine.appendSwitch('disable-3d-apis');
  // app.commandLine.appendSwitch('disable-gpu');
  // app.commandLine.appendSwitch('disable-hardware-acceleration') // 全局禁用
})

// 监听显示软键盘事件
ipcMain.on('show-keyboard', () => {
  // 尝试启动 Onboard
  shell.openExternal('onboard')
    .catch(err => {
      console.error('Failed to start Onboard:', err);
      // 备选方案：启动 Florence
      shell.openExternal('florence');
    });
});

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on('ready', createWindow)

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})
// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and import them here.
