// See the Electron documentation for details on how to use preload scripts:
// https://www.electronjs.org/docs/latest/tutorial/process-model#preload-scripts
const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')
// const log = require('electron-log')
import * as log from 'electron-log'

contextBridge.exposeInMainWorld('electron', {
  log: {
    info: (message: string) => log.info(message),
    warn: (message: string) => log.warn(message),
    error: (message: string) => log.error(message),
  },
})

contextBridge.exposeInMainWorld('dirname', ipcRenderer.invoke('__dirname'))

contextBridge.exposeInMainWorld('electronAPI', {
  showKeyboard: () => ipcRenderer.send('show-keyboard')
});
