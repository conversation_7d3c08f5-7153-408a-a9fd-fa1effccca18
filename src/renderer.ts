/**
 * This file will automatically be loaded by vite and run in the "renderer" context.
 * To learn more about the differences between the "main" and the "renderer" context in
 * Electron, visit:
 *
 * https://electronjs.org/docs/tutorial/process-model
 *
 * By default, Node.js integration in this file is disabled. When enabling Node.js integration
 * in a renderer process, please be aware of potential security implications. You can read
 * more about security risks here:
 *
 * https://electronjs.org/docs/tutorial/security
 *
 * To enable Node.js integration in this file, open up `main.ts` and enable the `nodeIntegration`
 * flag:
 *
 * ```
 *  // Create the browser window.
 *  mainWindow = new BrowserWindow({
 *    width: 800,
 *    height: 600,
 *    webPreferences: {
 *      nodeIntegration: true
 *    }
 *  });
 * ```
 */

import './index.css'
import { createApp } from 'vue'
import App from './App.vue'

import Antd from 'ant-design-vue';
// import Antd, { ConfigProvider } from 'ant-design-vue';
// import zhCN from 'ant-design-vue/es/locale/zh_CN'; // 引入中文语言包
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn'; // 引入 dayjs 中文语言包
import 'ant-design-vue/dist/reset.css';
// import 'ant-design-vue/dist/antd.css'
import longpress from './directives/longpress';

import router from './routers/index'
import pinia from './stores'

import mitt from 'mitt';

// electron 日志
// const electronLog = require('electron-log');
// import electronLog from 'electron-log';

// 设置 dayjs 的全局语言为中文
dayjs.locale('zh-cn');

// 定义全局函数
function globalFunction() {
  console.log('This is a global function from global properties')
}

// createApp(App).mount('#app')
const app = createApp(App)

app.use(Antd).use(pinia).use(router)

// 将函数挂载到全局
app.config.globalProperties.$globalFunction = globalFunction
app.config.globalProperties.$bus = mitt();
// app.config.globalProperties.$log = electronLog;
// 使用 ConfigProvider 设置全局语言为中文
// app.use(ConfigProvider, {
//     locale: zhCN,
// });

// 注册全局指令
app.directive('longpress', longpress);

// console.log(
//   '👋 This message is being logged by "renderer.ts", included via Vite'
// )

app.mount('#app')
