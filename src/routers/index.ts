import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'

// 固定路由（默认路由）
const constantRoutes: RouteRecordRaw[] = [
	{
		path: '/login',
		name: 'login',
		component: () => import('../views/login/index.vue'),
		meta: {
			title: '登录',
		},
	},
	{
		path: '/',
		component: () => import('../layouts/index.vue'),
		redirect: '/index',
		meta: {
			title: '工作台',
		},
		children: [
			// ===================== 首页 =====================
			{
				path: '/index',
				component: () => import('../views/main/searchIndex.vue'),
				meta: {
					title: '主页面-查询',
				},
			},
			{
				path: '/waitList',
				component: () => import('../views/main/listIndex.vue'),
				meta: {
					title: '主页面-待检清单',
				},
			},
			// ===================== 操作页 =====================
			{
				path: '/todoList',
				component: () => import('../views/todo/todoIndex.vue'),
				meta: {
					title: '个人任务列表',
				},
			},
			{
				path: '/hrv',
				component: () => import('../views/hrv/hrvIndex.vue'),
				meta: {
					title: 'HRV采集页面',
				},
			},
			// ===================== 工作台 =====================
			{
				path: '/record',
				component: () => import('../views/record/recordIndex.vue'),
				meta: {
					title: '采集端登记',
				},
			},
			{
				path: '/unlineTodo',
				component: () => import('../views/main/unlineIndex.vue'),
				meta: {
					title: '离线任务',
				},
			},
			{
				path: '/logIndex',
				component: () => import('../views/log/logIndex.vue'),
				meta: {
					title: '数据统计',
				},
			},
			// ===================== 底部设置栏 =====================
			{
				path: '/settings',
				component: () => import('../layouts/components/settingsIndex.vue'),
				meta: {
					title: '系统设置',
				},
			},
			{
				path: '/warnConfig',
				component: () => import('../layouts/components/warnConfigIndex.vue'),
				meta: {
					title: '报警设置',
				},
			},
			{
				path: '/waveform',
				component: () => import('../views/waveForm/index.vue'),
				meta: {
					title: '波形图图例',
				},
			},
			{
				path: '/reload',
				name: 'reload',
				component: () => import('../views/reload.vue'),
				meta: {
					title: '重新加载'
				},
			},
		],
	},
]

const router = createRouter({
	// history: createWebHistory(), // 使用History模式
	history: createWebHashHistory('/'), // 使用Hash模式
	routes: constantRoutes,
})

/**
 * @description 路由拦截 beforeEach
 * */
// router.beforeEach(async (to, _from, next) => {
//   let token = localStorage.getItem('token')
//   // document.title = to.title
//   // 判断是访问登陆页，有 Token 就在当前页面，没有 Token 重置路由到登陆页
//   console.log('判断', !token)
//   if (!token) {
//     next('/login')
//   } else {
//     // 如果已登录状态下，进入登录页会强制跳转到主页
// 	console.log('to.name', to.name)
//     if (to.name === 'login') {
//       next({
//         path: '/',
//         replace: true,
//       })
//     }else{
// 		next()
// 	}
//   }
// })

router.isReady().then(() => {
	// loadingFadeOut()
})

export default router