import { defineStore } from 'pinia';
import settingsDefault from '@/settings';
import { ref } from 'vue';

const useSettingsStore = defineStore(
  // 唯一ID
  'settings',
  () => {
    const settings = ref(settingsDefault)

    // 设置请求地址
    function setBaseUrl(_baseUrl: string) {
      settings.value.client.baseUrl = _baseUrl;
      console.log('设置请求地址：', settings.value.client.baseUrl)
    }

    function getBaseUrl() {
      console.log('获取请求地址：', settings.value.client.baseUrl)
      return settings.value.client.baseUrl;
    }

    // 设置报告地址
    function setReportUrl(_reportUrl: string) {
      settings.value.client.reportUrl = _reportUrl;
    }

    // 设置请求地址
    function setLogDate(_logDate: number) {
      settings.value.client.logStorageDate = _logDate
    }

    return {
      settings,
      setBaseUrl,
      getBaseUrl,
      setReportUrl,
      setLogDate
    }
  }
)

export default useSettingsStore
