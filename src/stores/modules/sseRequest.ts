// stores/message.js
import { set } from 'lodash';
import { defineStore } from 'pinia';
import { sseService, SSEChatParams } from "@/utils/sse";
import $bus from "@/utils/bus.ts";
import { userMessageStore } from '@/stores/modules/userMessage'
import { Modal, message } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { createVNode } from 'vue';

export const useSSEStore = defineStore(
    // 唯一ID
    'sseStoreMessage',
    {
        state: () => ({
            waveformSource: null, // 连接波形数据SSE
            leadStatusSource: null, // 连接导联状态SSE
            batterySource: null, // 连接连接用于接收电量信息以及低电报警提示SSE
            warningSource: null, // 连接连接用于接收报警提示SSE-心电过速、心电过缓、纤维性颤动
            heartRateSource: null, // 连接连接用于接收心率-呼吸率数据
            showDeleteConfirm: false, // 显示删除确认框
        }),
        getters: {
            // getBoXing: state => state.isBoXingChart,
        },
        actions: {
            // 连接波形数据SSE
            async setWaveform() {
                let i = 0;
                // 连接SSE
                this.waveformSource = {
                    url: '/api/sse/waveform',
                    onmessage: (event: MessageEvent) => {
                        i++
                        if (true) {
                            i = 0;
                            // 处理导联状态数据
                            const data = JSON.parse(event.data);
                            $bus.emit('waveFormSSE', data);
                            // 打印导联状态数据
                            // console.log('收到waveformSource消息:', data)
                        }
                    },
                    onopen: () => console.log('waveformSource-SSE 连接已开启'),
                    finallyHandler: () => console.log("waveformSource-finallyHandler 操作")
                };
                await sseService.connect(this.waveformSource)
            },
            // 连接导联状态SSE
            async setLeadStatus() {
                // 连接SSE
                this.leadStatusSource = {
                    url: '/api/sse/ecg-lead-status',
                    onmessage: (event: MessageEvent) => {
                        // 处理导联状态数据
                        const dataLead = JSON.parse(event.data);
                        $bus.emit('leadStatusSSE', dataLead);
                        // 打印导联状态数据
                        console.log('收到leadStatusSource消息:', dataLead)
                    },
                    onopen: () => console.log('leadStatusSource-SSE 连接已开启'),
                    finallyHandler: () => console.log("leadStatusSource-finallyHandler 操作")
                };
                await sseService.connect(this.leadStatusSource)
            },
            // 连接连接用于接收电量信息以及低电报警提示SSE
            async setBattery() {
                // 连接SSE
                this.batterySource = {
                    url: '/api/sse/power-supply-early-warning',
                    onmessage: (event: MessageEvent) => {
                        // 处理导联状态数据
                        const data = JSON.parse(event.data);
                        const alarmBatteryLow = Number(userMessageStore().clientGet.alarmBatteryLow) ? Number(userMessageStore().clientGet.alarmBatteryLow) : 0.15;
                        $bus.emit('batterySSE', data);
                        // 打印导联状态数据
                        // console.log('收到batterySource消息:', data, alarmBatteryLow);
                        if (Number(data.charge) <= alarmBatteryLow && !Number(data.status) && !this.showDeleteConfirm) {
                            this.showDeleteConfirm = true;
                            const modal = Modal.confirm({
                                title: '提示',
                                icon: createVNode(ExclamationCircleOutlined),
                                content: '当前电量已低于预警值，请及时充电?',
                                okText: '确认',
                                okType: 'danger',
                                cancelText: '关闭',
                                onOk: () => {
                                    console.log('OK');
                                    setTimeout(() => {
                                        this.showDeleteConfirm = false;
                                    }, 60000)
                                },
                                onCancel: () => {
                                    console.log('Cancel');
                                    setTimeout(() => {
                                        this.showDeleteConfirm = false;
                                    }, 60000)
                                },
                            });
                            modal.update({});
                        }
                    },
                    onopen: () => console.log('batterySource-SSE 连接已开启'),
                    finallyHandler: () => console.log("batterySource-finallyHandler 操作")
                };
                await sseService.connect(this.batterySource)
            },
            // 创建SSE连接用于接收心率-呼吸率数据
            async setHeartRate() {
                // 连接SSE
                this.heartRateSource = {
                    url: '/api/sse/heart-rate',
                    onmessage: (event: MessageEvent) => {
                        // 处理导联状态数据
                        const data = JSON.parse(event.data);
                        $bus.emit('heartRateSSE', data);
                        // 打印导联状态数据
                        // console.log('收到heartRateSource消息:', data)
                    },
                    onopen: () => console.log('heartRateSource-SSE 连接已开启'),
                    finallyHandler: () => console.log("heartRateSource-finallyHandler 操作")
                };
                await sseService.connect(this.heartRateSource)
            },
            // 连接连接用于接收报警提示SSE-心电过速、心电过缓、纤维性颤动
            async setWarning() {
                // 连接SSE
                this.warningSource = {
                    url: '/api/sse/ecg-early-warning',
                    onmessage: (event: MessageEvent) => {
                        // 处理导联状态数据
                        const data = JSON.parse(event.data);
                        $bus.emit('warningSSE', data);
                        // 打印导联状态数据
                        // console.log('收到warningSource消息:', data)
                    },
                    onopen: () => console.log('warningSource-SSE 连接已开启'),
                    finallyHandler: () => console.log("warningSource-finallyHandler 操作")
                };
                await sseService.connect(this.warningSource)
            },
            // 断开SSE连接
            disconnectSSE(query: string) {
                sseService.disconnect();
                let queryArr = query.split(',');
                queryArr.forEach((item) => {
                    // set(this, item, null);
                    switch (item) {
                        case 'waveformSource':
                            this.waveformSource = null;
                            $bus.off('waveFormSSE');
                            break;
                        case 'leadStatusSource':
                            this.leadStatusSource = null;
                            $bus.off('leadStatusSSE');
                            break;
                        case 'batterySource':
                            this.batterySource = null;
                            $bus.off('batterySSE');
                            break;
                        case 'warningSource':
                            this.warningSource = null;
                            $bus.off('warningSSE');
                            break;
                        case 'heartRateSource':
                            this.heartRateSource = null;
                            $bus.off('heartRateSSE');
                            break;
                        default:
                            break;
                    }
                })
            }
        }
    }
);