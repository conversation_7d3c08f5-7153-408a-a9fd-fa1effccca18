// stores/message.js
import { defineStore } from 'pinia';
import piniaPersistConfig from "@/stores/helper/persist";

export const useUnlineTodoStore = defineStore(
	// 唯一ID
	'unlineTodo',
	{
		state: () => ({
			todoList: [
				{ id: 1, title: 'HRV', patientName: '患者A', content: '这是卡片 1 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '男', inspectItem: 'HRV' },
				{ id: 2, title: 'Stroop + HRV(SCWT)', patientName: '患者A', content: '这是卡片 2 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '男', inspectItem: 'SCWT' },
				{ id: 3, title: '心算+ HRV(MAT)', patientName: '患者A', content: '这是卡片 3 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '男', inspectItem: 'MAT' },
				{ id: 4, title: 'Stroop + HRV(SCWT)', patientName: '患者B', content: '这是卡片 1 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '女', inspectItem: 'SCWT' },
				{ id: 5, title: '心算+ HRV(MAT)', patientName: '患者B', content: '这是卡片 2 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '女', inspectItem: 'MAT' },
				{ id: 6, title: 'HRV', patientName: '患者B', content: '这是卡片 3 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '女', inspectItem: 'HRV' },
				{ id: 7, title: 'Stroop + HRV(SCWT)', patientName: '患者T', content: '这是卡片 1 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '男', inspectItem: 'SCWT' },
				{ id: 8, title: '心算+ HRV(MAT)', patientName: '患者A', content: '这是卡片 2 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '男', inspectItem: 'MAT' },
				{ id: 9, title: 'HRV', patientName: '患者G', content: '这是卡片 3 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '男', inspectItem: 'HRV' },
				{ id: 10, title: '心算+ HRV(MAT)', patientName: '患者R', content: '这是卡片 1 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '男', inspectItem: 'MAT' },
				{ id: 11, title: 'HRV', patientName: '患者G', content: '这是卡片 2 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '男', inspectItem: 'HRV' },
				{ id: 12, title: 'Stroop + HRV(SCWT)', patientName: '患者T', content: '这是卡片 3 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '男', inspectItem: 'SCWT' },
				{ id: 34, title: 'HRV', patientName: '患者B', content: '这是卡片 3 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '女', inspectItem: 'HRV' },
				{ id: 25, title: 'Stroop + HRV(SCWT)', patientName: '患者T', content: '这是卡片 1 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '男', inspectItem: 'SCWT' },
				{ id: 85, title: '心算+ HRV(MAT)', patientName: '患者A', content: '这是卡片 2 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '男', inspectItem: 'MAT' },
				{ id: 23, title: 'HRV', patientName: '患者G', content: '这是卡片 3 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '男', inspectItem: 'HRV' },
				{ id: 69, title: '心算+ HRV(MAT)', patientName: '患者R', content: '这是卡片 1 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '男', inspectItem: 'MAT' },
				{ id: 389, title: 'HRV', patientName: '患者G', content: '这是卡片 2 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '男', inspectItem: 'HRV' },
				{ id: 332, title: 'Stroop + HRV(SCWT)', patientName: '患者T', content: '这是卡片 3 的内容', doctorName: '吴宇', deptName: '心理科', age: 32, gender: '男', inspectItem: 'SCWT' }
			],
		}),
		getters: {
			getTodoList: state => state.todoList,
		},
		actions: {
			// 单个添加
			setTodoList(newValue) {
				this.todoList.concat(newValue);
			},
			// 数组重置
			setAllTodoList(newValue) {
				this.todoList = newValue;
			},
			// 删除离线任务
			deleteTodoItem(tIndex) {
				this.todoList.splice(tIndex, 1);
			},

		},
		persist: piniaPersistConfig("unlineTodo")
	}
);