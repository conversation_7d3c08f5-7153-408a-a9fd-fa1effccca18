// stores/message.js
import { set } from 'lodash';
import { defineStore } from 'pinia';
import router from '@/routers'
import { loginApi, getLoginUserInfo, logoutApi } from '@/api/modules/login'
import { WebSocketClient } from '@/utils/websocket'
import { getDeviceInfo } from '@/api/modules/device'
import { taskExamStart, taskExamStop, getInspectList } from '@/api/modules/inspect'
import { Modal, message } from 'ant-design-vue';

export const userMessageStore = defineStore(
	// 唯一ID
	'userMessage',
	{
		state: () => ({
			content: '',
			token: "",
			// isBoXingChart: true,
			topicInfo: {
				id: 11111111, // 问卷ID
				topicAnswerIndex: 0, // 题目序号
				list: [{
					topicSort: 0, // 题目序号
					topicGraphic: 1, // 题目图片
					topicCharacter: [2, 1, 3, 0], // 题目选项
					correctAnswer: 1, // 正确答案
					actualAnswer: null, // 用户答案
				}]
			},
			// 客户端配置
			client: {
				baseUrl: 'http://127.0.0.1:8080',
				reportUrl: 'http://127.0.0.1:9000',
				logStorageDate: 60,
			},
			wsClient: null,
			deviceInfo: {
				deviceNo: null
			}
		}),
		getters: {
			// getBoXing: state => state.isBoXingChart,
			topicInfoGet: state => state.topicInfo,
			clientGet: state => state.client,
			deviceGet: state => state.deviceInfo
		},
		actions: {
			setContent(newContent: string) {
				this.content = newContent;
			},
			setToken(token: string) {
				this.token = token;
			},
			setTopicInfo(value: any) {
				this.topicInfo = value;
			},
			setClient(value: any, key?: string) {
				if (key) {
					this.client[key] = value;
				} else {
					this.client = value;
				}
			},
			// 登录
			async login(data: { username: string; password: string }) {
				const res = await loginApi(data)
				sessionStorage.setItem('token', res.data.token);
				await this.setToken(res.data.token);
				await getLoginUserInfo();
				const dev = await getDeviceInfo();
				if (dev.data) {
					this.deviceInfo = dev.data;
				}
			},
			// 登出
			async logout(redirect = router.currentRoute.value.fullPath) {
				await logoutApi();
				sessionStorage.removeItem('token')
				await router.push({
					name: 'login',
					query: {}
				})
			},
			// 初始化WebSocket
			async initWebSocket() {
				const dev = await getDeviceInfo();
				if (dev.data) {
					this.deviceInfo = dev.data;
				}
				if (this.wsClient) {
					this.wsClient.close();
					this.wsClient = null;
				}
				this.wsClient = await new WebSocketClient('ws://192.168.1.72:18888/websocket/api/hrv/' + (this.deviceInfo.deviceNo || 1) + '_client');
				// this.wsClient.connect();
				// 设置事件监听
				this.wsClient.onopen(() => {
					console.log('WebSocket连接已建立')
				})

				this.wsClient.onmessage((event: any,) => {
					try {
						const data = JSON.parse(event.data)
						console.log('收到消息:', data)
						// 处理收到的消息
						// 处理消息逻辑
						// if (event.data.http_type == 'resp' && event.data.code == 200) {
						switch (data.msg_type) {
							case 'push_start':
								// 准备开始
								break;
							case 'task_start':
								// 任务开始
								const modal = Modal.success({
									title: '准备开始',
									content: `收到同步任务消息`,
									onOk: () => {
										this.sendMessage({
											"msg_type": "task_start_confirm",
											"data": {
												"dest_id": data.data.src_id
											}
										})
										getInspectList(data.data.inspect_list_id).then(res => {
											if (res.code == 200) {
												router.push({
													path: '/hrv',
													query: {
														patient: res.data.patientName,
														todoType: res.data.inspectItemHrv,
														age: res.data.age,
														id: res.data.id,
														title: res.data.inspectItem,
														isTempTask: 0, // 是否临时任务
														isOfflineTask: 0, // 是否离线任务
														hrvDuration: res.data.hrvDuration,
														isWebsocket: 1, // 是否websocket
													}
												})
											}
										})

									},
									onCancel() {
										console.log('Cancel');
									},
									class: 'test',
								});
								modal.update({})
								break;
							case 'task_stop':
								// 任务结束
								taskExamStop({ taskId: data.data.inspect_list_id, isGeneralTask: true }).then(res => {
									if (res.code == 200) {
										message.success('任务结束');
										router.replace('/')
									} else {
										console.log(res.msg);
										message.warning(res.msg || '服务异常');
									}
								}).catch(err => {
									console.log(err);
									message.warning(err.message || '服务异常');
								})
								break;
							default:
								break;
						}
						// }
					} catch (e) {
						console.error('消息解析失败:', e)
					}
				})
				this.wsClient.onclose(() => {
					console.log('WebSocket连接已关闭')
				})
				this.wsClient.onerror(() => {
					console.error('WebSocket连接发生错误')
				})
				// 建立连接
				this.wsClient.connect()
			},
			// 发送消息
			sendMessage(message: any) {
				// console.log('发送消息:', this.wsClient);
				if (this.wsClient && this.wsClient.socket.readyState === WebSocket.OPEN) {
					this.wsClient.send(JSON.stringify(message))
				} else {
					console.error('WebSocket连接未建立或已关闭')
				}
			},
			// 关闭WebSocket连接
			closeWebSocket() {
				if (this.wsClient) {
					this.wsClient.close()
				}
			}
		}
	}
);