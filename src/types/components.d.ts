/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AdminSearch: typeof import('./../components/Nurse/adminSearch.vue')['default']
    Auth: typeof import('./../components/Auth/index.vue')['default']
    Chip: typeof import('./../components/Chip/index.vue')['default']
    ColorfulCard: typeof import('./../components/ColorfulCard/index.vue')['default']
    ColSetting: typeof import('./../components/ProTable/components/ColSetting.vue')['default']
    FileUpload: typeof import('./../components/FileUpload/index.vue')['default']
    FixedActionBar: typeof import('./../components/FixedActionBar/index.vue')['default']
    Form: typeof import('./../components/Form/index.vue')['default']
    FormItem: typeof import('./../components/Form/components/FormItem.vue')['default']
    Grid: typeof import('./../components/Grid/index.vue')['default']
    GridItem: typeof import('./../components/Grid/components/GridItem.vue')['default']
    HBadge: typeof import('./../layouts/ui-kit/HBadge.vue')['default']
    HButton: typeof import('./../layouts/ui-kit/HButton.vue')['default']
    HCheckList: typeof import('./../layouts/ui-kit/HCheckList.vue')['default']
    HDialog: typeof import('./../layouts/ui-kit/HDialog.vue')['default']
    HDropdown: typeof import('./../layouts/ui-kit/HDropdown.vue')['default']
    HDropdownMenu: typeof import('./../layouts/ui-kit/HDropdownMenu.vue')['default']
    HInput: typeof import('./../layouts/ui-kit/HInput.vue')['default']
    HKbd: typeof import('./../layouts/ui-kit/HKbd.vue')['default']
    HSelect: typeof import('./../layouts/ui-kit/HSelect.vue')['default']
    HSlideover: typeof import('./../layouts/ui-kit/HSlideover.vue')['default']
    HTabList: typeof import('./../layouts/ui-kit/HTabList.vue')['default']
    HToggle: typeof import('./../layouts/ui-kit/HToggle.vue')['default']
    HTooltip: typeof import('./../layouts/ui-kit/HTooltip.vue')['default']
    IconPicker: typeof import('./../components/IconPicker/index.vue')['default']
    ImagePreview: typeof import('./../components/ImagePreview/index.vue')['default']
    ImagesUpload: typeof import('./../components/ImagesUpload/index.vue')['default']
    ImageUpload: typeof import('./../components/ImageUpload/index.vue')['default']
    LayoutContainer: typeof import('./../components/LayoutContainer/index.vue')['default']
    Loading: typeof import('./../components/Loading/index.vue')['default']
    LoginForm: typeof import('./../components/LoginForm/index.vue')['default']
    NotAllowed: typeof import('./../components/NotAllowed/index.vue')['default']
    NurseSearch: typeof import('./../components/Nurse/nurseSearch.vue')['default']
    OnlySearch: typeof import('./../components/Nurse/onlySearch.vue')['default']
    PageHeader: typeof import('./../components/PageHeader/index.vue')['default']
    PageMain: typeof import('./../components/PageMain/index.vue')['default']
    Pagination: typeof import('./../components/ProTable/components/Pagination.vue')['default']
    PcasCascader: typeof import('./../components/PcasCascader/index.vue')['default']
    ProTable: typeof import('./../components/ProTable/index.vue')['default']
    RegisterForm: typeof import('./../components/RegisterForm/index.vue')['default']
    ResetPasswordForm: typeof import('./../components/ResetPasswordForm/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchBar: typeof import('./../components/SearchBar/index.vue')['default']
    SearchForm: typeof import('./../components/SearchForm/index.vue')['default']
    SearchFormItem: typeof import('./../components/SearchForm/components/SearchFormItem.vue')['default']
    Sparkline: typeof import('./../components/Sparkline/index.vue')['default']
    SpinkitLoading: typeof import('./../components/SpinkitLoading/index.vue')['default']
    StorageBox: typeof import('./../components/StorageBox/index.vue')['default']
    SvgIcon: typeof import('./../components/SvgIcon/index.vue')['default']
    SystemInfo: typeof import('./../components/SystemInfo/index.vue')['default']
    TableColumn: typeof import('./../components/ProTable/components/TableColumn.vue')['default']
    TreeFilter: typeof import('./../components/TreeFilter/index.vue')['default']
    Trend: typeof import('./../components/Trend/index.vue')['default']
  }
}
