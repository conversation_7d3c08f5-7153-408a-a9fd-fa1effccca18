<template>
    <a-spin :spinning="spinning" class="scwt-topic">
        <a-row :gutter="20"
            v-if="matTopicIndex <= (matTopicChange.list.length > 0 ? matTopicChange.list.length - 1 : 0)">
            <a-col :span="24" style="height: 10vh;">
                <h1>{{ matTopic.topic }}</h1>
            </a-col>
            <a-col :span="24">
                <a-input ref="inputRef" size="large" v-model:value="inputValue" placeholder="请输入计算结果"
                    oninput="value=value.replace(/^0+(\d)|[^\d]+/g,'')"></a-input>
            </a-col>
            <a-col :span="24" style="height: 5vh;"></a-col>
            <a-col :span="24">
                <VirtualKeyboard @input="handleInput" />
            </a-col>
        </a-row>
        <a-result status="success" title="当前题目已作答完毕" sub-title="请联系医生进行下一步" v-else>
        </a-result>
    </a-spin>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed, defineEmits, watch } from "vue";
// import { matEsConfig } from "./mat_data_es";
import { get_MAT_ES_Topic } from "@/api/modules/inspect"
import VirtualKeyboard from "@/components/keyboardItem.vue";
import { topicColor } from "@/utils/filter";
import { userMessageStore } from "@/stores/modules/userMessage";
import { cloneDeep } from "lodash-es";
import { message } from 'ant-design-vue';

const userStore = userMessageStore();
const matStoreInfo = computed(() => userStore.topicInfoGet);
const emit = defineEmits(['topic', 'submit', 'start']);

const inputRef = ref(null); // 输入框的引用
const spinning = ref<boolean>(false);
const matTopicIndex = ref(0); // 记录当前的题目索引
const matTopic = ref({ topicCharacter: [], topicGraphic: 0, topicSort: 0 }); // 记录当前的题目内容
const matTopicChange = ref({
    topicAnswerIndex: 0,
    list: []
}); // 记录当前的问卷
const inputValue = ref(''); // 输入框的值
const matTopicHistory = ref([]); // 记录当前的问卷

// 选中选项
const handleChoose = async (result) => {
    let topic = cloneDeep(matTopic.value);
    matTopicChange.value.list[topic.topicSort].actualAnswer = result;

    // spinning.value = true;
    // 保存提交答案
    emit('submit', {
        "timestamp": new Date().getTime(), // 时间戳
        "eventName": "topic",
        "topicType": 0,
        "matTopicDTO": {
            "topicSort": topic.topicSort,
            "topic": topic.topic,
            "correctAnswer": topic.correctAnswer,
            "actualAnswer": result
        },
    });
    // 关闭计时器
    await closeTimer('timer');
    // 答案判定
    console.log(`第${topic.topicSort + 1}题答题情况`, topic.correctAnswer, result, topic.correctAnswer == result);
    matTopicHistory.value.push({ ...matTopicChange.value.list[topic.topicSort], actualAnswer: result, currentDate: new Date().getTime() });
    if (result >= 1000) {
        if (matTopic.value.topicSort + 1 < 17) message.info('规定时间内未作答');
    } else {
        if (topic.correctAnswer == result) {
            message.success('回答正确');
        } else {
            message.error('回答错误');
        }
    }
    // 下一题
    // inputValue.value = '';
    // matTopicIndex.value = topic.topicSort + 1;
    // matTopicChange.value.topicAnswerIndex = topic.topicSort + 1;
    // matTopic.value = matTopicChange.value.list[topic.topicSort + 1];
    if (topic.topicSort == matTopicChange.value.list.length - 1) {
        emit('start'); // 进入下一阶段
    } else {
        inputValue.value = '';
        matTopicIndex.value = topic.topicSort + 1;
        matTopicChange.value.topicAnswerIndex = topic.topicSort + 1;
        matTopic.value = matTopicChange.value.list[topic.topicSort + 1];
    }
}

const handleInput = (key) => {
    if (key === 'backspace') {
        inputValue.value = inputValue.value.slice(0, -1); // 处理删除操作
    } else if (key === 'enter') {
        handleChoose(inputValue.value) // 处理正常输入操作
    } else {
        inputValue.value += key; // 处理正常输入操作
    }
};

// 答题计时器
const timer = ref(null);
const topicWaitTime = ref(5); // 题目等待时间
const startTimer = function () {
    if (timer.value) return false;
    topicWaitTime.value = 5;
    timer.value = setInterval(() => {
        topicWaitTime.value--;
        console.log('题目等待时间', topicWaitTime.value);
        emit('topic', topicWaitTime.value, matTopicIndex.value + 1, matTopicChange.value.list.length || 0, matTopicHistory.value);
        if (topicWaitTime.value == 0) {
            closeTimer('timer');
            handleChoose(9999, matTopic.value.topicSort ?? 0);
            spinning.value = true;
            // matTopicIndex.value += 1;
            return false;
        }
    }, 1000)
}
const closeTimer = function (operate) {
    if (timer.value) {
        clearInterval(timer.value);
        topicWaitTime.value = 3;
        timer.value = null;
        setTimeout(() => {
            spinning.value = false;
            // 若页面注销，无需下一步
            if (operate == 'destroy') return false;
            // 若题目未作完，自动开始
            if (matTopicIndex.value <= matTopicChange.value.list.length - 1) {
                console.log('若题目未作完，自动开始');
                startTimer();
            }
        })
    }
}

const handleKeyDownMat = (e) => {
    if (e.key === 'Backspace') {
        inputValue.value = inputValue.value.slice(0, -1); // 处理删除操作
    } else if (e.key === 'Enter') {
        handleChoose(inputValue.value) // 处理正常输入操作
    }
}

// 获取答题数据
const getTopicData = async () => {
    const { data } = await get_MAT_ES_Topic();
    matTopicChange.value = { ...data, topicAnswerIndex: 0 };
    // console.log('问卷', scwtTopicChange.value)
    matTopic.value = data.list[matTopicIndex.value];
    // console.log('题目', scwtTopic.value)
    userStore.setTopicInfo(data);
    // 初始化
    inputRef.value?.focus();
    // console.log('初始化', scwtTopicChange.value.list.length)
    emit('topic', 5, 1, matTopicChange.value.list.length);
}

onMounted(() => {
    // if (matStoreInfo.value.id == matEsConfig.data.id) {
    //     matTopicChange.value = matStoreInfo.value;
    //     matTopic.value = matTopicChange.value.list[matStoreInfo.value.topicAnswerIndex + 1];
    //     matTopicIndex.value = matTopicChange.value.topicAnswerIndex;
    // } else {
    //     matTopicChange.value = { ...matEsConfig.data, topicAnswerIndex: 0 };
    //     matTopic.value = matEsConfig.data.list[matTopicIndex.value];
    //     userStore.setTopicInfo(matEsConfig.data);
    // }
    // matTopicChange.value = { ...matEsConfig.data, topicAnswerIndex: 0 };
    // matTopic.value = matEsConfig.data.list[matTopicIndex.value];
    // userStore.setTopicInfo(matEsConfig.data);

    // console.log('当前题目', matTopic.value, matStoreInfo.value.id == matEsConfig.data.id)
    // 获取答题数据
    getTopicData();
    // 初始化
    // emit('topic', 5, 1, matTopicChange.value.list.length);
    // 开始计时器
    startTimer();
    // 监听键盘事件
    document.addEventListener("keydown", handleKeyDownMat)
})

onUnmounted(() => {
    // 移除计时器
    closeTimer('destroy');
    // 移除键盘事件
    document.removeEventListener('keydown', handleKeyDownMat)
});

// 监听-问卷
watch(
    () => matTopic.value,
    (newValue, oldValue) => {
        if (newValue != oldValue) {
            emit('topic', 5, matTopicIndex.value + 1, matTopicChange.value.list.length || 0, matTopicHistory.value);
            userStore.setTopicInfo(matTopicChange.value);
        }
    },
    { immediate: true }
)

// 监听-跳转下一题
watch(
    () => matTopicIndex.value,
    (newValue, oldValue) => {
        if (newValue != oldValue) {
            // matTopic.value = matTopicChange.value.list[newValue];
            console.log('当前题目', matTopic.value)
        }
    },
    { immediate: true }
)

</script>

<style lang="scss" scoped>
.scwt-topic {
    width: 100%;
    height: 100%;
}

.scwt-circle {
    width: 20vh;
    height: 20vh;
    border-radius: 50%;
}
</style>./mat_data_es