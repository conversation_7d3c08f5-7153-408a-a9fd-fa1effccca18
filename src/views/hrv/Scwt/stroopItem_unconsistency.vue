<template>
    <a-spin :spinning="spinning" class="scwt-topic">
        <a-row :gutter="20"
            v-if="scwtTopicIndex <= (scwtTopicChange.list.length > 0 ? scwtTopicChange.list.length - 1 : 0)">
            <a-col :span="24" style="height: 10vh;"></a-col>
            <a-col :span="24">
                <a-flex align="center" justify="center">
                    <!-- <div class="scwt-circle"
                    </div> -->
                    <h1
                        :style="{ 'color': topicColor(scwtTopic.topicCharacterColor, scwtTopic.topicCharacterColor).color || '#000000', 'font-size': '2rem' }">
                        {{ ` ${topicColor(scwtTopic.topicCharacter, scwtTopic.topicCharacter).label || '黑'} ` }}
                    </h1>
                </a-flex>
            </a-col>
            <a-col :span="24" style="height: 10vh;"></a-col>
            <a-col :span="24">
                <a-flex justify="space-around" align="center">
                    <div v-for="(item, index) in scwtTopic.topicGraphic" :key="index"
                        :style="{ 'color': topicColor(item, item).color || '#000000', 'font-size': '1.5rem', 'cursor': 'pointer', 'display': 'flex' }"
                        @click="handleChoose(item, scwtTopic.topicSort)">
                        {{ ` ${index + 1}、 ` }}
                        <div class="scwt-circle-2"
                            :style="{ 'background-color': topicColor(item, item).color || '#000000' }">
                        </div>
                    </div>
                </a-flex>
            </a-col>
        </a-row>
        <a-result status="success" title="当前题目已作答完毕" sub-title="请联系医生进行下一步" v-else>
        </a-result>
    </a-spin>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed, defineEmits, watch } from "vue";
// import { scwtConfig } from "./notConsistency";
import { get_SCWT_Unconsistency } from "@/api/modules/inspect";
import { topicColor } from "@/utils/filter";
import { userMessageStore } from "@/stores/modules/userMessage";
import { cloneDeep } from "lodash-es";
import { message } from 'ant-design-vue';

const userStore = userMessageStore();
const scwtStoreInfo = computed(() => userStore.topicInfoGet);
const emit = defineEmits(['topic', 'submit', 'start']);

const spinning = ref<boolean>(false);
const scwtTopicIndex = ref(0); // 记录当前的题目索引
const scwtTopic = ref({ topicGraphic: [], topicGraphic: 0, topicSort: 0 }); // 记录当前的题目内容
const scwtTopicChange = ref({
    topicAnswerIndex: 0,
    list: []
}); // 记录当前的问卷
const scwtTopicHistory = ref([]); // 记录当前的问卷

// 选中选项
const handleChoose = async (item, index) => {
    let topic = cloneDeep(scwtTopicChange.value);
    topic.list[index].actualAnswer = item;
    topic.list[index].currentDate = new Date().getTime();
    // spinning.value = true;
    // 保存提交答案
    emit('submit', {
        "timestamp": new Date().getTime(), // 时间戳
        "eventName": "topic",
        "topicType": 2,
        "nonConsistencyDTO": {
            "topicSort": topic.list[index].topicSort,
            "topicGraphic": topic.list[index].topicGraphic,
            "topicCharacterColor": topic.list[index].topicCharacterColor,
            "topicCharacter": topic.list[index].topicCharacter,
            "correctAnswer": topic.list[index].correctAnswer,
            "actualAnswer": item
        },
    });
    // 关闭计时器
    await closeTimer('timer');
    // 答案判定
    scwtTopicHistory.value.push({ ...topic.list[index] });
    console.log(`第${index + 1}题答题情况`, topic.list[index].correctAnswer, item, topic.list[index].correctAnswer == item);
    if (item > 99) {
        message.info('规定时间内未作答');
    } else {
        if (topic.list[index].correctAnswer == item) {
            message.success('回答正确');
        } else {
            message.error('回答错误');
        }
    }
    // 下一题
    if (index == topic.list.length - 1) {
        emit('start'); // 进入下一阶段
    } else {
        scwtTopicIndex.value = index + 1;
        scwtTopicChange.value = { ...topic, topicAnswerIndex: index + 1 };
        scwtTopic.value = topic.list[index + 1];
    }
    // scwtTopicIndex.value = index + 1;
    // scwtTopicChange.value = { ...topic, topicAnswerIndex: index + 1 };
    // scwtTopic.value = topic.list[index + 1];
}

// 答题计时器
const timer = ref(null);
const topicWaitTime = ref(3); // 题目等待时间
const startTimer = function () {
    if (timer.value) return false;
    topicWaitTime.value = 3;
    timer.value = setInterval(() => {
        topicWaitTime.value--;
        emit('topic', topicWaitTime.value, scwtTopicChange.value.topicAnswerIndex + 1, scwtTopicChange.value.list.length || 0, scwtTopicHistory.value);
        if (topicWaitTime.value == 0) {
            handleChoose(100, scwtTopic.value.topicSort ?? 0);
            spinning.value = true;
            // scwtTopicIndex.value += 1;
            closeTimer('timer');
        }
    }, 1000)
}
const closeTimer = function (operate) {
    if (timer.value) {
        clearInterval(timer.value);
        topicWaitTime.value = 3;
        timer.value = null;
        setTimeout(() => {
            spinning.value = false;
            // 若页面注销，无需下一步
            if (operate == 'destroy') return false;
            // 若题目未作完，自动开始
            if (scwtTopicIndex.value < scwtTopicChange.value.list.length - 1) {
                console.log('若题目未作完，自动开始');
                startTimer();
            }
        })
    }
}

const handleKeyDown = (e) => {
    if (Number(e.key) >= 1 || Number(e.key) <= 5) {
        handleChoose(scwtTopic.value.topicGraphic[Number(e.key) - 1], scwtTopic.value.topicSort);
    }
}

// 获取答题数据
const getTopicData = async () => {
    const { data } = await get_SCWT_Unconsistency();
    scwtTopicChange.value = { ...data, topicAnswerIndex: 0 };
    // console.log('问卷', scwtTopicChange.value)
    scwtTopic.value = data.list[scwtTopicIndex.value];
    // console.log('题目', scwtTopic.value)
    userStore.setTopicInfo(data);
    // 初始化
    // console.log('初始化', scwtTopicChange.value.list.length)
    emit('topic', 3, 1, scwtTopicChange.value.list.length);
}

onMounted(() => {
    // if (scwtStoreInfo.value.id == scwtConfig.data.id) {
    //     scwtTopicChange.value = scwtStoreInfo.value;
    //     scwtTopic.value = scwtTopicChange.value.list[scwtStoreInfo.value.topicAnswerIndex + 1];
    //     scwtTopicIndex.value = scwtTopicChange.value.topicAnswerIndex;
    // } else {
    //     scwtTopicChange.value = { ...scwtConfig.data, topicAnswerIndex: 0 };
    //     scwtTopic.value = scwtConfig.data.list[scwtTopicIndex.value];
    //     userStore.setTopicInfo(scwtConfig.data);
    // }

    // 获取答题数据
    getTopicData();

    // console.log('当前题目', scwtTopic.value, scwtStoreInfo.value.id == scwtConfig.data.id)
    // 初始化
    // emit('topic', 1, scwtTopicChange.value.list.length);
    // 开始计时器
    startTimer();
    // 监听键盘事件
    document.addEventListener("keydown", handleKeyDown)
})

onUnmounted(() => {
    // 移除计时器
    closeTimer('destroy');
    // 移除键盘事件
    document.removeEventListener('keydown', handleKeyDown)
});

// 监听-问卷
watch(
    () => scwtTopicChange.value,
    (newValue, oldValue) => {
        if (newValue != oldValue) {
            emit('topic', 3, newValue.topicAnswerIndex + 1, newValue.list.length || 0, scwtTopicHistory.value);
            userStore.setTopicInfo(newValue);
        }
    },
    { immediate: true }
)

// 监听-跳转下一题
watch(
    () => scwtTopicIndex.value,
    (newValue, oldValue) => {
        if (newValue != oldValue) {
            // scwtTopic.value = scwtTopicChange.value.list[newValue];
            console.log('当前题目', scwtTopic.value)
        }
    },
    { immediate: true }
)

</script>

<style lang="scss" scoped>
.scwt-topic {
    width: 100%;
    height: 100%;
}

.scwt-circle-2 {
    width: 5vh;
    height: 5vh;
    border-radius: 50%;
}
</style>