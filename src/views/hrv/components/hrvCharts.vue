<template>
	<div>
		<a-card :style="{ ...recordRightAreaCss }" class="hrv_card_header">
			<template #title>
				<a-flex justify="space-between" align="center" v-if="todoInfo.todoType == 0">
					<span>II导联</span>
					<a-button type="default" size="large">×1</a-button>
					<span>诊断</span>
					<a-button type="default" size="large">
						<a-image :width="22" :height="20" :src="isHeartLink ? heartIcon : heartErrorIcon"
							:preview="false" :fallback="settingInfo.noneImgBase64" style="display: block" />
					</a-button>
					<span>25mm/s</span>
				</a-flex>
				<span v-else>放松阶段</span>
			</template>

			<!-- <HrvEchart v-if="todoInfo.todoType == 0" ref="echartHrvRef" :options="echartOption" style="height: 47vh" /> -->
			<WaveForm v-if="todoInfo.todoType == 0" ref="echartHrvRef" :options="echartOption" style="height: 56vh" />
			<HrvVideo v-else ref="videoHrvRef" style="height: 56vh" />

			<!-- <div v-if="todoInfo.todoType == 0" style="margin-top: 5px;">
				<a-flex align="center" justify="space-around">
					<span class="font_cyan">心电分辨率：1bpm</span>
					<a-image :width="24" :height="20" :src="exchangeIcon" :preview="false"
						:fallback="settingInfo.noneImgBase64" style="display: block;cursor: pointer;"
						@click="handleChange()" />
				</a-flex>
			</div> -->

			<!-- <template class="ant-card-actions" #actions>
					<span class="font_cyan">心电分辨率：1bpm</span>
					<a-image :width="24" :height="20" :src="exchangeIcon" :preview="false"
						:fallback="settingInfo.noneImgBase64" style="display: block" @click="handleChange()" />
			</template> -->

		</a-card>

		<a-flex justify="space-around" align="center" class="hrv_main_footer">
			<a-button type="default" size="large" @click="handleExit">退 出</a-button>
			<a-button type="default" size="large" @click="handleFinish">终 止</a-button>
			<a-button type="default" size="large" v-if="!isWaveFormFrozen" @click="handleFrooze(1)">波形冻结</a-button>
			<a-button type="default" size="large" v-else @click="handleFrooze(0)">解除冻结</a-button>
		</a-flex>
	</div>
</template>

<script setup lang="ts">
import { ref, watch, reactive, defineProps, defineEmits } from "vue";
import type { CSSProperties } from 'vue';
import $bus from "@/utils/bus.ts";
import HrvEchart from '@/components/echartItem.vue';
import HrvVideo from '@/components/videoItem.vue';
import WaveForm from '@/views/waveForm/index.vue';
import settingInfo from '@/settings';
import heartIcon from '@/assets/images/hrv_btn/heart.png';
import heartErrorIcon from '@/assets/images/hrv_btn/heart_red.png';
import exchangeIcon from '@/assets/images/hrv_btn/exchange.png';

const recordRightAreaCss: CSSProperties = {
	textAlign: 'center',
	height: '64vh',
	backgroundColor: '#fff',
	borderRadius: '6px',
};

const Emits = defineEmits(['taskStop', 'taskExit', 'taskDiscontinue']);

const isHeartLink = ref(false);
const isWaveFormFrozen = ref(false);
// const hrvIsChange = ref(false);

const echartHrvRef = ref(null)
const videoHrvRef = ref(null)

const props = defineProps({
	todoInfo: Object,
});

const echartOption = reactive({
	grid: {
		show: true, // 显示网格
		backgroundColor: 'rgba(250, 250, 250, 0.8)', // 网格背景颜色
		borderColor: '#ccc', // 网格线颜色
		borderWidth: 1 // 网格线宽度
	},
	xAxis: {
		type: 'category',
		data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21],
		axisLine: {
			lineStyle: {
				color: '#999'
			}
		},
		splitLine: {
			show: true,
			lineStyle: {
				type: 'dotted',
				color: 'rgba(0,0,0)'
			}
		}
	},
	yAxis: {
		type: 'value',
		axisLine: {
			lineStyle: {
				color: '#999'
			}
		},
		splitLine: {
			lineStyle: {
				type: 'dotted',
				color: 'rgba(0,0,0)'
			}
		}
	},
	series: [
		{
			data: [15, 23, 22, 21, 11, 14, 25, 15, 23, -22, 21, 20, -14, 25, 15, 23, 22, 21, -6, 14, 5],
			type: 'line',
			lineStyle: {
				width: 3,
				color: '#00a6b0'
			},
			itemStyle: {
				color: '#00a6b0'
			}
		}
	]
});

const handleChange = () => {
	// hrvIsChange.value = !hrvIsChange.value;
}

const handleFrooze = (val: number) => {
	isWaveFormFrozen.value = !isWaveFormFrozen.value;
	if (isWaveFormFrozen.value) {
		$bus.emit('waveFormFrozen', val);
	} else {
		$bus.emit('waveFormRestart', val);
	}
}

const handleFinish = () => {
	Emits('taskDiscontinue');
}

const handleExit = () => {
	// Emits('taskExit');
	Emits('taskStop');
}

</script>

<style lang="scss">
.hrv_card_header .ant-card-head {
	background-color: #00a6b0 !important;
	color: #fff !important;
}

.hrv_card_header .ant-card-body {
	padding: 12px 12px 0 !important;
}

.hrv_main_footer {
	height: 7vh;

	.ant-btn {
		width: 10vw;
		border-color: #00a6b0;
		color: #00a6b0;
	}
}

.font_cyan {
	color: #00A6B0 !important;
}
</style>