<template>
	<div>
		<a-card :style="{ ...recordRightAreaCss }">
			<a-row>
				<a-col :span="12" class="font_green">
					<a-flex vertical align="center">
						<span class="font_letter">ECG</span>
						<span class="font_letter">bpm</span>
					</a-flex>
				</a-col>
				<a-col :span="12" class="font_green">
					<a-flex vertical align="center">
						<span class="font_letter">PVCs</span>
						<div style="display: block; position: absolute!important; top: .5rem;">
							<a-image :width="22" :height="22" :src="errorIcon" :preview="false"
								:fallback="settingInfo.noneImgBase64">
							</a-image>
						</div>
					</a-flex>
				</a-col>
				<!-- <a-col :span="24"><a-divider style="border-color: #00a6b0" /></a-col> -->
				<a-col :span="12" class="font_cyan">
					<a-flex vertical align="center">
						<span class="font_number">{{ heartRateArray[0] || 110 }}</span>
						<span class="font_number">{{ heartRateArray[1] || 60 }}</span>
					</a-flex>
				</a-col>
				<a-col :span="12" class="font_green">
					<a-flex vertical align="center" justify="space-between">
						<span class="font_number">{{ heartRateValue || '--' }}</span>
						<span class="font_letter" style="line-height: 5rem;">ST:50</span>
					</a-flex>
				</a-col>
				<a-col :span="24"><a-divider style="border-color: #00a6b0" /></a-col>
				<a-col :span="24" class="font_cyan">
					<a-flex vertical align="center" justify="space-between">
						<span class="font_timer">计时：{{ formattedTime }}</span>
					</a-flex>
				</a-col>
				<a-col :span="24"><a-divider style="border-color: #00a6b0" /></a-col>
				<a-col :span="24">
					<a-flex align="center" justify="space-around">
						<a-image width="4vw" height="4vw" :src="voiceBtn ? voiceIcon : voiceRedIcon" :preview="false"
							:fallback="settingInfo.noneImgBase64" style="display: block" />
						<a-image width="4vw" height="4vw" :src="lightBtn ? lightIcon : lightRedIcon" :preview="false"
							:fallback="settingInfo.noneImgBase64" style="display: block" />
					</a-flex>
				</a-col>
			</a-row>
			<!-- <template class="ant-card-actions" #actions>
				<a-image :width="22" :height="22" :src="voiceBtn ? voiceIcon : voiceRedIcon" :preview="false"
					:fallback="settingInfo.noneImgBase64" style="display: block" />
				<a-image :width="22" :height="22" :src="lightBtn ? lightIcon : lightRedIcon" :preview="false"
					:fallback="settingInfo.noneImgBase64" style="display: block" />
			</template> -->
		</a-card>

		<a-flex justify="space-around" align="center" class="hrv_watch_footer"
			v-if="!props.todoInfo.isWebsocket && props.todoInfo.todoType == '0'">
			<a-button type="default" size="large" :disabled="!isRunning" @click="restartCountdown()">重来</a-button>
			<a-button type="default" size="large" :disabled="!isRunning" @click="pauseCountdown()">暂停</a-button>
			<a-button type="default" size="large" :disabled="isRunning" @click="startCountdown()">启动</a-button>
		</a-flex>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, onBeforeUnmount, watch, onMounted, defineEmits } from "vue";
import type { CSSProperties } from 'vue';
import settingInfo from '@/settings.ts';
import errorIcon from '@/assets/images/hrv_btn/error.png';
import voiceIcon from '@/assets/images/hrv_btn/voice.png';
import lightIcon from '@/assets/images/hrv_btn/warnLight.png';
import voiceRedIcon from '@/assets/images/hrv_btn/voice_red.png';
import lightRedIcon from '@/assets/images/hrv_btn/warnLight_red.png';
import { getConfigDetails } from '@/api/modules/device';
import $bus from "@/utils/bus.ts";
import { useSSEStore } from '@/stores/modules/sseRequest';
import { message } from 'ant-design-vue';
import { todo } from "node:test";

const Emits = defineEmits(['taskStart', 'taskStop']); // 定义自定义事件类型

const recordRightAreaCss: CSSProperties = {
	textAlign: 'center',
	height: '64vh',
	backgroundColor: '#fff',
	borderRadius: '6px',
};

const props = defineProps({
	todoInfo: Object,
});

const heartRateArray = ref<number[]>([]); // 存储心率数据的数组
const heartRateValue = ref<string>(''); // 存储心率数据

const getConfigDetailsData = async () => {
	let res = await getConfigDetails();
	if (res.code == 200) {
		let arr = [];
		if (Number(props.todoInfo.age) >= 60) {
			arr = [res.data.alarmEcgHeartRateOldUpper, res.data.alarmEcgHeartRateOldLower];
		} else if (Number(props.todoInfo.age) <= 14) {
			arr = [res.data.alarmEcgHeartRateChildUpper, res.data.alarmEcgHeartRateChildLower];
		} else {
			arr = [res.data.alarmEcgHeartRateAdultUpper, res.data.alarmEcgHeartRateAdultLower];
		}
		heartRateArray.value = arr;
	} else {
		heartRateArray.value = [];
		console.log(res.msg);
	}
	// 触发自定义事件
	if (props.todoInfo.isWebsocket || props.todoInfo.todoType != '0') {
		Emits('taskStart');
		totalSeconds.value = 0;
		timer = setInterval(() => {
			totalSeconds.value += 1; // 每秒加1
		}, 1000);
	} else {
		totalSeconds.value = (Number(props.todoInfo.hrvDuration) || 10) * 60;
	};
}


// 音量、报警灯状态
const voiceBtn = ref(false);
const lightBtn = ref(true);


// 选择的倒计时初始值
// const selectedHours = ref(0);
const selectedMinutes = ref(5);
const selectedSeconds = ref(0);
// 当前剩余的倒计时时间，以秒为单位动态更新
const totalSeconds = ref(600);
// 表示倒计时是否正在运行，用来防止重复启动计时器
const isRunning = ref(false);
// 存储定时器的句柄，用于管理 setInterval
let timer: any = null;

// computed：这是一个计算属性，动态计算剩余时间的格式化显示。
const formattedTime = computed(() => {
	// const hours = Math.floor(totalSeconds.value / 3600);
	const minutes = Math.floor((totalSeconds.value % 3600) / 60);
	const seconds = totalSeconds.value % 60;
	// 返回格式用于倒计时器的显示 hh:mm:ss
	return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
});

// 将计算结果赋值给 totalSeconds，作为倒计时的起始值。
const initializeCountdown = () => {
	totalSeconds.value = selectedMinutes.value * 60 + selectedSeconds.value;
};

// 暂停倒计时器
const pauseCountdown = () => {
	// 将 isRunning 设置为 false，标记倒计时暂停
	isRunning.value = false;
	// 如果计时器已存在（timer 不为 null），调用 clearInterval 停止计时器，并将 timer 置空
	if (timer) {
		clearInterval(timer);
		timer = null;
	};
	// 关联视频播放
	if (props.todoInfo.todoType != 'HRV') $bus.emit('videoEvent', { type: 2 });
};

// 启动倒计时器
const startCountdown = () => {
	Emits('taskStart'); // 触发自定义事件
	if (totalSeconds.value === 0) {
		initializeCountdown(); // 如果总秒数为0，重新初始化
	}
	// 通过 isRunning 确保倒计时只能启动一次
	if (!isRunning.value) {
		isRunning.value = true;
		// 使用 setInterval 每秒执行一次回调
		timer = setInterval(() => {
			if (totalSeconds.value > 0) {
				totalSeconds.value -= 1; // 每秒减1
			} else {
				// 调用 pauseCountdown 停止计时器
				pauseCountdown();
				// 倒计时结束后，执行相关操作，例如显示提示或执行其他逻辑
				if (props.todoInfo.todoType != 'HRV') {
					$bus.emit('videoEvent', { type: 2 });
					message.success('任务完成');
					Emits('taskStop');
				}
			}
		}, 1000);
	};
	// 关联视频播放
	if (props.todoInfo.todoType != 'HRV') $bus.emit('videoEvent', { type: 1 });
};

// 重新开始倒计时
const restartCountdown = () => {
	// 停止当前计时器
	pauseCountdown();
	// 重置 totalSeconds 为初始值
	initializeCountdown();
	// 重新启动倒计时
	if (props.todoInfo.todoType != 'HRV') $bus.emit('videoEvent', { type: 3 });
	// startCountdown();
};

onMounted(async () => {

	// return false;
	// 获取心率
	await useSSEStore().setHeartRate();
	// 监听SSE事件
	$bus.on('heartRateSSE', (data) => {
		heartRateValue.value = Number(data.heartRate);
	});
	// 获取数据
	await getConfigDetailsData();
})

// 当组件卸载时触发
onBeforeUnmount(() => {
	// 断开连接
	useSSEStore().disconnectSSE('heartRateSource')
	// 清理计时器，防止内存泄漏
	if (timer) {
		clearInterval(timer);
	};
	//在组件卸载的时候，手动解绑定义的绑定事件
	$bus.off('videoEvent');
	//解绑所有的事件
	// $bus.all.clear()
});

watch(
	() => props.todoInfo, // 监听的属性
	(newVal, oldVal) => { // 回调函数，当属性变化时执行
		console.log('props.todoInfo->', newVal, oldVal)
		if (newVal.todoType == 'MAT') {
			totalSeconds.value = 300; // 重置总秒数为0
		} else if (newVal.todoType == 'SCWT') {
			totalSeconds.value = 600;
		}
	},
)

</script>

<style lang="scss">
.hrv_watch_footer {
	height: 7vh;

	.ant-btn {
		border-color: #00a6b0;
		color: #00a6b0;
	}
}

.font_green {
	color: #00B41E !important;
}

.font_cyan {
	color: #00A6B0 !important;
}

.font_number {
	font-size: 3rem !important;
	font-weight: 600 !important;
}

.font_letter {
	font-size: 1.5rem !important;
}

.font_timer {
	font-size: 1.6rem !important;
	color: #FF8F51;
	font-family: '思源黑体';
	font-weight: 600;
}
</style>