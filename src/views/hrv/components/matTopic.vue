<template>
    <div>
        <a-card :style="{ ...recordRightAreaCss }" class="hrv_card_header">
            <template #title>
                <a-flex justify="space-between" align="center">
                    <span>{{ matStep <= 2 ? '放松阶段' : (matStep == 3 || matStep == 5 || matStep == 7 ? '' : topicTitle)
                            }}</span>
                            <span v-if="topicTitleTime">{{ `答题倒计时\r${topicTitleTime}\r秒` }}</span>
                </a-flex>
            </template>

            <div v-if="matStep == 0" class="hrvReadyBtn">
                预放松阶段：此处播放实验介绍相关视频，5min后进入放松阶段。
                <br />
                <br />
                <a-button type="primary" shape="round" size="large" @click="handleStart">跳 过</a-button>
            </div>
            <div v-if="matStep == 1" class="hrvReadyBtn">
                <a-button type="primary" shape="round" size="large" @click="handleStart">放 松 阶 段</a-button>
            </div>
            <hrv-video ref="videoHrvRef" v-if="matStep == 2" style="height: 56vh" @start="handleStart" />
            <div v-if="matStep == 3" class="hrvReadyBtn">
                ES题组：由包含1位数的加减乘除组成（如7*8或7+2），每题限时5s
                <br />
                <br />
                <a-button type="primary" shape="round" size="large" @click="handleStart">准 备 开 始</a-button>
            </div>
            <mat-topic-es v-if="matStep == 4" style="height: 47vh" @topic="handleTopic" @submit="setAnswerData"
                @start="handleStart" />
            <div v-if="matStep == 5" class="hrvReadyBtn">
                DS题组：由其结果为3位数涉及借位或者错位的的加减乘除运算组成（如33×12或者112+124），每题限时15s。
                <br />
                <br />
                <a-button type="primary" shape="round" size="large" @click="handleStart">继 续 作 答</a-button>
            </div>
            <mat-topic-ds v-if="matStep == 6" style="height: 47vh" @topic="handleTopic" @submit="setAnswerData"
                @start="handleStart" />
            <a-result v-if="matStep == 7" status="success" title="当前题目已作答完毕" sub-title="请联系医生进行下一步"></a-result>
        </a-card>

        <!-- <a-flex justify="space-around" align="center" class="hrv_main_footer">
            <a-button type="default" size="large">退 出</a-button>
            <a-button type="default" size="large">终 止</a-button>
            <a-button type="default" size="large">波形冻结</a-button>
        </a-flex> -->
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, reactive, defineProps } from "vue";
import type { CSSProperties } from 'vue';
import { useRoute } from "vue-router";
import { commit_event } from "@/api/modules/inspect"
import { tempUpload } from "@/api/modules/unline";
import HrvVideo from '@/components/videoItem.vue';
import MatTopicEs from '@/views/hrv/Mat/matItem_es.vue';
import MatTopicDs from '@/views/hrv/Mat/matItem_ds.vue';
import settingInfo from '@/settings';
import heartIcon from '@/assets/images/hrv_btn/heart.png';
import heartErrorIcon from '@/assets/images/hrv_btn/heart_red.png';
import exchangeIcon from '@/assets/images/hrv_btn/exchange.png';

const recordRightAreaCss: CSSProperties = {
    textAlign: 'center',
    height: '64vh',
    backgroundColor: '#fff',
    borderRadius: '6px',
};

const route = useRoute();
const videoHrvRef = ref(null)
const isShowVideo = ref(false)
const matStep = ref(3); // 0: 预放松阶段(120S) 1：等待确认 2：ES阶段（100s=15s引导+85s答题） 3：等待确认 4：DS阶段：（80s=20s引导+60s答题） 6：结束
const answerObj = ref({
    inspectListId: route.query.id, // 问卷ID
    taskType: 'MAT',
    answerData: [
        // {
        // 	timestamp: null, // 时间戳
        // 	eventName: null, // 事件名称 start：开始答题 end：结束答题 topic：答题
        // 	topicType: null, // 题目类型 0：mat 1：一致性 2：非一致性
        // 	consistencyDTO: { // 一致性
        // 		topicSort: null, // 题目序号
        // 		topicCharacter: null, // 题目
        // 		topicGraphic: null, // 题目颜色
        // 		correctAnswer: null, // 正确答案
        // 		actualAnswer: null, // 实际答案
        // 	},
        // 	nonConsistencyDTO: {}, // 非一致性
        // 	matTopicDTO: {} // mat
        // }
    ], // 答题结果
}); // 存储答题结果

const props = defineProps({
    todoInfo: Object,
});

const handleChange = () => {
    // hrvIsChange.value = !hrvIsChange.value;
}

const handleStart = async () => {
    matStep.value += 1;
    topicTitleTime.value = 0;
    if (matStep.value == 7) {
        let obj = {
            'timestamp': new Date().getTime(), // 时间戳
            'eventName': 'end', // 事件名称 start：开始答题 end：结束答题 topic：答题
            'topicType': 0, // 题目类型 0：mat 1：一致性 2：非一致性
        };
        await setAnswerData(obj);
        await commit_event(answerObj.value);
        await tempUpload({
            id: answerObj.value.inspectListId,
            isTempTask: props.todoInfo.isTempTask || 0, // 是否临时任务
            isOfflineTask: props.todoInfo.isOfflineTask || 0, // 是否离线任务
        })
    }
}

const initStepStart = () => {
    let obj = {
        'timestamp': new Date().getTime(), // 时间戳
        'eventName': 'start', // 事件名称 start：开始答题 end：结束答题 topic：答题
        'topicType': 0, // 题目类型 0：mat 1：一致性 2：非一致性
    };
    setAnswerData(obj)
}

const setAnswerData = (data) => {
    // console.log('setAnswerData->', data)
    answerObj.value.answerData.push(data);
    console.log('MAT.answerData->', answerObj.value)
}

const topicIndex = ref(1);
const topicTotal = ref(1);
const topicTitle = ref('');
const topicTitleTime = ref(0);
const handleTopic = (time, index, total, history) => {
    // console.log('index, total->', index, total)
    topicIndex.value = Number(index) ?? 1;
    topicTotal.value = Number(total) ?? 1;
    topicTitle.value = index <= total ? `第${index}道题(共${total}道)：请输入计算结果` : '所有题目已完成';
    if (time) topicTitleTime.value = time;
    if (total && index > total) {
        topicTitleTime.value = 0;
        console.log('所有题目已完成MAT', history);
        handleStart();
    }
}

onMounted(() => {
    // 记录放松阶段起始时间
    initStepStart();
    setTimeout(() => {
        isShowVideo.value = false;
        console.log('isShowVideo.value->', isShowVideo.value)
    }, 2000)
})

</script>

<style lang="scss">
.hrv_card_header .ant-card-head {
    background-color: #00a6b0 !important;
    color: #fff !important;
}

.hrv_main_footer {
    height: 7vh;

    .ant-btn {
        width: 10vw;
        border-color: #00a6b0;
        color: #00a6b0;
    }
}

.font_cyan {
    color: #00A6B0 !important;
}
</style>