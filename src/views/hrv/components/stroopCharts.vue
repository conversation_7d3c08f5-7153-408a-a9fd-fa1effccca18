<template>
	<div>
		<a-card :style="{ ...recordRightAreaCss }" class="hrv_card_header">
			<template #title>
				<a-flex justify="space-between" align="center">
					<span>{{ scwtStep == 2 || scwtStep == 1 ? '放松阶段' :
			(whiteTitleList.indexOf(scwtStep) > -1) ? '' : topicTitle }}</span>
					<span v-if="topicTitleTime">{{ `答题倒计时\r${topicTitleTime}\r秒` }}</span>
				</a-flex>
			</template>

			<!-- <hrv-video ref="videoHrvRef" v-if="scwtStep == 0" style="height: 56vh" @start="handleStart" /> -->
			<div v-if="scwtStep == 0" class="hrvReadyBtn">
				预放松阶段：此处播放实验介绍相关视频，5min后进入放松阶段。
				<br />
				<br />
				<a-button type="primary" shape="round" size="large" @click="handleStart">跳 过</a-button>
			</div>
			<div v-if="scwtStep == 1" class="hrvReadyBtn">
				<a-button type="primary" shape="round" size="large" @click="handleStart">放 松 阶 段</a-button>
			</div>
			<hrv-video ref="videoHrvRef" v-if="scwtStep == 2" style="height: 56vh" @start="handleStart" />
			<div v-if="scwtStep == 3" class="hrvReadyBtn">
				<a-button type="primary" shape="round" size="large" @click="handleStart">准 备 答 题</a-button>
				<br />
				提示：即将进入一致性阶段，你需要点击与圆形一致的颜色。
			</div>
			<scwt-topic-consistency v-if="scwtStep == 4" style="height: 47vh" @topic="handleTopic"
				@submit="setAnswerData" @start="handleStart" />
			<div v-if="scwtStep == 5" class="hrvReadyBtn">
				<a-button type="primary" shape="round" size="large" @click="handleStart">继 续 答 题</a-button>
				<br />
				提示：即将进入非一致性阶段，你需要点击与文字颜色一致的圆形。
			</div>
			<scwt-topic-unconsistency v-if="scwtStep == 6" style="height: 47vh" @topic="handleTopic"
				@submit="setAnswerData" @start="handleStart" />
			<a-result status="success" title="当前题目已作答完毕" sub-title="请联系医生进行下一步" v-if="scwtStep == 7"></a-result>
		</a-card>

		<!-- <a-flex justify="space-around" align="center" class="hrv_main_footer">
			<a-button type="default" size="large">退 出</a-button>
			<a-button type="default" size="large">终 止</a-button>
			<a-button type="default" size="large">波形冻结</a-button>
		</a-flex> -->
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, reactive, defineProps } from "vue";
import type { CSSProperties } from 'vue';
import { useRoute } from "vue-router";
import { commit_event } from "@/api/modules/inspect"
import { tempUpload } from "@/api/modules/unline";
import HrvVideo from '@/components/videoItem.vue';
import ScwtTopicConsistency from '@/views/hrv/Scwt/stroopItem_consistency.vue';
import ScwtTopicUnconsistency from '@/views/hrv/Scwt/stroopItem_unconsistency.vue';
import settingInfo from '@/settings';
import heartIcon from '@/assets/images/hrv_btn/heart.png';
import heartErrorIcon from '@/assets/images/hrv_btn/heart_red.png';
import exchangeIcon from '@/assets/images/hrv_btn/exchange.png';

const recordRightAreaCss: CSSProperties = {
	textAlign: 'center',
	height: '64vh',
	backgroundColor: '#fff',
	borderRadius: '6px',
};

const route = useRoute();
const videoHrvRef = ref(null)
const isShowVideo = ref(false)
const scwtStep = ref(0); // 0: 预放松阶段(300S) 1: 学习阶段（确认开始） 2：放松阶段（130s） 3：一致性阶段（80s=10s引导+70s答题） 4：等待确认 5：非一致性阶段：（90s=20s引导+70s答题） 6：结束
const answerObj = ref({
	inspectListId: route.query.id, // 问卷ID
	taskType: 'SCWT',
	answerData: [
		// {
		// 	timestamp: null, // 时间戳
		// 	eventName: null, // 事件名称 start：开始答题 end：结束答题 topic：答题
		// 	topicType: null, // 题目类型 0：mat 1：一致性 2：非一致性
		// 	consistencyDTO: { // 一致性
		// 		topicSort: null, // 题目序号
		// 		topicCharacter: null, // 题目
		// 		topicGraphic: null, // 题目颜色
		// 		correctAnswer: null, // 正确答案
		// 		actualAnswer: null, // 实际答案
		// 	},
		// 	nonConsistencyDTO: {}, // 非一致性
		// 	matTopicDTO: {} // mat
		// }
	], // 答题结果
}); // 存储答题结果
const whiteTitleList = [0, 3, 5, 7];

const props = defineProps({
	todoInfo: Object,
});

const handleChange = () => {
	// hrvIsChange.value = !hrvIsChange.value;
}

const topicIndex = ref(1);
const topicTotal = ref(1);
const topicTitle = ref('');
const topicTitleTime = ref(0);
const handleTopic = (time: Number, index: Number, total: Number, history) => {
	console.log('index, total->', index, total)
	topicIndex.value = Number(index) ?? 1;
	topicTotal.value = Number(total) ?? 1;
	topicTitle.value = index <= total ? `第${index}道题(共${total}道)：请选择与圆形/字体颜色一致的选项` : '所有题目已完成';
	if (time) topicTitleTime.value = time;
	if (Number(total) && Number(index) > Number(total)) {
		console.log('所有题目已完成', history)
		handleStart()
	}
}

const handleStart = async () => {
	scwtStep.value += 1;
	// console.log('scwtStep.value->', scwtStep.value)
	topicTitleTime.value = 0;
	if (scwtStep.value == 7) {
		let obj = {
			'timestamp': new Date().getTime(), // 时间戳
			'eventName': 'end', // 事件名称 start：开始答题 end：结束答题 topic：答题
			'topicType': 2, // 题目类型 0：mat 1：一致性 2：非一致性
		};
		await setAnswerData(obj);
		await commit_event(answerObj.value);
		await tempUpload({
			id: answerObj.value.inspectListId,
			isTempTask: props.todoInfo.isTempTask || 0, // 是否临时任务
			isOfflineTask: props.todoInfo.isOfflineTask || 0, // 是否离线任务
		})
	}
}

const initStepStart = () => {
	let obj = {
		'timestamp': new Date().getTime(), // 时间戳
		'eventName': 'start', // 事件名称 start：开始答题 end：结束答题 topic：答题
		'topicType': 1, // 题目类型 0：mat 1：一致性 2：非一致性
	};
	setAnswerData(obj)
}

const setAnswerData = (data) => {
	// console.log('setAnswerData->', data)
	answerObj.value.answerData.push(data);
	console.log('SCWT.answerData->', answerObj.value)
}

onMounted(() => {
	// 记录放松阶段起始时间
	initStepStart();
	// let startItem = {
	// 	timestamp: new Date().getTime(), // 时间戳
	// 	eventName: 'start', // 事件名称 start：开始答题 end：结束答题 topic：答题
	// 	topicType: 1, // 题目类型 0：mat 1：一致性 2：非一致性
	// };
	// answerObj.value.answerData = [];
	// answerObj.value.answerData.push(startItem);

	setTimeout(() => {
		isShowVideo.value = false;
		console.log('isShowVideo.value->', isShowVideo.value)
	}, 2000)
})

</script>

<style lang="scss">
.hrv_card_header .ant-card-head {
	background-color: #00a6b0 !important;
	color: #fff !important;
}

.hrvReadyBtn {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	height: 47vh;
}

.hrv_main_footer {
	height: 7vh;

	.ant-btn {
		width: 10vw;
		border-color: #00a6b0;
		color: #00a6b0;
	}
}

.font_cyan {
	color: #00A6B0 !important;
}
</style>