<template>
	<div>
		<a-flex justify="space-between" align="center" class="hrv_main_header">
			<span>{{ `${routeQuery.patient}【${routeQuery.title}】` }}</span>
			<a-button type="primary" size="large" @click="handleToListPage">返回</a-button>
		</a-flex>
		<a-row class="hrv_main_area">
			<a-col :span="16">
				<HrvCharts v-if="routeQuery.todoType == '0'" :todoInfo="routeQuery" @taskStop="taskStop"
					@taskExit="taskExit" @taskDiscontinue="taskDiscontinue" />
				<ScwtCharts v-else-if="routeQuery.todoType == '1'" :todoInfo="routeQuery" />
				<MatTopic v-else-if="routeQuery.todoType == '2'" :todoInfo="routeQuery" />
			</a-col>
			<a-col :span="7" :offset="1">
				<HrvWatch :todoInfo="routeQuery" @taskStart="taskStart" @taskStop="taskStop" />
			</a-col>
		</a-row>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from "vue-router";
import { taskExamStart, taskExamStop, taskExamExit, taskExamDiscontinue } from '@/api/modules/inspect';
import { useSSEStore } from '@/stores/modules/sseRequest';
import HrvCharts from "./components/hrvCharts.vue";
import HrvWatch from "./components/hrvWatch.vue";
import ScwtCharts from './components/stroopCharts.vue';
import MatTopic from './components/matTopic.vue';

const router = useRouter();
const route = useRoute();

const handleToListPage = function () {
	router.go(-1)
}

const routeQuery = ref({
	patient: '',
	title: '',
	age: 0,
	todoType: '0',
	id: 0,
	hrvDuration: 0,
	isTempTask: 0, // 是否临时任务
	isOfflineTask: 0, // 是否离线任务
	isWebsocket: 0, // 是否websocket
})

const isGeneralTask = computed(() => {
	return !Number(routeQuery.value.isTempTask) && !Number(routeQuery.value.isOfflineTask);
})

const taskStart = async () => {
	let res = await taskExamStart({ taskId: routeQuery.value.id, age: routeQuery.value.age, time: routeQuery.value.hrvDuration, isGeneralTask: isGeneralTask.value });
	if (res.code == 200) {
		// console.log('taskStart', res)
	} else {
		console.log(res.msg);
	}
}

const taskStop = async () => {
	let res = await taskExamStop({ taskId: routeQuery.value.id, isGeneralTask: isGeneralTask.value });
	if (res.code == 200) {
		// console.log('taskStop', res)
		handleToListPage();
	} else {
		console.log(res.msg);
	}
}

const taskExit = async () => {
	let res = await taskExamExit({ taskId: routeQuery.value.id, isGeneralTask: isGeneralTask.value });
	if (res.code == 200) {
		// console.log('taskStop', res)
		handleToListPage();
	} else {
		console.log(res.msg);
	}
}

const taskDiscontinue = async () => {
	let isGeneralTask = !routeQuery.value.isTempTask && !routeQuery.value.isOfflineTask;
	let res = await taskExamDiscontinue({ taskId: routeQuery.value.id, isGeneralTask: isGeneralTask });
	if (res.code == 200) {
		// console.log('taskStop', res)
		handleToListPage();
	} else {
		console.log(res.msg);
	}
}

onMounted(() => {
	console.log('routeQuery', routeQuery, route?.query, isGeneralTask.value)
	if (route?.query) {
		routeQuery.value = { ...routeQuery.value, ...route?.query }
	}
})
// 当组件卸载时触发
onBeforeUnmount(() => {
	// 断开连接
	useSSEStore().disconnectSSE('heartRateSource')
});
</script>

<style>
.hrv_main_header {
	padding: .5vh 10px 0;
	color: #000;
	height: 5vh;
	font-family: '思源黑体';
	font-size: 1rem;
	font-weight: bold;
}

.hrv_main_area {
	padding: 0 3vw;
	margin-top: 2vh;
}
</style>