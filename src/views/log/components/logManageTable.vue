<template>
  <a-table :columns="columns" :data-source="tableData" :pagination="paginationObj" class="log_table">
    <template #emptyText>
      <a-empty description="暂无数据" />
    </template>
    <template #headerCell="{ column, record }" style="baclground-color: #F4F9FA;"></template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'patientName'">
        <a>
          {{ record.patientName }}
        </a>
      </template>
      <template v-else-if="column.key === 'detectionTime'">
        {{ formatTime() }}
      </template>
      <template v-else-if="column.key === 'uploadStatus'">
        <span>
          <a-tag size="large" :color="record.uploadStatus ? 'green' : 'cyan'">
            {{ record.uploadStatus ? '已上传' : '未上传' }}
          </a-tag>
        </span>
      </template>
      <template v-else-if="column.key === 'action'">
        <a-button type="primary" size="size" v-if="!record.uploadStatus" @click="handleUpload(record)">上 传</a-button>
      </template>
    </template>
  </a-table>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { cloneDeep } from "lodash-es";
import { useUnlineTodoStore } from "@/stores/modules/unlineTodo.ts";
import { tempUpload } from "@/api/modules/unline";
import { manageStatistics } from "@/api/modules/unline";
import moment from 'moment';

const useUnlineTodo = useUnlineTodoStore();

const columns = [
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    key: 'patientName',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    key: 'age',
  },
  {
    title: '测评项目',
    dataIndex: 'inspectItem',
    key: 'inspectItem',
  },
  {
    title: '医生',
    key: 'doctorName',
    dataIndex: 'doctorName',
  },
  {
    title: '检测时间',
    key: 'detectionTime',
    dataIndex: 'detectionTime',
  },
  {
    title: '上传状态',
    key: 'uploadStatus',
    dataIndex: 'uploadStatus',
  },
  {
    title: '操作',
    key: 'action',
  },
];

const tableData = ref([]);

const formatTime = function () {
  let date = new Date();
  return moment(date).format('YYYY-MM-DD HH:mm:ss')
}

const paginationObj = ref({
  pageIndex: 1,
  pageSize: 6,
  total: 0
})

const handleUpload = async (row: any) => {
  const { code, success } = await tempUpload({
    id: row.id,
    isTempTask: 0, // 是否临时任务
    isOfflineTask: 0, // 是否离线任务
  })
  if (success) await getData();
}

const getData = async () => {
  const { data } = await manageStatistics(paginationObj.value);
  console.log('manageStatistics->', data)
  tableData.value = data.list;
  paginationObj.value.total = data.total;

}

onMounted(async () => {
  await getData();
})
</script>

<style>
.log_table {
  margin-top: 2vh;
  padding: 0 1.5vw;
}
</style>