<template>
	<div>
		<a-flex justify="space-between" align="center" class="list_main_header">
			<a-segmented v-model:value="terminalHrv" :options="terminalOptions" size="large"
				class="log_operate_segmented" />
			<a-button type="primary" size="large" @click="handleToListPage">返回</a-button>
		</a-flex>
		<LogTable v-if="terminalHrv == '未上报'" />
		<LogManageTable v-if="terminalHrv == '平台下发'" />
		<LogLocalTable v-if="terminalHrv == '本地创建'" />
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { useRouter } from "vue-router";
import LogTable from './components/logTable.vue'
import LogLocalTable from './components/logLocalTable.vue'
import LogManageTable from '@/views/log/components/LogManageTable.vue'
const router = useRouter();
const open = ref<boolean>(false);
const terminalOptions = reactive(['平台下发', '本地创建', '未上报']);
const terminalHrv = ref<string>(terminalOptions[0]);

const handleToListPage = function () {
	router.go(-1);
}

</script>

<style>
.list_area_input {
	width: 45vw;
	height: 4vh;
}

.list_main_header {
	margin-top: 1vh;
	padding: 0 10px;
	color: #000;
	height: 5vh;
}

.list_main_area {
	margin: 0 3vw;
	margin-top: 2vh;
}

.log_operate_segmented .ant-segmented-item-selected {
	background-color: #00A6B032;
	border: #00A6B0 1px solid;
	color: #00A6B0;
}
</style>