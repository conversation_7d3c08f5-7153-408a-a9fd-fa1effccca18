<template>
	<div>
		<a-form ref="formRef" :model="formState" name="basic" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }"
			autocomplete="off" @finish="onFinish" @finishFailed="onFinishFailed">
			<a-form-item label="账号" name="username" :rules="[{ required: true, message: '请输入账号!' }]">
				<a-input v-model:value="formState.username" v-longpress="2000" />
			</a-form-item>

			<a-form-item label="密码" name="password" :rules="[{ required: true, message: '请输入密码!' }]">
				<a-input-password v-model:value="formState.password" @keyup.enter.native="onSubmit"
					v-longpress="2000" />
			</a-form-item>
			<!-- <a-form-item name="remember" :wrapper-col="{ offset: 16, span: 8 }">
			<a-checkbox v-model:checked="formState.remember">记住账号</a-checkbox>
			</a-form-item> -->
			<!--  <a-form-item :wrapper-col="{ offset: 8, span: 16 }">
			<a-button type="primary" html-type="submit" size="large">登录</a-button>
			</a-form-item> -->
		</a-form>
		<br />
		<div class="login-btn">
			<a-button size="large" type="primary" :loading="loading" style="width: 100%;" @click="onSubmit">
				登录
			</a-button>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, toRaw } from "vue";
import { useRouter } from "vue-router";
import Spin from "@/components/Loading/fullScreen";
import { useGlobalStore } from '@/stores/modules/global';
import { userMessageStore } from "@/stores/modules/userMessage";
import { message } from 'ant-design-vue';
import md5 from "md5";

const router = useRouter();
const global = useGlobalStore();
const userStore = userMessageStore();

interface FormState {
	username: string;
	password: string;
	remember: boolean;
}

interface DelayLoading {
	delay: number;
}

const loading = ref<boolean | DelayLoading>(false);
const formRef = ref();

const formState = reactive<FormState>({
	username: 'admin',
	password: '123456',
	remember: true,
});
const onFinish = (values: any) => {
	console.log('Success:', values);
};

const onFinishFailed = (errorInfo: any) => {
	console.log('Failed:', errorInfo);
};

const onSubmit = () => {
	// loading.value = true;
	Spin.show(' ');
	formRef.value.validate()
		.then(() => {

			// ----------------------------------------------
			// router.push('/index');
			// message.success('登录成功!');
			// sessionStorage.setItem('token', '123456');
			// return false;
			// ----------------------------------------------
			// console.log('values', formState, toRaw(formState));
			userStore.login({ ...formState, password: md5(formState.password), username: formState.username }).then((res) => {
				// console.log('loginForm', res)
				// 登录成功后，保存用户信息到本地存储
				router.push('/index');
				message.success('登录成功!');
				Spin.hide();
				// userMessageStore().setToken(res.data.token);
				window.electronLog.info('登录成功，用户名：', formState.username);
			})

		})
		.catch((error: any) => {
			Spin.hide();
			console.log('error', error);
		});
};

onMounted(() => {
	// 监听 enter 事件（调用登录）
	// document.onkeydown = (e: KeyboardEvent) => {
	// 	e = (window.event as KeyboardEvent) || e;
	// 	if (e.code === "Enter" || e.code === "enter" || e.code === "NumpadEnter") {
	// 		if (loading.value) return;
	// 		if (!loginForm.username) { // 聚焦账号
	// 			return usernameRef.value.focus()
	// 		}
	// 		if (!loginForm.password) { // 聚焦密码
	// 			return passwordRef.value.focus()
	// 		}
	// 		login(loginFormRef.value); // 调用登录
	// 	}
	// };
	// 清空
	// localStorage.clear()
	// userStore.setMenuType(1);
});

// defineExpose({ jumpFn })
</script>

<style scoped lang="scss">
@import "../index.scss";
</style>