<template>
    <div style="overflow: hidden;">
        <a-form ref="networkFormRef" :model="networkConfig" name="networkConfig" :label-col="{ span: 6 }"
            :wrapper-col="{ span: 14 }" autocomplete="off" class="warn_config_form">
            <a-form-item label="WIFI设置">
                <a-input v-model:value="networkConfig.wifi" />
            </a-form-item>
            <a-form-item label="数据服务">
                <a-input v-model:value="networkConfig.baseUrl" />
            </a-form-item>
            <a-form-item label="报告服务">
                <a-input v-model:value="networkConfig.reportUrl" />
            </a-form-item>
            <a-form-item label="升级路径">
                <a-radio-group :options="uploadOptions" :value="networkConfig.upload" />
                <a-upload :file-list="fileList" name="file" :multiple="true" action="" :headers="headers"
                    @change="handleChange" v-if="!(networkConfig.upload - 1)">
                    <a-button>
                        <upload-outlined></upload-outlined>
                        点击上传
                    </a-button>
                </a-upload>
            </a-form-item>
            <a-form-item label="连接状态">
                <a-flex :style="{ 'width': '100%' }" justify="space-around" align="center">
                    <div v-for="item in statusOptions" :key="item.label">
                        <a-badge-ribbon :text="item.status ? '在线' : '离线'" :color="item.status ? 'green' : 'red'"
                            style="margin-top: -15px;">
                            <a-card size="small" title="">{{ item.label }}</a-card>
                        </a-badge-ribbon>
                    </div>
                </a-flex>
            </a-form-item>
            <a-form-item label="本地存储">
                <a-flex align="center" justify="flexd-start">
                    <a-input-number id="inputNumber" v-model:value="networkConfig.dataStorage" :min="1" :max="365" />
                    &nbsp;&nbsp;天
                </a-flex>
            </a-form-item>
        </a-form>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, UnwrapRef } from 'vue';
import { message } from 'ant-design-vue';
import { UploadOutlined } from '@ant-design/icons-vue';
// import storage from '@/utils/storage'
// import { userMessageStore } from '@/stores/modules/userMessage'
import storage from "@/utils/storage";
import { networkConfigUpdate } from '@/api/modules/device';
// const userStore = userMessageStore()

interface netFormItem {
    wifi: string | null;
    baseUrl: string | null;
    reportUrl: string | null;
    upload: number;
    dataStorage: number;
};

interface FileItem {
    uid: string;
    name?: string;
    status?: string;
    response?: string;
    url?: string;
};

interface FileInfo {
    file: FileItem;
    fileList: FileItem[];
};

export default defineComponent({
    components: {
        UploadOutlined,
    },
    setup() {
        const networkFormRef = ref();
        // const client = computed(() => userStore.clientGet);
        const networkConfig: UnwrapRef<netFormItem> = reactive({
            wifi: '',
            baseUrl: storage.local.has("serverUrl") ? storage.local.get("serverUrl") : "http://127.0.0.1:8080",
            reportUrl: storage.local.has("reportUrl") ? storage.local.get("reportUrl") : "http://127.0.0.1:9000",
            upload: 1,
            dataStorage: storage.local.has("logSaveDate") ? Number(storage.local.get("logSaveDate")) : 60,
        });

        const uploadOptions = [
            { label: '在线', value: 2 },
            { label: '离线', value: 1 },
        ];

        const statusOptions = [
            { label: '数据服务', status: 1 },
            { label: '平台服务', status: 1 },
            { label: '报告服务', status: 0 },
        ]

        const handleChange = (info: FileInfo) => {
            if (info.file.status !== 'uploading') {
                console.log(info.file, info.fileList);
            }
            if (info.file.status === 'done') {
                message.success(`${info.file.name} file uploaded successfully`);
            } else if (info.file.status === 'error') {
                message.error(`${info.file.name} file upload failed.`);
            }
        };

        const fileList = ref([]);

        const submitFn = () => {
            console.log('networkConfig', networkConfig);
            storage.local.set("serverUrl", networkConfig.baseUrl);
            storage.local.set("reportUrl", networkConfig.reportUrl);
            storage.local.set("logSaveDate", networkConfig.dataStorage);
            networkConfigUpdate({
                "netServerUrl": networkConfig.baseUrl,
                "netPlatformUrl": networkConfig.baseUrl,
                "netReportUrl": networkConfig.reportUrl,
                "netOnlineUpgrade": networkConfig.upload
            }).then((res) => {
                if (res.code === 200) {
                    message.success('配置成功');
                } else {
                    message.error(res.msg);
                }
            })
        }

        return {
            networkFormRef,
            fileList,
            headers: {
                authorization: 'authorization-text',
            },
            handleChange,
            networkConfig,
            uploadOptions,
            statusOptions,
            submitFn
        };
    },
});

</script>

<style lang="scss">
.warn_config_header {
    padding: 0 3vw;
    color: #000;
    height: 5vh;
}

.warn_config_grid {
    height: 73vh;
    padding: 1vh 3vw;

    // .warn_config_card {
    //     height: 33vh;
    // }

    // .warn_config_card .ant-card-head {
    //     background-color: #00a6b0 !important;
    //     color: #fff !important;
    // }

    .warn_config_form {
        font-size: 1rem;
        font-family: emoji;
        text-align: left;

        .warn_config_input {
            width: 10vw
        }
    }

}
</style>