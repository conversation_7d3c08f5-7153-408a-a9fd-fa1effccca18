	.login-container{
		height: 100%;
		min-height: 100vh;
		background-color: #eeeeee;
		// background-image: url("@/assets/images/login/login_bg.png");
		background-size: 100% 100%;
		background-size: cover;
		background-image: url('../../assets/images/login-bg.png');
		overflow: hidden;
		.login-header-left{
			position: absolute;
			top: 5px;
			left: 5px;
			color: #eeeeee;
			font-family: 'Courier New', Courier, monospace;
			font-weight: 900;
		}
		.login-header-right{
			// position: absolute;
			display: flex;
			width: 20vw;
			height: 3vh;
			justify-content: space-between;
			align-items: center;
			float: right;
			margin-top: 5px;
			margin-right: 5px;
			color: #eeeeee;
			font-family: 'Courier New', Courier, monospace;
			font-weight: 900;
		}
		.login-box {
		  box-sizing: border-box;
		  width: 96.5%;
		  height: 94%;
		  padding: 0 50px;
		  border-radius: 10px;
		  display: flex;
		  align-items: center;
		  margin-left: 52%;
		  margin-top: 15%;
		  .login-form {
		    width: 390px;
		    padding: 80px 40px;
		    // background-color: var(--a-bg-color);
			background: rgba(255, 255, 255, 0.6);
		    border-radius: 20px;
			border: 2px solid #00A6B0;
			box-sizing: border-box;
		    // box-shadow: rgb(0 0 0 / 10%) 0 2px 10px 2px;
		  }
		}
	}
	.logo-text{
		padding:0;
		margin: 0;
		font-weight: 600;
		color: #333333;
		margin-bottom: 10px;
		&.logo-text-line{
			position:relative;
			&::after{
				content: '';
				position: absolute;
				bottom:-8px;
				left:0;
				height: 3px;
				width: 5em;
				background: #00A6B0;
			}
		}
	}
	.welcome{
		text-transform: uppercase;
		font-size: 12px;
	}
	
	.a-form{
		margin-bottom: 60px;
		.a-form-item.password{
			margin-bottom: 0;
		}
	}
	
	.login-btn{
		.a-button{
			width: 100%;
		}
	}