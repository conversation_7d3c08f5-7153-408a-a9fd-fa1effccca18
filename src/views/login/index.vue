<template>
	<div class="login-container">
		<div class="login-header-left" @click="openDeviceDialog">
			<a-image :width="40" :height="40" :src="logoImg" :preview="false"
				fallback="data:image/png;base64,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" />
			<span> 心率变异采集分析系统</span>
		</div>
		<div class="login-header-right">
			<div class="login-tab-ico">
				<a-image :width="25" :height="15" :src="deviceIcon01" :preview="false"
					:fallback="settingInfo.noneImgBase64" />
			</div>
			<div class="login-tab-ico">
				<a-image :width="25" :height="15" :src="daolianIcon01" :preview="false"
					:fallback="settingInfo.noneImgBase64" />
			</div>
			<div class="login-tab-ico" @click="openNetworkDialog()">
				<a-image :width="25" :height="15" :src="serverIcon01" :preview="false"
					:fallback="settingInfo.noneImgBase64" />
			</div>
			<BatteryItem :proIsCharge="false" />
		</div>
		<div class="login-box">
			<div class="login-form">
				<h2 class="logo-text">欢迎使用</h2>
				<h2 class="logo-text logo-text-line">{{ '心率变异采集分析系统' }}</h2>
				<p class="welcome">Welcome to Login</p>
				<login-form ref="loginFormRef" />
				<!-- <PasswordDialog v-model="firstLogin" @submit="onSubmit" /> -->
			</div>
		</div>

		<!-- 服务配置 -->
		<a-modal v-model:open="serverVisable" width="60vw" class="warnConfig-dialog" title="网络设置"
			@ok="handleServerConfigSubmit" okText="提交" cancelText="取消" :okButtonProps="okButtonProps">
			<server-config ref="serverFormRef" />
		</a-modal>

		<!-- 设备配置 -->
		<a-modal v-model:open="deviceVisable" width="30vw" class="warnConfig-dialog" title="设备编码"
			@ok="handleDeviceNumberSubmit" okText="提交" cancelText="取消" :okButtonProps="okButtonProps">
			<br />
			<a-input v-model:value="deviceNumber" size="large" placeholder="请输入" />
			<br />
		</a-modal>
	</div>
</template>

<script setup lang="ts" name="login">
import { onMounted, ref, computed } from "vue";
import { userMessageStore } from "@/stores/modules/userMessage";
import LoginForm from "@/views/login/components/LoginForm.vue";
import logoImg from '@/assets/images/logo.png';
import settingInfo from '@/settings';
import deviceIcon01 from '@/assets/images/header_btn/device_1.png';
import daolianIcon01 from '@/assets/images/header_btn/daolian_1.png';
import serverIcon01 from '@/assets/images/header_btn/server_1.png';
// const userStore = useUserStore();
// const systemStore = useSystemStore();
// const firstLogin = computed(()=> userStore.$state.firstLogin);
// const qySysConfig = computed(()=> systemStore.qySysConfigGet);
import ServerConfig from "./components/ServerConfigForm.vue"
import BatteryItem from "@/components/batteryItem.vue"
import { getConfigDetails } from '@/api/modules/device';
import storage from "@/utils/storage";

const userStore = userMessageStore();
const imageUrl = ref('');
const loginFormRef = ref();
const serverFormRef = ref();
const serverVisable = ref<boolean>(false);
const deviceVisable = ref<boolean>(false);

const deviceNumber = ref<string>(''); // 设备编码

const onSubmit = () => {
	loginFormRef.value?.jumpFn();
}

// 打开设备绑定弹框
const openDeviceDialog = () => {
	deviceVisable.value = true;
}

// 打开数据服务弹框
const openNetworkDialog = () => {
	serverVisable.value = true;
}

// 数据服务设置提交
const handleServerConfigSubmit = (e: MouseEvent) => {
	// console.log(e);
	serverVisable.value = false;
	serverFormRef.value?.submitFn();
	setTimeout(() => {
		window.location.reload();
	}, 1000);
};

const handleDeviceNumberSubmit = (e: MouseEvent) => {
	userStore.sendMessage({
		"msg_type": "device_register",
		"http_type": "req", // 请求 req 、 响应 resp
		"data": {
			"deviceNo": deviceNumber.value,
			"deviceType": "1",
			// "deviceMac": "1L:T5:8Y:6D:9U:4V"
		}
	})
	deviceVisable.value = false;
	// console.log(deviceNumber.value);
};

const okButtonProps = {
	size: 'large'
}

onMounted(async () => {
	// 清空缓存-需保留离线数据禁止全删
	// localStorage.clear()
	await localStorage.removeItem("token");
	// 初始化
	let res = await getConfigDetails();
	if (res.code == 200) {
		storage.local.set("serverUrl", res.data.netServerUrl);
		storage.local.set("reportUrl", res.data.netReportUrl);
	} else {
		console.log(res.msg);
	}
})
</script>

<style scoped lang="scss">
@import "./index.scss";

.login-tab-ico {
	cursor: pointer;
}
</style>