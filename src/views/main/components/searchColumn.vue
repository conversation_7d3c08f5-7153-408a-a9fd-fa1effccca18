<template>
	<div :style="{ ...searchAreaCss }">
		<a-flex justify="center" align="center">
			<a-row style="height: 20vh;margin-top: 10vh;">
				<a-col :span="24">
					<a-input v-model:value="value" class="search_area_input" size="large"
						placeholder="请输入住院号/门诊号/身份证后6位开始测评" />
				</a-col>
				<a-col :span="24">
					<a-button class="search_area_btn" type="primary" size="large" @click="handleSearch">查 询</a-button>
				</a-col>
			</a-row>
		</a-flex>
	</div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from "vue";
import type { CSSProperties } from 'vue';
import { useRouter } from "vue-router";
import { getInspectList } from "@/api/modules/ESsearch";
import { message } from 'ant-design-vue';
import { table } from "console";

const router = useRouter()

const searchAreaCss: CSSProperties = {
	textAlign: 'center',
	height: '50vh',
	backgroundColor: '#fff',
	borderRadius: '6px'
};

const value = ref<string>('');

const handleSearch = async () => {
	// router.push('/todoList')
	try {
		if (!value.value || value.value == '') {
			message.info('请输入查询内容');
			return;
		}
		const { data } = await getInspectList({ prefix: value.value })
		if (data.length > 0) {
			router.push({ path: '/waitList', query: { id: data[0].id, keyword: value.value } })
		} else {
			message.info('暂无查询结果');
			tableData.value = [];
			return;
		}
	} catch (e) {
		console.log(e)
	}
}
</script>

<style>
.search_area_input {
	width: 75vw;
	height: 5rem;
}

.search_area_btn {
	width: 20vw;
	height: 3rem !important;
	font-size: 1.25rem !important;
	font-weight: bold;
}
</style>