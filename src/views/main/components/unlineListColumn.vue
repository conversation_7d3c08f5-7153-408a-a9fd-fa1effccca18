<template>
    <div :style="{ ...listAreaCss }">
        <a-row :gutter="16" class="list_main_area">
            <a-col :span="6" v-for="card in props.tableData" :key="card.id" class="list_item_card">
                <a-card :title="card.patientName" hoverable @click="handleSelectCard(card)"
                    :class="{ 'selected-card': selectedCard.id === card.id, 'list-item-card': true }">
                    <template #extra>ID：{{ card.id }}</template>
                    <a-descriptions title="" :column="2">
                        <a-descriptions-item label="性别">{{ Number(card.gender) ? '男' : (Number(card.gender) == 0 ? '女'
        : '--')
                            }}</a-descriptions-item>
                        <a-descriptions-item label="年龄">{{ card.age || '--' }}</a-descriptions-item>
                        <a-descriptions-item label="科室">{{ card.deptName || '--' }}</a-descriptions-item>
                        <a-descriptions-item label="医生">{{ card.doctorName || '--' }}</a-descriptions-item>
                        <a-descriptions-item label="项目">
                            <a-tooltip placement="topLeft" :title="card.inspectListType != 1 ? card.inspectItem : '--'"
                                arrow-point-at-center>
                                <span class="ellipsis-text">{{ card.inspectListType != 1 ? card.inspectItem : '--'
                                    }}</span>
                            </a-tooltip>
                        </a-descriptions-item>
                    </a-descriptions>
                </a-card>
            </a-col>
        </a-row>
        <div class="list_footer_area">
            <a-flex justify="center" align="center">
                <a-tooltip title="上一页">
                    <a-button type="primary" shape="circle" :icon="h(LeftCircleOutlined)" size="large"
                        @click="handleGoLast" :disabled="props.initParams.pageIndex <= 1" />
                </a-tooltip>
                <a-button class="list_submit_btn" type="primary" size="large" :disabled="selectedCard.id < 0"
                    @click="handleGoTodo">确 认</a-button>
                <a-tooltip title="下一页">
                    <a-button type="primary" shape="circle" :icon="h(RightCircleOutlined)" size="large"
                        @click="handleGoNext"
                        :disabled="props.initParams.pageIndex >= Math.ceil(props.tableTotal / props.initParams.pageSize)" />
                </a-tooltip>
            </a-flex>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref, h, computed, defineProps, defineEmits, watch } from "vue";
import type { CSSProperties } from 'vue';
import { LeftCircleOutlined, RightCircleOutlined } from '@ant-design/icons-vue';
import { useRouter } from "vue-router";
import { cloneDeep } from "lodash-es"
// import { useUnlineTodoStore } from '@/stores/modules/unlineTodo.ts'
// import { getInspectListPage } from '@/api/modules/inspect';
// import { getInspectList, getInspectListById } from "@/api/modules/ESsearch";
// import { message } from 'ant-design-vue';

const router = useRouter();
const props = defineProps({
    tableData: { type: Array, default: 0 },
    tableTotal: { type: Number, default: 0 },
    initParams: { type: Object, default: () => ({ pageIndex: 1, pageSize: 8 }) },
})
const emit = defineEmits(['lastPage', 'nextPage', 'clickCard'])

const listAreaCss: CSSProperties = {
    textAlign: 'center',
    height: '70vh',
    backgroundColor: 'transparent',
    borderRadius: '6px'
};

// 卡片数据
const cards = computed(() => {
    let todoList = cloneDeep(props.tableData);
    return todoList
});

// 当前选中的列表项
const selectedCard = ref({ id: -1 });

// 选中卡片的逻辑
const handleSelectCard = (cardItem: any) => {
    if (selectedCard.value.id && selectedCard.value.id == cardItem.id) {
        selectedCard.value = { id: -1 };
    } else {
        selectedCard.value = cardItem;
    }

};

// 上一页
const handleGoLast = () => {
    emit('lastPage');
    selectedCard.value = { id: -1 };
}

// 下一页
const handleGoNext = () => {
    emit('nextPage');
    selectedCard.value = { id: -1 };
}

// 确认
const handleGoTodo = function () {
    router.push({
        path: '/hrv',
        query: {
            patient: selectedCard.value.patientName,
            todoType: selectedCard.value.inspectItemHrv,
            age: selectedCard.value.age,
            id: selectedCard.value.id,
            title: selectedCard.value.inspectItem,
            isTempTask: 0, // 是否临时任务
            isOfflineTask: 1, // 是否离线任务
            hrvDuration: selectedCard.value.hrvDuration
        }
    })
}

onMounted(() => {
    // 卡片选中赋值
    if (cards.length > 0) {
        cards.forEach((item: any) => {
            item.selected = false;
        })
    };
})

watch(() => selectedCard.value, (newValue, oldValue) => {
    if (newValue.id != oldValue.id) {
        emit('clickCard', newValue)
    }
})

</script>

<style lang="scss">
.list_main_area {

    // height: 64vh;
    .ant-card {
        .ant-card-head-title {
            text-align: left;
        }

        .ant-card-body {
            padding: 10px 5px !important;
        }
    }

}

/* 添加省略号样式 */
.ellipsis-text {
    display: inline-block;
    max-width: 150px;
    /* 根据实际需要调整 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.list_footer_area {
    width: 95%;
    position: absolute;
    bottom: 18vh;
}

.list_item_card {
    height: 27vh;
    margin-bottom: 1.5vh;
    cursor: pointer;
}

// .ant-card-hoverable:hover, .ant-card-hoverable:active{
// 	border: 1px solid #00a6b0; /* 蓝色边框 */
// 	background: #00a6b06b;
// }
.list-item-card .ant-card-head {
    padding: 0px 5px !important;
}

/* 选中卡片的样式 */
.selected-card {
    border: 1px solid #00a6b0;
    /* 蓝色边框 */
    background-color: #00a6b06b;
    /* 浅蓝色背景 */
}

.list_submit_btn {
    width: 20vw;
    height: 3rem !important;
    font-size: 1.25rem !important;
    font-weight: bold;
    margin: 0 2vw
}
</style>