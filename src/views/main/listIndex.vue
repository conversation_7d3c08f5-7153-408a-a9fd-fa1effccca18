<template>
	<div>
		<a-flex justify="space-between" align="center" class="list_main_header">
			<span>待检列表</span>
			<a-input-search v-model:value="searchText" class="list_area_input" size="large" enter-button
				placeholder="请输入住院号/门诊号/身份证后6位" @search="onSearch" />
			<a-button type="primary" danger size="large" @click="showModal" :disabled="selectedCard.id < 0">&nbsp;终
				止&nbsp;</a-button>
		</a-flex>
		<list-column class="list_main_area" v-if="tableData.length > 0" :tableData="tableData" :tableTotal="tableTotal"
			:initParams="initParams" @lastPage="handleGoLast" @nextPage="handleGoNext" @clickCard="handleSelectCard" />
		<a-empty class="list_main_area_empty" :image="simpleImage" description="暂无数据" v-else />
		<!-- <WorkColumn class="main_area" /> -->
		<a-modal v-model:open="open" title="提示" :confirm-loading="confirmLoading" @ok="handleOk" cancelText="取消"
			okText="确认">
			<p>是否确认当前待检人员: {{ selectedCard.patientName }}; 检查项目为: {{ selectedCard.inspectItem }}</p>
			<p>检查未开始，当前结束检查?</p>
		</a-modal>
	</div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import ListColumn from './components/listColumn.vue';
import { getInspectList, getInspectListById } from "@/api/modules/ESsearch";
import { deleteInspectList } from '@/api/modules/inspect';
import { useRouter, useRoute } from "vue-router";
import { Empty } from 'ant-design-vue';
import { message } from 'ant-design-vue';
// import WorkColumn from './components/workColumn.vue'

const route = useRoute(); // 获取路由对象的实例，用于获取路由参数等信息，如：route.query、route.params等。
const router = useRouter();

const searchText = ref<string>(null);
const open = ref<boolean>(false);
const confirmLoading = ref<boolean>(false);
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE

const showModal = () => {
	open.value = true;
};

const selectedCard = ref({ id: -1 });
const tableData = ref([]); // 表格数据
const tableTotal = ref<number>(0); // 表格总记录数
const initParams = ref({ pageIndex: 1, pageSize: 8 }); // 初始化参数
// const pageTotal = ref<number>(0); // 表格总记录数

const handleSelectCard = (cardItem: any) => {
	selectedCard.value = cardItem;
}

const handleOk = async () => {
	confirmLoading.value = true;
	const { code, data } = await deleteInspectList(selectedCard.value.id)
	if (code == 200) {
		message.success('操作成功');
	}
	setTimeout(() => {
		open.value = false;
		confirmLoading.value = false;
		selectedCard.value = { id: -1 };
		if (searchBtn.value) {
			onSearch();
		} else {
			getInspectData();
		}
	}, 2000);
}

const getInspectData = async (params: any) => {
	// const { data } = await getInspectListPage({ ...initParams }); // 调用接口获取表格数据的方法，根据实际情况替换为你自己的接口请求方法，返回表格数据的 Promise object
	// console.log('getInspectListPage', data);
	const { data } = await getInspectListById(params)
	const listData = data.filter((item: any) => {
		return item.inspectListType != 2; // 筛出同步任务
	});
	tableTotal.value = listData.length || 0;
	if (listData.length > initParams.value.pageSize) {
		let pageTabs = listData.length - ((initParams.value.pageIndex - 1) * initParams.value.pageSize)
		let lastIndex = pageTabs <= initParams.value.pageSize ? pageTabs : initParams.value.pageSize;
		// console.log('lastIndex', lastIndex, ((initParams.pageIndex - 1) * initParams.pageSize))
		tableData.value = listData.splice(((initParams.value.pageIndex - 1) * initParams.value.pageSize), lastIndex);
		// console.log('tableData', tableData.value, data)
	} else {
		tableData.value = listData;
	}
}

const searchBtn = ref(false);
const onSearch = async function () {
	try {
		if (!searchBtn.value) {
			searchBtn.value = true;
			tableData.value = []; // 将tableData重置为空数组
			initParams.value.pageIndex = 1; // 将pageIndex重置为1
			tableTotal.value = 0; // 将tableTotal重置为0
		};
		const { data } = await getInspectList({ prefix: searchText.value })
		if (data.length > 0) {
			await getInspectData({ id: data[0].id })
		} else {
			tableData.value = []; // 将tableData重置为空数组
			initParams.value.pageIndex = 1; // 将pageIndex重置为1
			tableTotal.value = 0; // 将tableTotal重置为0
		}
	} catch (e) {
		console.log(e)
	}
}

// 上一页
const handleGoLast = () => {
	initParams.value.pageIndex = initParams.value.pageIndex - 1;
	if (searchBtn.value) {
		onSearch();
	} else {
		getInspectData();
	}
}

// 下一页
const handleGoNext = () => {
	initParams.value.pageIndex = initParams.value.pageIndex + 1;
	if (searchBtn.value) {
		onSearch();
	} else {
		getInspectData();
	}
}

onMounted(async () => {
	const { id, keyword } = route.query; // 解构赋值，从路由参数中获取id和keyword的值。
	if (id) {
		// 当id存在时，执行搜索逻辑
		onSearch();
	} else {
		await getInspectData(); // 调用getInspectData方法获取表格数据
	}
	if (keyword) {
		searchText.value = keyword; // 将keyword的值赋给searchText.value，以便在搜索框中显示。
		searchBtn.value = true; // 将searchBtn的值设置为true，以便显示搜索按钮。
	}
})

// 监听
watch(
	() => searchText.value,
	(newValue, oldValue) => {
		if (!newValue || newValue == '') {
			searchBtn.value = false;
			tableData.value = []; // 将tableData重置为空数组
			initParams.value.pageIndex = 1; // 将pageIndex重置为1
			tableTotal.value = 0; // 将tableTotal重置为0
			getInspectData();
		} else {
			onSearch();
		}
	},
	{ immediate: true }
);
</script>

<style>
.list_area_input {
	width: 45vw;
	height: 4vh;
}

.list_main_header {
	padding: .5vh 10px 0;
	color: #000;
	height: 5vh;
	font-family: '思源黑体';
	font-size: 1rem;
	font-weight: bold;
}

.list_main_area {
	margin: 0 1.5vw;
	margin-top: 3vh;
}

.list_main_area_empty {
	margin: 0 1.5vw;
	margin-top: 15vh;

	.ant-empty-image {
		height: 80px;
	}

	.ant-empty-description {
		font-size: 1rem;
	}
}
</style>