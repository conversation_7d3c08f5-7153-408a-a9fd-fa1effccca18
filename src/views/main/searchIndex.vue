<template>
	<div>
		<a-flex justify="space-between" align="center" class="search_main_header">
			<span>待检人员</span>
			<a-button type="primary" size="large" @click="handleToListPage">待检清单</a-button>
		</a-flex>
		<SearchColumn class="search_main_area" />
		<WorkColumn class="search_main_area" />
	</div>
</template>

<script setup lang="ts">
import SearchColumn from './components/searchColumn.vue';
import WorkColumn from './components/workColumn.vue';
import { useRouter } from "vue-router";

const router = useRouter();

const handleToListPage = function () {
	router.push('/waitList')
}
</script>

<style>
.search_main_header {
	padding: .5vh 10px 0;
	color: #000;
	height: 5vh;
	font-family: '思源黑体';
	font-size: 1rem;
	font-weight: bold;
}

.search_main_area {
	margin: 0 3vw;
	margin-top: 2vh;
}
</style>