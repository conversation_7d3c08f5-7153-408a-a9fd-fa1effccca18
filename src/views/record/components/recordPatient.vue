<template>
	<a-card class="record_patient_card">
		<template #title>患者信息</template>
		<a-segmented v-model:value="value" block :options="data" size="large" class="record_patient_segmented"
			@change="changeTab" />
		<div v-if="value == '采集端录入'">
			<br />
			<a-form ref="formRef" :model="formState" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol"
				@finishFailed="onFinishFailed" @submit="onFinish">
				<a-form-item label="患者姓名" name="name" class="record_patient_form_label">
					<a-input v-model:value="formState.name" class="record_patient_form_input" />
				</a-form-item>
				<a-form-item label="年   龄" name="age" class="record_patient_form_label">
					<a-input v-model:value="formState.age" class="record_patient_form_input" />
				</a-form-item>
				<a-form-item label="性   别" name="gender">
					<a-radio-group v-model:value="formState.gender" style="float: left;">
						<a-radio value="0">女</a-radio>
						<a-radio value="1">男</a-radio>
						<!-- <a-radio value="2">未知</a-radio> -->
					</a-radio-group>
				</a-form-item>
				<a-form-item label="住院/门诊号" name="outpatientNo" class="record_patient_form_label">
					<a-input v-model:value="formState.outpatientNo" class="record_patient_form_input" />
				</a-form-item>
			</a-form>
		</div>
		<div v-else>
			<a-flex justify="space-between" align="center" style="height: 25vh;">
				<a-select v-model:value="patientIndex" size="large" style="width: 100%;" :notFoundContent="'暂无数据'"
					:options="patientOptions" :field-names="{ 'value': 'id', 'label': 'entry' }"></a-select>
			</a-flex>
		</div>
	</a-card>
</template>

<script setup lang="ts">
import { defineEmits, reactive, ref } from 'vue';
import type { UnwrapRef } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import { getPatientInfoList } from '@/api/modules/ESsearch';

const data = reactive(['采集端录入', '管理端患者']);
const value = ref(data[0]);
const emit = defineEmits(['setInfo']);

interface FormState {
	name: string;
	age: number;
	gender: number;
	outpatientNo: string;
}

const patientOptions = ref([]);
const patientIndex = ref(null);
const patientForm = ref({
	"name": "",
	"age": 0,
	"gender": "",
	"outpatientNo": "",
	"inspectListDTO": {
		"inspectItemHrv": 0,
		"hrvDuration": 0
	}
})
const formRef = ref();
const labelCol = { span: 6 };
const wrapperCol = { span: 18 };
const formState: UnwrapRef<FormState> = reactive({
	name: '',
	age: null,
	gender: null,
	outpatientNo: '',
});
const rules: Record<string, Rule[]> = {
	name: [
		{ required: true, message: '请输入患者姓名', trigger: 'blur' },
		{ min: 2, max: 5, message: '长度应2至5', trigger: 'blur' },
	],
	age: [
		{ required: true, message: '请输入患者年龄', trigger: 'blur' },
	],
	gender: [
		{ required: true, message: '请选择患者性别', trigger: 'blur' },
	],
	// outpatientNo: [
	// 	{ required: true, message: '请输入患者住院/门诊号', trigger: 'blur' },
	// ]
};

const changeTab = async (val) => {
	// console.log(val);
	if (val == '管理端患者') {
		const { data } = await getPatientInfoList()
		patientOptions.value = data;
		patientIndex.index = null;
		patientForm.value = {};
	}
}

const onFinish = async () => {
	await formRef.value?.validateFields().then(valid => {
		// 处理成功的逻辑
		console.log(valid);
		// TODO 提交
		emit('setInfo', valid); // 进入下一阶段
	}).catch(error => {
		// 处理错误的逻辑
		console.log('error submit!', error);
		// return false;
	});
}

// const onFinish = (values: any) => {
// 	console.log('Success:', values);
// 	return formState.value
// };

const onFinishFailed = (errorInfo: any) => {
	console.log('Failed:', errorInfo);
};

defineExpose({ onFinish })
</script>

<style lang="scss">
.record_patient_card {
	text-align: left;
	height: 64vh;
	background-color: #fff;
	border-radius: 6px;

	// .ant-card {
	// 	.ant-card-head{
	// 		background-color: #00a6b0!important;
	// 		color: #fff!important;
	// 	}
	// }
	.record_patient_form_label {
		line-height: 5vh;
	}

	.record_patient_form_input {
		height: 5vh;
	}

	.record_patient_segmented .ant-segmented-item-selected {
		background-color: #00A6B032;
		border: #00A6B0 1px solid;
		color: #00A6B0;
	}
}

.record_patient_card .ant-card-head {
	background-color: #00a6b0 !important;
	color: #fff !important;
}
</style>
