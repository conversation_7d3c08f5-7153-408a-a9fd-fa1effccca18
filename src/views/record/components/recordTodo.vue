<template>
	<a-card :style="{ ...recordRightAreaCss }" class="record_todo_card">
		<template #title>任务信息</template>
		<a-flex vertical justify="space-around" align="center">
			<a-radio-group v-model:value="todoValues" style="display: block; width: 100%;margin-bottom: 5px;">
				<a-list size="large" bordered :data-source="data" class="record_checkbox_list">
					<template #renderItem="{ item }">
						<a-list-item>
							<template #actions>
								<a-radio :value="item"></a-radio>
							</template>
							{{ item }}
						</a-list-item>
					</template>
				</a-list>
			</a-radio-group>
			<br />
			<a-segmented v-model:value="hrvTime" :options="timeOptions" size="large" class="record_todo_segmented"
				@change="changeTime" />
			<br />
			<a-input-number v-model:value="hrvMinuteTime" size="large" v-if="hrvTime == '自定义'">
				<template #addonAfter>分钟</template>
			</a-input-number>
		</a-flex>
	</a-card>
</template>

<script setup lang="ts">
import { ref, watch, reactive } from "vue";
import type { CSSProperties } from 'vue';

const todoValues = ref('HRV');
const timeOptions = reactive(['5分钟', '10分钟', '15分钟', '自定义']);
const hrvTime = ref(timeOptions[0]);
const hrvMinuteTime = ref(5);

const changeTime = (e: any) => {
	console.log(e);
	// hrvMinuteTime.value = 5;
	switch (e) {
		case '5分钟':
			hrvMinuteTime.value = 5;
			break;
		case '10分钟':
			hrvMinuteTime.value = 10;
			break;
		case '15分钟':
			hrvMinuteTime.value = 15;
			break;
		default:
			hrvMinuteTime.value = 5;
			break;
	}
}

const recordRightAreaCss: CSSProperties = {
	textAlign: 'center',
	height: '64vh',
	backgroundColor: '#fff',
	borderRadius: '6px',
};

const data: string[] = [
	'HRV',
	'SCWT',
	'MAT',
];

const onSubmit = () => {
	let hrvFunc = (hrvName: String) => {
		switch (hrvName) {
			case 'HRV':
				return 0;
			case 'SCWT':
				return 1;
			case 'MAT':
				return 2;
			default:
				return 0;
		}
	}
	return {
		"inspectItemHrv": hrvFunc(todoValues.value),
		"hrvDuration": hrvMinuteTime.value,
	}
}

defineExpose({ onSubmit })
</script>

<style>
.record_todo_card .ant-card-head {
	background-color: #00a6b0 !important;
	color: #fff !important;
}

.record_checkbox_list {
	width: 100%;
	height: 25vh;
	display: block;
	overflow: auto;
}

.record_todo_segmented .ant-segmented-item-selected {
	background-color: #00A6B032;
	border: #00A6B0 1px solid;
	color: #00A6B0;
}
</style>