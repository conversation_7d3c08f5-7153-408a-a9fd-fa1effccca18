<template>
	<div>
		<a-flex justify="space-between" align="center" class="record_main_header">
			<span>临时登记</span>
			<a-button type="primary" size="large" @click="handleToListPage">返回</a-button>
		</a-flex>
		<a-row>
			<a-col :span="12">
				<RecordPatient ref="patientRef" class="todo_main_area_l" @setInfo="setPatientInfo" />
			</a-col>
			<a-col :span="12">
				<RecordTodo ref="todoRef" class="todo_main_area_r" />
			</a-col>
			<a-col :span="24">
				<a-row>
					<a-col :span="12">
						<a-flex justify="flex-end" align="center" class="record_main_footer">
							<a-button type="primary" size="large" @click="handleGoHrv">确 认</a-button>
						</a-flex>
					</a-col>
					<a-col :span="12">
						<a-flex justify="flex-start" align="center" class="record_main_footer">
							<a-button type="default" size="large">取 消</a-button>
						</a-flex>
					</a-col>
				</a-row>
				<!-- <a-flex justify="space-around" align="center" class="record_main_footer">
					<a-button type="primary" size="large">确 认</a-button>
					<a-button type="default" size="large">取 消</a-button>
				</a-flex> -->
			</a-col>
		</a-row>
	</div>
</template>

<script setup lang="ts" name="record">
import { ref, reactive } from "vue";
import RecordPatient from "./components/recordPatient.vue";
import RecordTodo from "./components/recordTodo.vue";
import { useRouter } from "vue-router";
import { tempAdd } from "@/api/modules/unline";

const router = useRouter();
const patientRef = ref();
const todoRef = ref();
const patientForm = ref({
	"name": "",
	"age": 0,
	"gender": "",
	"outpatientNo": "",
	"inspectListDTO": {
		"inspectItemHrv": 0,
		"hrvDuration": 0
	}
})

const handleToListPage = function () {
	router.go(-1);
}

const patientInfo = ref(null)
const setPatientInfo = function (data) {
	patientInfo.value = data;
}

const handleGoHrv = async function () {
	// router.push('/unlineTodo');
	await patientRef.value?.onFinish();
	let dataB = await todoRef.value?.onSubmit();
	if (!patientInfo.value || !dataB) {
		return false;
	} else {
		patientForm.value = { ...patientInfo.value, ...{ 'inspectListDTO': dataB } }
		// console.log(patientForm.value);
		await tempAdd(patientForm.value).then(res => {
			if (res.code == 200) {
				router.push({
					path: '/hrv',
					query: {
						patient: patientForm.value.name,
						age: patientForm.value.age,
						todoType: patientForm.value.inspectListDTO.inspectItemHrv,
						id: res.data.id,
						title: patientForm.value.inspectListDTO.inspectItemHrv == 0 ? 'HRV' : patientForm.value.inspectListDTO.inspectItemHrv == 1 ? 'SCWT' : 'MAT',
						isTempTask: 1, // 是否临时任务
						isOfflineTask: 0, // 是否离线任务
						hrvDuration: patientForm.value.inspectListDTO.hrvDuration
					}
				})
			}
		})
	}
}
</script>

<style lang="scss">
.record_main_header {
	padding: .5vh 10px 0;
	color: #000;
	height: 5vh;
	font-family: '思源黑体';
	font-size: 1rem;
	font-weight: bold;
}

.todo_main_area_l {
	margin: 1vh 1.5vw 2vh 3vw;
}

.todo_main_area_r {
	margin: 1vh 3vw 2vh 1.5vw;
}

.record_main_footer {
	margin-right: 1.5vw;
	margin-left: 1.5vw;

	.ant-btn {
		width: 20vw;

	}
}
</style>