<template>
	<div :style="{ ...todoAreaCss}">
		<a-list size="large" :data-source="data" :split="false" class="todo_list">
			<template #renderItem="{ item }">
				<a-list-item 
					:style="{
						borderRadius: '5px',
						margin: '.5vh',
						cursor: 'pointer',
						border: selectedItem.id === item.id ? '1px solid #00a6b0' : 'none',
						backgroundColor: selectedItem.id === item.id ? '#00a6b042' : '#fff'
					}" 
					@click="handleItemClick(item)"
				>
					<template #actions>
						<!-- <a key="list-loadmore-edit">edit</a> -->
						<a-tag color="cyan">{{todoFilter(item.type)}}</a-tag>
						<a key="list-loadmore-more" style="color: #00A6B0">{{item.count}}次</a>
					</template>
					<a-list-item-meta>
						<template #title>
							<a-flex justify="flex-start" align="center" class="list_title_div">{{ item.title }}</a-flex>
						</template>
						<template #avatar>
							<div class="list_avatar_div">
								<a-image :width="'4vh'" :height="'4vh'" :src="item.imgSrc" :preview="false"
									fallback="data:image/png;base64,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" />
							</div>
						</template>
					</a-list-item-meta>
				</a-list-item>
			</template>
		</a-list>
		<a-flex justify="center" align="center">
			<a-button class="list_submit_btn" type="primary" size="large" :disabled="selectedItem.id < 0" @click="handleGoTodo">开 始 采 集</a-button>
		</a-flex>
	</div>
</template>

<script setup lang="ts">
	import { onMounted, onUnmounted, ref, watch } from "vue";
	import type { CSSProperties } from 'vue';
	import { useRouter } from "vue-router";
	import zpIcon from '../../../assets/images/todo_btn/zp.png';
	import tpIcon from '../../../assets/images/todo_btn/tp.png';
	import hrvIcon from '../../../assets/images/todo_btn/hrv.png';
	import scwtIcon from '../../../assets/images/todo_btn/scwt.png';
	import matIcon from '../../../assets/images/todo_btn/mat.png';

	const router = useRouter()

	const todoAreaCss : CSSProperties = {
		textAlign: 'center',
		height: '70vh',
		// backgroundColor: '#fff',
		borderRadius: '6px',
	};

	const todoFilter = (id : Number) => {
		let options = {
			1: '自评项目',
			2: '他评项目',
			3: 'HRV项目',
			4: 'SCWT项目',
			5: 'MAT项目'
		}

		return options[id] || '--'
	}

	// 当前选中的列表项
	const selectedItem = ref({id: -1});

	// 点击选中的列表项
	const handleItemClick = (item : any) => {
		console.log('点击行->', item);
		selectedItem.value = item;
	}

	interface DataItem {
		id : number
		title : string;
		type : number;
		imgSrc : string;
		count : number
	}
	const data : DataItem[] = [
		{ id: 1, title: 'Beck焦虑自评量表(BAI)', type: 1, imgSrc: zpIcon, count: 2 },
		{ id: 2, title: 'Wilson病改良Young量表', type: 1, imgSrc: zpIcon, count: 1 },
		{ id: 3, title: 'QMGS重症肌无力定量评分', type: 2, imgSrc: tpIcon, count: 2 },
		{ id: 4, title: 'MG临床绝对评分记录法', type: 1, imgSrc: zpIcon, count: 4 },
		{ id: 5, title: 'HRV', type: 3, imgSrc: hrvIcon, count: 1 },
		{ id: 6, title: 'Stroop+HRV', type: 4, imgSrc: scwtIcon, count: 2 },
		{ id: 7, title: '心算+HRV', type: 5, imgSrc: matIcon, count: 1 },
		{ id: 8, title: 'Beck焦虑自评量表(BAI)', type: 1, imgSrc: zpIcon, count: 3 },
		{ id: 9, title: 'Wilson病改良Young量表', type: 1, imgSrc: zpIcon, count: 5 },
		{ id: 10, title: 'QMGS重症肌无力定量评分', type: 2, imgSrc: tpIcon, count: 2 },
		{ id: 11, title: 'MG临床绝对评分记录法', type: 1, imgSrc: zpIcon, count: 2 },
		{ id: 12, title: 'HRV', type: 3, imgSrc: hrvIcon, count: 4 },
		{ id: 13, title: 'Stroop+HRV', type: 4, imgSrc: scwtIcon, count: 3 },
		{ id: 14, title: '心算+HRV', type: 5, imgSrc: matIcon, count: 2 },
	];
</script>

<style lang="scss">
	.todo_list {
		height: 63vh;
		overflow: auto;
	}

	.list_avatar_div {
		width: 5vh;
		height: 5vh;
		background-color: #00A6B0;
		border-radius: 6px;
		padding: .5vh;
	}

	.list_title_div {
		height: 5vh;
	}

	.list_submit_btn {
		width: 20vw;
		height: 3rem !important;
		font-size: 1.25rem !important;
		font-weight: bold;
		margin: 1.2vh 2vw
	}
</style>