<template>
	<div>
		<a-flex justify="space-between" align="center" class="todo_main_header">
			<h1>患者HY【任务列表】</h1>
			<a-button type="primary" size="large" @click="handleToListPage">返回</a-button>
		</a-flex>
		<TodoList class="todo_main_area" />
	</div>
</template>

<script setup lang="ts">
	import TodoList from './components/todoList.vue';
	import { useRouter } from "vue-router";
	
	const router = useRouter();
	
	const handleToListPage = function() {
		router.go(-1)
	}
</script>

<style>
	.todo_main_header{
		padding: .5vh 10px 0;
		color: #000;
		height: 5vh;
	}
	.todo_main_area{
		margin: 0 3vw;
		margin-top: 2vh;
	}
</style>