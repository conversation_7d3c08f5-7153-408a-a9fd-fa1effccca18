// pixel * dpr
const CELL_H = 16; // 8 pixel per cell
const CELL_W = 16;

// amplitude/1mV 振幅/mV
const AMPL = 800;
// benchmark 基准
const BEN = 2048;
const FREQ = 500;

class CanvasLineChart {
  private _canvas: HTMLCanvasElement;
  private _context: CanvasRenderingContext2D;

  constructor(canvas: HTMLCanvasElement) {
    this._canvas = canvas;
    this._context = canvas.getContext('2d');

    /**
     * 初始化画布大小
     */
    // https://juejin.cn/post/7042293002651303944
    // https://segmentfault.com/a/1190000020189168
    // canvas 无损、高清适配
    const dpr = window.devicePixelRatio;
    const gridRect = this._canvas.getBoundingClientRect();
    // console.log(gridRect);
    this._canvas.width = gridRect.width * dpr;
    this._canvas.height = gridRect.height * dpr;
  }

  public clear() {
    this._context.clearRect(0, 0, this._canvas.width, this._canvas.height);
  }

  public maxLen() {
    // maxW / (1 / FREQ) * (CELL_W / 0.04)
    // => maxW / (CELL_W / (0.04 * FREQ))
    return Math.floor(this._canvas.width / (CELL_W / (0.04 * FREQ)));
  }

  public drawGrid() {
    this._context.strokeStyle = '#cccccc';
    this._context.lineWidth = 1;
    // const gridSize = 150 / 50;
    // console.log(this._canvas);

    // 绘制水平线条 (从上到下 y+, 从下到上, 则y-)
    for (let y = this._canvas.height; y > 0; y -= CELL_H) {
      this._context.beginPath();
      this._context.moveTo(0, y);
      this._context.lineTo(this._canvas.width, y);
      this._context.stroke();
    }

    // 绘制垂直线条 (从左到右 x+)
    for (let x = CELL_W; x < this._canvas.width; x += CELL_W) {
      this._context.beginPath();
      this._context.moveTo(x, 0);
      this._context.lineTo(x, this._canvas.height);
      this._context.stroke();
    }
  }

  private calcPosition(i: number, d: number): [number, number] {
    // 1) w : 0.04s = x_ : (1 / FREQ)  => x_ = w / (0.04 * FREQ)
    // 2) h : 0.1mV = y_ : (1mV / AMPL) => y_ = (10 * h) / AMPL
    // 3) x = 1 * x_
    // 4) y = midH + (d - BEN) * y_
    const midH = Math.floor(this._canvas.height / 2);
    const y_ = (10 * CELL_H) / AMPL;
    return [(i * CELL_W) / (0.04 * FREQ), midH + (d - BEN) * y_];
  }

  public drawLine(dataQueue: Iterable<number>) {
    this._context.beginPath();
    this._context.strokeStyle = 'green';

    let i = 0;
    for (const d of dataQueue) {
      const p = this.calcPosition(i, d);
      if (i++ === 0) {
        this._context.moveTo(p[0], p[1]);
        continue;
      }
      this._context.lineTo(p[0], p[1]);
    }
    this._context.stroke();
  }
}

type UseCanvasLineChartProps = {
  canvas: HTMLCanvasElement;
};

const useCanvasLineChart = ({ canvas }: UseCanvasLineChartProps) => {
  const lineChart = new CanvasLineChart(canvas);
  return lineChart;
};

export default CanvasLineChart;
export { useCanvasLineChart };
