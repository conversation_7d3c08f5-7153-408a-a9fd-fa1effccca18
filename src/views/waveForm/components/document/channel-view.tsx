import { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import CanvasLine from './canvas-line';
import { ReactionCapacityQueue } from '@/ui/utils/dsa';

interface ChannelViewProps extends React.PropsWithChildren {}

interface ChannelViewRef {
  startAnimation: () => void;
  stopAnimation: () => void;
  pushData: (d: number) => void;
  resizeView: () => void;
}

const ChannelView: React.ForwardRefRenderFunction<
  ChannelViewRef,
  ChannelViewProps
> = (_: ChannelViewProps, ref: React.ForwardedRef<ChannelViewRef>) => {
  console.log('renderer ChannelView');
  const viewRef = useRef<HTMLCanvasElement>(null);
  const datas = useRef<number[]>([]);
  const dataQueue = useRef<ReactionCapacityQueue<number>>();
  const animationSwitch = useRef(false);
  const rafId = useRef<number>(null);
  const canvasLine = useRef<CanvasLine>(null);

  const startAnimation = () => {
    animationSwitch.current = true;
    rafId.current = requestAnimationFrame(() => {
      draw();
    });
  };

  const stopAnimation = () => {
    console.log('stopAnimation');
    animationSwitch.current = false;
    rafId.current != null && cancelAnimationFrame(rafId.current);
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const pushDataInArr = (d: number) => {
    const xLen = canvasLine.current.maxLen();
    if (datas.current.length > xLen) {
      // console.log(datas.current.length - xLen);
      datas.current.splice(0, datas.current.length - xLen);
    }
    datas.current.push(d);
    // console.log(datas);
  };

  const initDataQueue = () => {
    const xLen = canvasLine.current.maxLen();
    if (dataQueue.current == null) {
      dataQueue.current = new ReactionCapacityQueue(xLen);
      console.log('new ReactionCapacityQueue: ' + xLen);
    } else {
      dataQueue.current.setCapcity(xLen);
      console.log('setCapcity: ' + xLen);
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const pushDataInQueue = (d: number) => {
    if (dataQueue.current == null) {
      console.log('dataQueue is null');
      return;
    }
    dataQueue.current.enqueue(d, true);
  };

  const pushData = (d: number) => {
    // pushDataInArr(d);
    pushDataInQueue(d);
  };

  const resizeView = () => {
    const canvasLine = new CanvasLine(viewRef.current);
    canvasLine.clear();
    canvasLine.drawGrid();
  };

  const draw = () => {
    const canvasLine = new CanvasLine(viewRef.current);
    canvasLine.clear();
    canvasLine.drawGrid();
    // console.log('draw');
    canvasLine.drawLine(dataQueue.current);
    // canvasLine.drawLine(datas.current);
    requestAnimationFrame(() => {
      draw();
    });
  };

  useEffect(() => {
    canvasLine.current = new CanvasLine(viewRef.current);
    canvasLine.current.drawGrid();

    initDataQueue();

    return () => {
      rafId.current != null && cancelAnimationFrame(rafId.current);
    };
  }, []);

  useImperativeHandle(ref, () => ({
    startAnimation,
    stopAnimation,
    pushData,
    resizeView,
  }));

  return (
    <div
      className="flex-grow-1 flex-shrink-0 border my-2"
      style={{ height: 150 }}
    >
      <canvas ref={viewRef} className="w-100 h-100"></canvas>
    </div>
  );
};

export { ChannelViewRef };
export default forwardRef(ChannelView);
