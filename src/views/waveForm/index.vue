<template>
    <!-- <a-card :style="{ ...recordRightAreaCss }" class="hrv_card_header" title="心电波形图"> -->
    <div ref="containerRef"></div>
    <!-- </a-card> -->
</template>
<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { WaveformDrawer } from './method/waveform'
import type { CSSProperties } from 'vue';
import { useSSEStore } from '@/stores/modules/sseRequest';
import $bus from "@/utils/bus.ts";

const recordRightAreaCss: CSSProperties = {
    textAlign: 'center',
    width: '90vw',
    height: '66vh',
    margin: '5vh 5vw 0',
    backgroundColor: '#fff',
    borderRadius: '6px',
};

const props = defineProps({
    height: {
        type: Number,
        default: 360,
    },
})
const timer = ref<NodeJS.Timeout>()  // 修改为明确的类型定义

let drawer: WaveformDrawer | null = null
const containerRef = ref<HTMLDivElement | null>(null)

function push(waveform: any) {
    if (!drawer) return
    // drawer.pushPoint(waveform.data.raw_value)
    if (waveform) drawer.pushPoint(drawer.sseEcg(Number(waveform.ecgChannelI)));
}

onMounted(async () => {
    await nextTick()
    if (containerRef.value) {
        drawer = new WaveformDrawer(containerRef.value, props.height)
        push();
    }
    // return false;
    // 连接SSE
    await useSSEStore().setWaveform();
    // 监听SSE事件
    $bus.on('waveFormSSE', (data: any) => {
        push(data);
    })
    // 监听波形冻结事件
    $bus.on('waveFormFrozen', (data: any) => {
        useSSEStore().disconnectSSE('waveformSource')
    })
    $bus.on('waveFormRestart', async (data: any) => {
        console.log('波形重新连接')
        await useSSEStore().setWaveform();
        // 监听SSE事件
        $bus.on('waveFormSSE', (data: any) => {
            push(data);
        })
    })
})

onBeforeUnmount(() => {
    // if (timer.value) {
    //     clearInterval(timer.value)  // 清除定时器
    // }
    if (drawer) drawer.destroy()

    // 断开连接
    useSSEStore().disconnectSSE('waveformSource')
})

defineExpose({
    push,
})
</script>

<style scoped>
canvas {
    background: #fff;
    border-radius: 6px;
    display: block;
    width: 100%;
    height: 100px;
}

.hrv_card_header .ant-card-head {
    background-color: #00a6b0 !important;
    color: #fff !important;
}
</style>