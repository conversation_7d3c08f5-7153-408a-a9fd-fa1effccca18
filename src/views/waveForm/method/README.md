# 波形渲染优化 - OffscreenCanvas

## 概述

本项目使用 OffscreenCanvas 和 Web Worker 来优化波形渲染性能，将计算密集型的渲染任务从主线程转移到 Worker 线程，避免阻塞 UI。

## 主要特性

### 1. OffscreenCanvas 支持
- 在 Worker 线程中进行 Canvas 渲染
- 使用 `transferToImageBitmap()` 避免数据拷贝
- 自动回退到主线程渲染（当浏览器不支持时）

### 2. Web Worker 优化
- 波形数据处理
- 网格计算
- 统计数据计算
- 批量数据处理

### 3. 性能优化
- 减少主线程阻塞
- 提高渲染帧率
- 内存使用优化
- 自动错误处理和回退

## 技术实现

### Worker 线程渲染流程
1. 主线程创建 OffscreenCanvas
2. 将 OffscreenCanvas 传输到 Worker
3. Worker 在离屏画布上绘制波形
4. 使用 `transferToImageBitmap()` 返回结果
5. 主线程将 ImageBitmap 绘制到显示画布

### 回退机制
- 检测浏览器 OffscreenCanvas 支持
- 自动切换到主线程渲染
- 错误处理和恢复

## 使用方法

### 基本使用

```typescript
import { WaveformDrawer } from './waveform';

// 创建波形绘制器
const drawer = new WaveformDrawer(container, height);

// 添加数据点
drawer.pushPoint(value);

// 获取性能信息
const metrics = drawer.getPerformanceMetrics();
console.log('FPS:', metrics.fps);

// 清理资源
drawer.destroy();
```

### 高级使用（带性能监控）

```typescript
import { WaveformExample } from './usage-example';

// 创建示例实例
const example = new WaveformExample('waveform-container');
example.init(300);
example.addControls();

// 开始模拟数据
example.startSimulation();

// 获取性能报告
const report = example.getDrawer()?.getPerformanceReport();
console.log(report);
```

### 性能监控 API

```typescript
// 获取性能指标
const metrics = drawer.getPerformanceMetrics();

// 获取性能报告
const report = drawer.getPerformanceReport();

// 获取性能建议
const advice = drawer.getPerformanceAdvice();

// 检查性能是否良好
const isGood = drawer.isPerformanceGood();

// 启用/禁用性能监控
drawer.enablePerformanceMonitoring(true);

// 检查 OffscreenCanvas 支持
const supported = drawer.isOffscreenCanvasSupported();

// 获取当前渲染模式
const mode = drawer.getRenderMode();
```

## 浏览器兼容性

- Chrome 69+
- Firefox 105+
- Safari 16.4+
- Edge 79+

## 性能对比

| 渲染方式 | 主线程阻塞 | 帧率 | 内存使用 |
|---------|-----------|------|----------|
| 传统渲染 | 高 | 30-45fps | 中等 |
| OffscreenCanvas | 低 | 55-60fps | 优化 |

## 注意事项

1. 确保在支持的浏览器中使用
2. 及时清理 ImageBitmap 资源
3. 监控 Worker 错误和回退情况
4. 根据设备性能调整批处理大小 