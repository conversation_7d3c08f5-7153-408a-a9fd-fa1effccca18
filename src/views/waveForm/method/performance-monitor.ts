// 性能监控工具 - 用于跟踪 OffscreenCanvas 渲染性能

export interface PerformanceMetrics {
    fps: number;
    renderTime: number;
    memoryUsage?: number;
    workerTime?: number;
    mainThreadTime?: number;
}

export class PerformanceMonitor {
    private frameCount: number = 0;
    private lastTime: number = performance.now();
    private fps: number = 0;
    private renderTimes: number[] = [];
    private maxSamples: number = 60; // 保存最近60帧的数据

    // 开始监控
    public startMonitoring(): void {
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.renderTimes = [];
    }

    // 记录一帧的渲染时间
    public recordFrame(renderTime: number): void {
        this.frameCount++;
        this.renderTimes.push(renderTime);

        // 保持最近60帧的数据
        if (this.renderTimes.length > this.maxSamples) {
            this.renderTimes.shift();
        }

        // 计算FPS
        const currentTime = performance.now();
        if (currentTime - this.lastTime >= 1000) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.lastTime = currentTime;
        }
    }

    // 获取性能指标
    public getMetrics(): PerformanceMetrics {
        const avgRenderTime = this.renderTimes.length > 0 
            ? this.renderTimes.reduce((a, b) => a + b, 0) / this.renderTimes.length 
            : 0;

        return {
            fps: this.fps,
            renderTime: avgRenderTime,
            memoryUsage: this.getMemoryUsage()
        };
    }

    // 获取内存使用情况（如果支持）
    private getMemoryUsage(): number | undefined {
        if ('memory' in performance) {
            return (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
        }
        return undefined;
    }

    // 重置监控数据
    public reset(): void {
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.renderTimes = [];
        this.fps = 0;
    }

    // 生成性能报告
    public generateReport(): string {
        const metrics = this.getMetrics();
        return `
性能监控报告:
- FPS: ${metrics.fps}
- 平均渲染时间: ${metrics.renderTime.toFixed(2)}ms
- 内存使用: ${metrics.memoryUsage ? `${metrics.memoryUsage.toFixed(2)}MB` : '不支持'}
- 渲染模式: ${this.getRenderMode()}
        `.trim();
    }

    // 获取渲染模式
    private getRenderMode(): string {
        if (typeof OffscreenCanvas !== 'undefined') {
            return 'OffscreenCanvas + Worker';
        }
        return '主线程渲染';
    }

    // 检查性能是否达标
    public isPerformanceGood(): boolean {
        const metrics = this.getMetrics();
        return metrics.fps >= 50 && metrics.renderTime < 16; // 60fps = 16.67ms per frame
    }

    // 获取性能建议
    public getPerformanceAdvice(): string[] {
        const advice: string[] = [];
        const metrics = this.getMetrics();

        if (metrics.fps < 30) {
            advice.push('FPS过低，建议检查数据量或使用OffscreenCanvas');
        }

        if (metrics.renderTime > 33) {
            advice.push('渲染时间过长，建议优化渲染逻辑');
        }

        if (metrics.memoryUsage && metrics.memoryUsage > 100) {
            advice.push('内存使用过高，建议检查内存泄漏');
        }

        if (advice.length === 0) {
            advice.push('性能表现良好');
        }

        return advice;
    }
}

// 单例实例
export const performanceMonitor = new PerformanceMonitor(); 