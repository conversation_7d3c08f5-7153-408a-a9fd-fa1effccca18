// 波形绘制器使用示例 - 展示 OffscreenCanvas 优化

import { WaveformDrawer } from './waveform';

export class WaveformExample {
    private drawer: WaveformDrawer | null = null;
    private container: HTMLDivElement;
    private isRunning: boolean = false;
    private dataInterval: number | null = null;

    constructor(containerId: string) {
        this.container = document.getElementById(containerId) as HTMLDivElement;
        if (!this.container) {
            throw new Error(`Container with id "${containerId}" not found`);
        }
    }

    // 初始化波形绘制器
    public init(height: number = 300) {
        // 创建容器
        const canvasContainer = document.createElement('div');
        canvasContainer.style.width = '100%';
        canvasContainer.style.height = `${height}px`;
        canvasContainer.style.border = '1px solid #ccc';
        canvasContainer.style.position = 'relative';
        
        this.container.appendChild(canvasContainer);

        // 创建波形绘制器
        this.drawer = new WaveformDrawer(canvasContainer, height);

        // 显示初始化信息
        this.showInfo();
    }

    // 开始模拟数据
    public startSimulation() {
        if (!this.drawer || this.isRunning) return;

        this.isRunning = true;
        let time = 0;

        this.dataInterval = window.setInterval(() => {
            if (!this.drawer) return;

            // 生成模拟心电数据
            const value = this.generateEcgData(time);
            this.drawer.pushPoint(value);
            time += 0.02; // 20ms 间隔

            // 每5秒显示一次性能信息
            if (Math.floor(time * 50) % 250 === 0) {
                this.showPerformanceInfo();
            }
        }, 20); // 50fps
    }

    // 停止模拟
    public stopSimulation() {
        if (this.dataInterval) {
            clearInterval(this.dataInterval);
            this.dataInterval = null;
        }
        this.isRunning = false;
    }

    // 生成模拟心电数据
    private generateEcgData(time: number): number {
        // 模拟心电波形：P波、QRS复合波、T波
        const frequency = 1.2; // 心跳频率 (Hz)
        const phase = time * frequency * 2 * Math.PI;
        
        // 基础信号
        let signal = Math.sin(phase) * 0.3;
        
        // 添加QRS复合波
        const qrsPhase = phase % (2 * Math.PI);
        if (qrsPhase > 0 && qrsPhase < 0.5) {
            signal += Math.sin(qrsPhase * 20) * 2.0;
        }
        
        // 添加噪声
        signal += (Math.random() - 0.5) * 0.1;
        
        // 转换为原始数据格式
        return Math.floor((signal + 2) * 1000) + 1000;
    }

    // 显示初始化信息
    private showInfo() {
        if (!this.drawer) return;

        const info = document.createElement('div');
        info.style.cssText = `
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
        `;

        info.innerHTML = `
            <div><strong>波形绘制器状态</strong></div>
            <div>渲染模式: ${this.drawer.getRenderMode()}</div>
            <div>OffscreenCanvas: ${this.drawer.isOffscreenCanvasSupported() ? '支持' : '不支持'}</div>
            <div>性能监控: ${this.drawer.getPerformanceMetrics().fps} FPS</div>
        `;

        this.container.appendChild(info);
    }

    // 显示性能信息
    private showPerformanceInfo() {
        if (!this.drawer) return;

        const metrics = this.drawer.getPerformanceMetrics();
        const advice = this.drawer.getPerformanceAdvice();

        console.log('=== 性能监控报告 ===');
        console.log(this.drawer.getPerformanceReport());
        console.log('性能建议:', advice);

        // 如果性能不佳，显示警告
        if (!this.drawer.isPerformanceGood()) {
            console.warn('⚠️ 性能表现不佳，建议检查渲染设置');
        }
    }

    // 清理资源
    public destroy() {
        this.stopSimulation();
        if (this.drawer) {
            this.drawer.destroy();
            this.drawer = null;
        }
    }

    // 获取绘制器实例（用于外部控制）
    public getDrawer(): WaveformDrawer | null {
        return this.drawer;
    }

    // 添加控制按钮
    public addControls() {
        const controls = document.createElement('div');
        controls.style.cssText = `
            margin-top: 10px;
            text-align: center;
        `;

        const startBtn = document.createElement('button');
        startBtn.textContent = '开始模拟';
        startBtn.onclick = () => this.startSimulation();

        const stopBtn = document.createElement('button');
        stopBtn.textContent = '停止模拟';
        stopBtn.onclick = () => this.stopSimulation();

        const clearBtn = document.createElement('button');
        clearBtn.textContent = '清除数据';
        clearBtn.onclick = () => this.drawer?.clear();

        const perfBtn = document.createElement('button');
        perfBtn.textContent = '性能报告';
        perfBtn.onclick = () => this.showPerformanceInfo();

        controls.appendChild(startBtn);
        controls.appendChild(stopBtn);
        controls.appendChild(clearBtn);
        controls.appendChild(perfBtn);

        this.container.appendChild(controls);
    }
}

// 使用示例
/*
// 在 Vue 组件中使用
export default {
    mounted() {
        this.waveformExample = new WaveformExample('waveform-container');
        this.waveformExample.init(300);
        this.waveformExample.addControls();
    },
    
    beforeDestroy() {
        if (this.waveformExample) {
            this.waveformExample.destroy();
        }
    }
}
*/ 