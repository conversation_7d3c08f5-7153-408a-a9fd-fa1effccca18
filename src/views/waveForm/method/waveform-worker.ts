// 波形数据处理 Web Worker with OffscreenCanvas
// 处理复杂的波形计算和渲染任务，避免阻塞主线程

// 心电相关常量
const AMPL = 800; // amplitude/1mV 振幅/mV
const BEN = 2048; // benchmark 基准

// 心电数据处理函数
function sseEcg(val: number): number {
    let amplitude = ((val > 4096 ? 4096 : (val < 0 ? 0 : val)) - BEN) / AMPL;
    return amplitude * 50;
}

// 坐标转换函数
function mapValueToY(value: number, height: number): number {
    return height / 2 - value;
}

// 计算网格线
function calculateGridLines(maxPoints: number, height: number, gridSize: number, padding: { x: number; y: number }): Array<{ points: number[]; color: string }> {
    const gridLines: Array<{ points: number[]; color: string }> = [];
    const baseGridSize = gridSize * 1.5;
    
    // 垂直网格线
    for (let x = 0; x <= maxPoints; x += baseGridSize) {
        gridLines.push({
            points: [x, 0, x, height],
            color: '#eee'
        });
    }
    
    // 水平网格线
    for (let y = 0; y <= height; y += baseGridSize) {
        gridLines.push({
            points: [0, y, maxPoints, y],
            color: '#eee'
        });
    }
    
    // 主要网格线（更粗）
    for (let x = 0; x <= maxPoints; x += baseGridSize * 5) {
        gridLines.push({
            points: [x, 0, x, height],
            color: '#ddd'
        });
    }
    
    for (let y = 0; y <= height; y += baseGridSize * 5) {
        gridLines.push({
            points: [0, y, maxPoints, y],
            color: '#eee'
        });
    }
    
    return gridLines;
}

// 计算统计数据
function calculateStatistics(waveData: number[]): { maxValue: number; minValue: number; average: number; variance: number } {
    if (waveData.length === 0) {
        return { maxValue: 0, minValue: 0, average: 0, variance: 0 };
    }
    
    const maxValue = Math.max(...waveData);
    const minValue = Math.min(...waveData);
    const sum = waveData.reduce((acc, val) => acc + val, 0);
    const average = sum / waveData.length;
    
    const variance = waveData.reduce((acc, val) => acc + Math.pow(val - average, 2), 0) / waveData.length;
    
    return { maxValue, minValue, average, variance };
}

// 批量处理数据
function processBatchData(waveData: number[], batchSize: number): number[] {
    const processedData: number[] = [];
    
    for (let i = 0; i < waveData.length; i += batchSize) {
        const batch = waveData.slice(i, i + batchSize);
        const processedBatch = batch.map(val => sseEcg(val));
        processedData.push(...processedBatch);
    }
    
    return processedData;
}

// 使用 OffscreenCanvas 绘制波形
function drawWaveformOnOffscreenCanvas(
    offscreenCanvas: OffscreenCanvas,
    waveData: number[],
    width: number,
    height: number,
    gridSize: number = 1
): ImageBitmap {
    const ctx = offscreenCanvas.getContext('2d')!;
    
    // 清除画布
    ctx.clearRect(0, 0, width, height);
    
    // 绘制网格
    drawGridOnCanvas(ctx, width, height, gridSize);
    
    // 绘制波形
    if (waveData.length > 0) {
        ctx.beginPath();
        ctx.strokeStyle = '#e74c3c';
        ctx.lineWidth = 2;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';

        for (let i = 0; i < waveData.length; i++) {
            const y = mapValueToY(waveData[i], height);
            const x = i * gridSize;
            
            if (i === 0) {
                ctx.moveTo(x, y - 0.1);
            } else {
                ctx.lineTo(x, y - 0.1);
            }
        }
        ctx.stroke();
    }
    
    // 绘制坐标轴
    drawAxesOnCanvas(ctx, width, height);
    
    // 绘制标签
    drawLabelsOnCanvas(ctx, height);
    
    // 返回 ImageBitmap
    return offscreenCanvas.transferToImageBitmap();
}

// 在 Canvas 上绘制网格
function drawGridOnCanvas(ctx: OffscreenCanvasRenderingContext2D, width: number, height: number, gridSize: number) {
    const baseGridSize = gridSize * 1.5;
    
    ctx.strokeStyle = '#eee';
    ctx.lineWidth = 1;
    
    // 垂直网格线
    for (let x = 0; x <= width; x += baseGridSize) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
    }
    
    // 水平网格线
    for (let y = 0; y <= height; y += baseGridSize) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
    }
    
    // 主要网格线
    ctx.strokeStyle = '#ddd';
    for (let x = 0; x <= width; x += baseGridSize * 5) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
    }
}

// 在 Canvas 上绘制坐标轴
function drawAxesOnCanvas(ctx: OffscreenCanvasRenderingContext2D, width: number, height: number) {
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 2;
    
    // X轴
    ctx.beginPath();
    ctx.moveTo(0, height / 2);
    ctx.lineTo(width, height / 2);
    ctx.stroke();
    
    // Y轴
    ctx.beginPath();
    ctx.moveTo(0, 0);
    ctx.lineTo(0, height);
    ctx.stroke();
}

// 在 Canvas 上绘制标签
function drawLabelsOnCanvas(ctx: OffscreenCanvasRenderingContext2D, height: number) {
    ctx.fillStyle = '#000';
    ctx.font = '12px Arial';
    ctx.textAlign = 'left';
    
    // Y轴标签
    ctx.fillText('2 mV', 5, height / 14 * 1);
    ctx.fillText('1 mV', 5, height / 14 * 5);
    ctx.fillText('-1 mV', 5, height / 14 * 9.5);
    ctx.fillText('-2 mV', 5, height / 14 * 12.5);
}

// 主消息处理函数
self.onmessage = function (e) {
    const { 
        type, 
        waveData, 
        height, 
        maxPoints, 
        gridSize, 
        padding, 
        batchSize,
        offscreenCanvas,
        width
    } = e.data;
    
    let result = { type };
    
    try {
        switch (type) {
            case 'processWaveData':
                if (waveData && height) {
                    // 预分配数组大小（每个点需要2个坐标值）
                    const dataLength = waveData.length;
                    const points = new Array(dataLength * 2);
                    
                    // 高效循环计算（使用索引赋值替代push）
                    for (let i = 0; i < dataLength; i++) {
                        const y = mapValueToY(waveData[i], height);
                        points[i * 2] = i;       // x坐标
                        points[i * 2 + 1] = y;   // y坐标
                    }
                    
                    result.points = points;
                }
                break;
                
            case 'calculateGrid':
                if (maxPoints && height && gridSize && padding) {
                    result.gridLines = calculateGridLines(maxPoints, height, gridSize, padding);
                }
                break;
                
            case 'processBatchData':
                if (waveData && batchSize) {
                    result.processedData = processBatchData(waveData, batchSize);
                }
                break;
                
            case 'calculateStatistics':
                if (waveData) {
                    result.statistics = calculateStatistics(waveData);
                }
                break;
                
            case 'renderWaveform':
                if (offscreenCanvas && waveData && width && height) {
                    const imageBitmap = drawWaveformOnOffscreenCanvas(
                        offscreenCanvas,
                        waveData,
                        width,
                        height,
                        gridSize || 1
                    );
                    
                    // 使用 transferToImageBitmap 避免数据拷贝
                    (self as any).postMessage({
                        type: 'renderWaveform',
                        imageBitmap
                    }, [imageBitmap]);
                    return; // 直接返回，避免重复发送
                }
                break;
                
            default:
                throw new Error(`Unknown message type: ${type}`);
        }
        
        // 向主线程返回计算结果
        self.postMessage(result);
        
    } catch (error) {
        // 错误处理
        self.postMessage({
            type: 'error',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};