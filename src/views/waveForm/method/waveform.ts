import Konva from 'konva'
import { performanceMonitor } from './performance-monitor'

export class WaveformDrawer {
    // private line: Konva.Line
    // private line2: Konva.Line
    private grid: Konva.Group
    private stage: Konva.Stage
    private staticLayer: Konva.Layer
    private dynamicLayer: Konva.Layer
    private height: number
    private waveData: number[] = []
    private maxPoints: number = 400
    private animationId: number = 0
    private resizeObserver: ResizeObserver | null = null
    private parent: HTMLElement
    private container: HTMLDivElement
    private padding = {
        y: 4,
        x: 0,
    }
    // 心电相关
    private AMPL: number = 800 // amplitude/1mV 振幅/mV
    private BEN: number = 2048 // benchmark 基准
    private FREQ: number = 500
    // 走纸相关
    private currentIndex: number = 0
    private currentX: number = 0
    // 新增性能控制相关
    private offscreenCanvas: HTMLCanvasElement; // 离屏Canvas
    private offscreenCtx: CanvasRenderingContext2D;
    // OffscreenCanvas 相关
    private offscreenCanvasForWorker: OffscreenCanvas | null = null;
    // Web Worker 相关
    private worker: Worker | null = null;
    private workerReady: boolean = false;
    private pendingCalculations: Array<{ type: string; data: any; resolve: Function; reject: Function }> = [];
    // 渲染相关
    private useOffscreenCanvas: boolean = false;
    // 性能监控
    private performanceEnabled: boolean = true;

    public sseEcg(val: number) {
        let amplitude = ((val > 4096 ? 4096 : (val < 0 ? 0 : val)) - this.BEN) / this.AMPL; // 计算振幅
        // console.log('ecg', val, amplitude)
        return amplitude * 50; // 返回振幅
    };

    constructor(container: HTMLDivElement, height: number) {
        this.height = height // 保存画布高度
        this.parent = container.parentElement as HTMLElement // 获取父元素（用于计算宽度）
        this.container = container // 保存画布容器DOM元素
        this.stage = new Konva.Stage({
            container: this.container,
            width: this.parent.clientWidth,
            height: this.height,
            renderer: 'webgl', // 强制使用 WebGL
            webglBufferSize: 2048 // 根据设备调整纹理大小
        })
        // this.layer = new Konva.Layer()
        // this.stage.add(this.layer)
        // 创建静态图层（背景、固定元素）
        this.staticLayer = new Konva.Layer();
        this.stage.add(this.staticLayer);

        // 创建动态图层（动画元素）
        this.dynamicLayer = new Konva.Layer();
        this.stage.add(this.dynamicLayer);

        this.grid = new Konva.Group()
        this.staticLayer.add(this.grid)
        this.init()
        // 初始化离屏Canvas
        this.offscreenCanvas = document.createElement('canvas');
        this.offscreenCtx = this.offscreenCanvas.getContext('2d');

        // 初始化 OffscreenCanvas 和 Web Worker
        this.initOffscreenCanvas();
        this.initWorker();
        
        // 启动性能监控
        if (this.performanceEnabled) {
            performanceMonitor.startMonitoring();
        }
    }

    private init() {
        const resize = () => {
            const width = this.parent.clientWidth - 24
            this.stage.width(width)
            this.stage.height(this.height)
            this.container.style.height = this.height + 'px'
            this.maxPoints = width
            while (this.waveData.length > this.maxPoints) this.waveData.shift()
            
            // 重新初始化 OffscreenCanvas
            if (this.useOffscreenCanvas) {
                this.offscreenCanvasForWorker = new OffscreenCanvas(this.maxPoints, this.height);
            }
            // this.drawGrids()
        }
        resize()
        this.resizeObserver = new ResizeObserver(resize)
        this.resizeObserver.observe(this.parent)
        // this.drawGrids()
        this.drawPoints()
    }

    private initOffscreenCanvas() {
        try {
            // 检查浏览器是否支持 OffscreenCanvas
            if (typeof OffscreenCanvas !== 'undefined') {
                this.offscreenCanvasForWorker = new OffscreenCanvas(this.maxPoints, this.height);
                this.useOffscreenCanvas = true;
                console.log('OffscreenCanvas initialized successfully');
            } else {
                console.warn('OffscreenCanvas not supported, falling back to main thread rendering');
                this.useOffscreenCanvas = false;
            }
        } catch (error) {
            console.error('Failed to initialize OffscreenCanvas:', error);
            this.useOffscreenCanvas = false;
        }
    }

    private initWorker() {
        try {
            // 创建 Web Worker - 使用相对路径
            this.worker = new Worker('./waveform-worker.ts');

            // 设置消息处理器
            this.worker.onmessage = (e) => {
                this.handleWorkerMessage(e);
            };

            // 设置错误处理器
            this.worker.onerror = (error) => {
                console.error('Web Worker error:', error);
                this.workerReady = false;
            };

            this.workerReady = true;
            console.log('Web Worker initialized successfully');

        } catch (error) {
            console.error('Failed to initialize Web Worker:', error);
            this.workerReady = false;
        }
    }

    private handleWorkerMessage(e: MessageEvent) {
        const { type, points, gridLines, statistics, processedData, imageBitmap, error } = e.data;

        if (error) {
            console.error('Worker error:', error);
            return;
        }

        // 处理 OffscreenCanvas 渲染结果
        if (type === 'renderWaveform' && imageBitmap) {
            this.handleOffscreenCanvasResult(imageBitmap);
            return;
        }

        // 处理待处理的计算请求
        const pendingIndex = this.pendingCalculations.findIndex(p => p.type === type);
        if (pendingIndex !== -1) {
            const pending = this.pendingCalculations.splice(pendingIndex, 1)[0];
            pending.resolve({ type, points, gridLines, statistics, processedData });
        }
    }

    private handleOffscreenCanvasResult(imageBitmap: ImageBitmap) {
        // 获取主画布
        const canvas = this.container.querySelector('canvas') as HTMLCanvasElement;
        const ctx = canvas.getContext('2d')!;
        
        // 清除画布并绘制 ImageBitmap
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(imageBitmap, 0, 0);
        
        // 关闭 ImageBitmap 以释放内存
        imageBitmap.close();
    }

    private async sendToWorker(type: string, data: any): Promise<any> {
        if (!this.worker || !this.workerReady) {
            // 如果 Worker 不可用，回退到主线程计算
            return this.fallbackCalculation(type, data);
        }

        return new Promise((resolve, reject) => {
            // 添加待处理的计算请求
            this.pendingCalculations.push({ type, data, resolve, reject });

            // 发送消息到 Worker
            this.worker!.postMessage({ type, ...data });

            // 设置超时
            setTimeout(() => {
                const index = this.pendingCalculations.findIndex(p => p.type === type);
                if (index !== -1) {
                    this.pendingCalculations.splice(index, 1)[0].reject(new Error('Worker timeout'));
                }
            }, 5000);
        });
    }

    private fallbackCalculation(type: string, data: any): any {
        // 主线程回退计算
        switch (type) {
            case 'processWaveData':
                return this.processWaveDataInMainThread(data.waveData, data.height);
            case 'calculateGrid':
                return this.calculateGridInMainThread(data.maxPoints, data.height, data.gridSize, data.padding);
            case 'processBatchData':
                return this.processBatchDataInMainThread(data.waveData, data.batchSize);
            case 'calculateStatistics':
                return this.calculateStatisticsInMainThread(data.waveData);
            default:
                throw new Error(`Unknown calculation type: ${type}`);
        }
    }

    private processWaveDataInMainThread(waveData: number[], height: number): { points: number[] } {
        const dataLength = waveData.length;
        const points = new Array(dataLength * 2);

        for (let i = 0; i < dataLength; i++) {
            const y = this.mapValueToY(waveData[i]);
            points[i * 2] = i;
            points[i * 2 + 1] = y;
        }

        return { points };
    }

    private calculateGridInMainThread(maxPoints: number, height: number, gridSize: number, padding: { x: number; y: number }): { gridLines: Array<{ points: number[]; color: string }> } {
        const gridLines: Array<{ points: number[]; color: string }> = [];
        const baseGridSize = gridSize * 1.5;

        for (let x = 0; x <= maxPoints; x += baseGridSize) {
            gridLines.push({
                points: [x, 0, x, height],
                color: '#eee'
            });
        }

        for (let y = 0; y <= height; y += baseGridSize) {
            gridLines.push({
                points: [0, y, maxPoints, y],
                color: '#eee'
            });
        }

        return { gridLines };
    }

    private processBatchDataInMainThread(waveData: number[], batchSize: number): { processedData: number[] } {
        const processedData: number[] = [];

        for (let i = 0; i < waveData.length; i += batchSize) {
            const batch = waveData.slice(i, i + batchSize);
            const processedBatch = batch.map(val => this.sseEcg(val));
            processedData.push(...processedBatch);
        }

        return { processedData };
    }

    private calculateStatisticsInMainThread(waveData: number[]): { statistics: { maxValue: number; minValue: number; average: number; variance: number } } {
        if (waveData.length === 0) {
            return { statistics: { maxValue: 0, minValue: 0, average: 0, variance: 0 } };
        }

        const maxValue = Math.max(...waveData);
        const minValue = Math.min(...waveData);
        const sum = waveData.reduce((acc, val) => acc + val, 0);
        const average = sum / waveData.length;
        const variance = waveData.reduce((acc, val) => acc + Math.pow(val - average, 2), 0) / waveData.length;

        return { statistics: { maxValue, minValue, average, variance } };
    }

    public pushPoint(point: number) {
        // this.waveData.push(point + 75)
        this.waveData.push(point);
        if (this.waveData.length < this.maxPoints) {
            this.waveData.push(point);
        } else {
            // 达到上限时从头部开始替换
            // let whiteRelax = this.maxPoints - this.currentX;
            this.waveData.splice(this.currentX, 1, point);
            // 每完成一轮循环（currentX回到0）清理前10%数据
            if (this.currentX >= this.maxPoints * 0.9) {
                this.waveData = this.waveData.slice(-this.maxPoints * 0.9); // 保留90%最新数据
            }
        }
        if (this.currentX >= this.maxPoints) {
            this.currentX = 0;
            this.offscreenCtx.clearRect(0, 0, this.maxPoints, this.height); // 清理离屏缓存
        };
        this.currentX++;
    }

    // 使用 Web Worker 批量处理数据
    public async processBatchData(batchSize: number = 100): Promise<number[]> {
        try {
            const result = await this.sendToWorker('processBatchData', {
                waveData: this.waveData,
                batchSize
            });
            return result.processedData || [];
        } catch (error) {
            console.error('Error processing batch data:', error);
            return this.processBatchDataInMainThread(this.waveData, batchSize).processedData;
        }
    }

    // 使用 Web Worker 计算统计数据
    public async calculateStatistics(): Promise<{ maxValue: number; minValue: number; average: number; variance: number }> {
        try {
            const result = await this.sendToWorker('calculateStatistics', {
                waveData: this.waveData
            });
            return result.statistics || { maxValue: 0, minValue: 0, average: 0, variance: 0 };
        } catch (error) {
            console.error('Error calculating statistics:', error);
            return this.calculateStatisticsInMainThread(this.waveData).statistics;
        }
    }

    private mapValueToY(value: number): number {
        // return this.padding.y + (this.height - 2 * this.padding.y) * (1 - value / 127)
        return this.height / 2 - value; // 心电相关，放大显示
    }

    private drawGrids() {
        this.grid.destroyChildren();
        // 计算并显示实际最大最小值
        const maxValue = Math.max(...this.waveData);
        const minValue = Math.min(...this.waveData);


        this.grid.add(
            // 绘制X轴轴线（水平线）
            new Konva.Line({
                points: [0, this.height / 2, this.maxPoints, this.height / 2],
                // stroke: '#FF0',
                stroke: '#000',
                strokeWidth: 2,
            }),
            // 绘制Y轴轴线（垂直线）
            new Konva.Line({
                points: [0, 0, 0, this.height],
                // stroke: '#FF0',
                stroke: '#000',
                strokeWidth: 2,
            }),
            // 添加Y轴最大值标签
            // new Konva.Text({
            //     x: 5,
            //     y: this.height / 70 * 7,
            //     text: '2.56 mV',
            //     fontSize: 12,
            //     fill: '#000',
            // }),
            new Konva.Text({
                x: 5,
                y: this.height / 14 * 1,
                text: '2 mV',
                fontSize: 12,
                fill: '#000',
            }),
            new Konva.Text({
                x: 5,
                y: this.height / 14 * 5,
                text: '1 mV',
                fontSize: 12,
                fill: '#000',
            }),
            // 添加Y轴最小值标签
            new Konva.Text({
                x: 5,
                y: this.height / 14 * 9.5,
                text: '-1 mV',
                fontSize: 12,
                fill: '#000',
            }),
            new Konva.Text({
                x: 5,
                y: this.height / 14 * 12.5,
                text: '-2 mV',
                fontSize: 12,
                fill: '#000',
            }),
            // new Konva.Text({
            //     x: 5,
            //     y: this.height / 70 * 61,
            //     text: '-2.56 mV',
            //     fontSize: 12,
            //     fill: '#000',
            // })
        );

        this.drawGrid(5)
        this.drawGrid(25, '#ddd')
    }

    private drawGrid(gridSize: number, color = '#eee') {
        const width = this.maxPoints
        // 增加基础网格间距，减少网格线数量
        const baseGridSize = gridSize * 1.5; // 将基础网格间距扩大5倍
        for (let x = 0; x <= width; x += baseGridSize) {
            this.grid.add(
                new Konva.Line({
                    points: [x, 0, x, this.height],
                    stroke: color,
                    strokeWidth: 1,
                })
            )
        }
        // for (let y = this.padding.y; y <= this.height - this.padding.y; y += gridSize) {
        for (let y = 0; y <= this.height; y += baseGridSize) {
            this.grid.add(
                new Konva.Line({
                    points: [0, y, width, y],
                    stroke: color,
                    strokeWidth: 1,
                })
            )
        }
    }

    private async drawPoints() {
        const width = this.maxPoints;
        const GRID_SIZE = 1; // 每个小网格1px
        const startTime = performance.now();

        // 如果支持 OffscreenCanvas，使用 Worker 进行渲染
        if (this.useOffscreenCanvas && this.offscreenCanvasForWorker && this.workerReady) {
            try {
                // 发送 OffscreenCanvas 到 Worker 进行渲染
                this.worker!.postMessage({
                    type: 'renderWaveform',
                    offscreenCanvas: this.offscreenCanvasForWorker,
                    waveData: this.waveData,
                    width: width,
                    height: this.height,
                    gridSize: GRID_SIZE
                }, [this.offscreenCanvasForWorker]);
                
                // 重新创建 OffscreenCanvas 用于下次渲染
                this.offscreenCanvasForWorker = new OffscreenCanvas(width, this.height);
            } catch (error) {
                console.error('Error with OffscreenCanvas rendering:', error);
                this.useOffscreenCanvas = false;
                const canvas = this.container.querySelector('canvas') as HTMLCanvasElement;
                const ctx = canvas.getContext('2d')!;
                this.drawGrids();
                this.drawPointsFallback(ctx, GRID_SIZE);
            }
        } else {
            // 回退到传统渲染方式
            const canvas = this.container.querySelector('canvas') as HTMLCanvasElement;
            const ctx = canvas.getContext('2d')!;
            this.drawGrids();
            this.drawPointsFallback(ctx, GRID_SIZE);
        }

        // 记录性能数据
        if (this.performanceEnabled) {
            const renderTime = performance.now() - startTime;
            performanceMonitor.recordFrame(renderTime);
        }

        this.animationId = requestAnimationFrame(this.drawPoints.bind(this));
    }

    private drawPointsFallback(ctx: CanvasRenderingContext2D, GRID_SIZE: number) {
        // 主线程回退绘制方法
        ctx.beginPath();
        ctx.strokeStyle = '#e74c3c';
        ctx.lineWidth = 2;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';

        for (let i = 0; i < this.waveData.length; i++) {
            const y = this.mapValueToY(this.waveData[i]);
            const x = i * GRID_SIZE;
            if (i === 0) {
                ctx.moveTo(x, y - 0.1);
            } else {
                ctx.lineTo(x, y - 0.1);
            }
        }
        ctx.stroke();
    }

    public destroy() {
        cancelAnimationFrame(this.animationId)
        if (this.resizeObserver && this.parent) {
            this.resizeObserver.unobserve(this.parent)
        }

        // 清理 Web Worker
        if (this.worker) {
            this.worker.terminate()
            this.worker = null
            this.workerReady = false
        }

        // 清理 OffscreenCanvas
        if (this.offscreenCanvasForWorker) {
            this.offscreenCanvasForWorker = null
        }

        // 清理待处理的计算
        this.pendingCalculations.forEach(pending => {
            pending.reject(new Error('Worker destroyed'));
        })
        this.pendingCalculations = []

        this.stage.destroy()
        this.container.remove()
        this.waveData = [];
    }

    public clear() {
        this.waveData = [];
        this.currentX = 0;
        this.currentIndex = 0;
        this.staticLayer.destroyChildren();
        this.dynamicLayer.destroyChildren();
        this.drawGrids();
    }

    // 性能监控相关方法
    public getPerformanceMetrics() {
        return performanceMonitor.getMetrics();
    }

    public getPerformanceReport() {
        return performanceMonitor.generateReport();
    }

    public getPerformanceAdvice() {
        return performanceMonitor.getPerformanceAdvice();
    }

    public isPerformanceGood() {
        return performanceMonitor.isPerformanceGood();
    }

    public enablePerformanceMonitoring(enabled: boolean = true) {
        this.performanceEnabled = enabled;
        if (enabled) {
            performanceMonitor.startMonitoring();
        } else {
            performanceMonitor.reset();
        }
    }

    public isOffscreenCanvasSupported() {
        return this.useOffscreenCanvas;
    }

    public getRenderMode() {
        return this.useOffscreenCanvas ? 'OffscreenCanvas + Worker' : 'Main Thread';
    }
}
