<template>
    <div>
        <echart-item ref="echartHrvRef" :options="echartOption" style="height: 52vh" />
    </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive } from "vue";
import EchartItem from '@/components/echartItem.vue';
import waveFormData from './components/waveform_data';
import $bus from "@/utils/bus.ts"

const echartHrvRef = ref(null);

const echartOption = reactive({
    tooltip: {
        trigger: 'axis', // 触发类型，'axis'表示坐标轴触发
        triggerOn: 'mousemove', // 'mousemove': 鼠标移动时触发，'click': 点击时触发
        axisPointer: {   // 坐标轴指示器配置
            type: 'line'   // 指示器类型，'line'直线，'shadow'阴影，'cross'十字准星
        },
        backgroundColor: 'rgba(50, 50, 50, 0.7)', // 背景色
        borderColor: '#333',                      // 边框颜色
        borderWidth: 1,                           // 边框宽度
        textStyle: {                              // 文本样式
            color: '#fff',
            fontSize: 14
        },
        padding: [10, 15]                         // 内边距
    },
    // 数据平移组件
    // dataZoom: [
    //     {
    //         type: 'slider', // 滑动条型 dataZoom 组件
    //         xAxisIndex: 0,   // 控制哪个 x 轴
    //         start: 30, // 初始起始位置（百分比）
    //         end: 70, // 初始结束位置（百分比）
    //         zoomOnMouseWheel: true, // 允许鼠标滚轮缩放
    //         moveOnMouseMove: true,   // 允许鼠标拖拽平移
    //         filterMode: 'filter', // 'filter': 数据被过滤，'weakFilter': 数据被过滤但保留部分，'empty': 数据被清空
    //     }
    // ],
    grid: {
        show: true, // 显示网格
        backgroundColor: 'rgba(250, 250, 250, 0.8)', // 网格背景颜色
        borderColor: '#ccc', // 网格线颜色
        borderWidth: 1 // 网格线宽度
    },
    xAxis: {
        type: 'category',
        // data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25],
        data: [],
        axisLine: {
            lineStyle: {
                color: '#999'
            }
        },
        splitLine: {
            show: true,
            lineStyle: {
                type: 'dotted',
                color: 'rgba(0,0,0)'
            }
        }
    },
    yAxis: {
        type: 'value',
        min: -30,    // 设置最小值
        max: 30,   // 设置最大值
        axisLine: {
            lineStyle: {
                color: '#999'
            }
        },
        splitLine: {
            lineStyle: {
                type: 'dotted',
                color: 'rgba(0,0,0)'
            }
        }
    },
    series: [
        {
            // data: [15, 23, 22, 21, 11, 14, 25, 15, 23, -22, 21, 20, -14, 25, 15, 23, 22, 21, -6, 14, 5, 15, 23, 22, 21, 11, 14, 25, 15, 23, -22, 21, 20, -14, 25, 15, 23, 22, 21, -6, 14, 5, 15, 23, 22, 21, 11, 14, 25, 15, 23, -22, 21, 20, -14, 25, 15, 23, 22, 21, -6, 14, 5],
            data: [],
            type: 'line',
            lineStyle: {
                width: 3,
                color: '#00a6b0'
            },
            itemStyle: {
                color: '#00a6b0'
            }
        }
    ]
});

// amplitude/1mV 振幅/mV
const AMPL = 800;
// benchmark 基准
const BEN = 2048;
const FREQ = 500;
const xAxisData = ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''];
const yAxisData = [];
// 为参数val指定类型为number
const sseEcg = (val: number) => {
    let amplitude = ((val > 4096 ? 4096 : val) - BEN) / AMPL; // 计算振幅
    return amplitude * 10; // 返回振幅
};

const timer = ref(null);
const initChart = () => {
    const startTime = Number(waveFormData[0].timestamp); // 起始时间
    let counter = 0;
    timer.value = setInterval(() => {
        if (counter < 20) {
            // xAxisData.pop(); // 删除最后一个元素
            xAxisData[counter] = (Number(waveFormData[counter].timestamp) - startTime); // 计算相对时间
        } else {
            xAxisData.shift(); // 删除第一个元素
            xAxisData.push(Number(waveFormData[counter].timestamp) - startTime); // 计算相对时间
        }

        if (yAxisData.length < 20) {
            yAxisData.push(sseEcg(waveFormData[counter].ecgLeadI * (counter % 2 == 0 ? counter * 15 : 1)) || 0); // 计算相对时间  
        } else {
            yAxisData.shift(); // 删除第一个元素
            yAxisData.push(sseEcg(waveFormData[counter].ecgLeadI * (counter % 2 == 0 ? counter * 15 : 1)) || 0); // 计算相对时间
        }
        echartOption.xAxis.data = xAxisData; // 更新x轴数据
        echartOption.series[0].data = yAxisData; // 更新y轴数据 
        console.log(counter, waveFormData[counter], xAxisData, yAxisData); // 打印每个时间
        $bus.emit('waveForm', echartOption);

        if (++counter >= waveFormData.length) clearInterval(timer.value);
    }, 1000);
}

initChart();

watch(() => echartOption, (newVal) => {
    if (newVal) {
        // echartOption.xAxis.data = newVal; // 更新x轴数据
        echartHrvRef.value?.updateOptions(newVal)
    }
});

</script>