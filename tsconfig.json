{
  "compilerOptions": {
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"],
      "#/*": ["src/types/*"]
    },
    // 历史配置
    "target": "ESNext",
    "module": "commonjs",
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "noImplicitAny": true,
    "sourceMap": true,
    "outDir": "dist",
    "moduleResolution": "node",
    "resolveJsonModule": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "build/**/*.ts",
    "build/**/*.d.ts",
    "vite.config.ts"
, "vite-env.d.ts"  ],
  "exclude": ["node_modules", "dist", "**/*.js"]
}
