import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import Pages from 'vite-plugin-pages'
import Layouts from 'vite-plugin-vue-layouts'
import path from 'path'; // 导入 Node.js 的 path 模块

const PACKAGE_ROOT = './'

// https://vitejs.dev/config
export default defineConfig({
	resolve: {
		alias: {
			'@': path.resolve(__dirname, 'src'), // 设置 @ 别名指向 src 目录
		},
	},
	plugins: [
		vue(),
		Pages({
			// 需要生成路由的文件目录，默认就是识别src下面的pages文件
			pagesDir: PACKAGE_ROOT + 'src' + '/views',
			//dirs: "src/pages",
			// 排除在外的目录，即不将所有 components 目录下的 .vue 文件生成路由
			exclude: ['**/components/*.vue'],
		}),
		Layouts({
			layoutsDirs: PACKAGE_ROOT + 'src' + '/layouts',
		}),
	],
})